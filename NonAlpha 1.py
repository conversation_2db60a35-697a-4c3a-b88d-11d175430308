import pandas as pd
import os

input_file = 'input.txt'
output_file = 'output.txt'

if not os.path.isfile(input_file):
    print(f"File '{input_file}' not found in the current directory.")
    exit(1)
print("Reading input file...")
df = pd.read_csv(input_file, sep='\t', dtype=str, on_bad_lines='skip', encoding='cp1252').fillna('')


def has_nonalpha(cell):
    return any(not part.replace(' ', '').isalpha() for part in cell.split('|'))


def is_short(cell):
    return len(cell) < 4


df['Has_NonAlpha'] = df['NOT_FOUND_LINES'].apply(has_nonalpha)
df['Is_Short'] = df['NOT_FOUND_LINES'].apply(is_short)


df.to_csv(output_file, sep='\t', index=False)

print(f"Processing complete. Output saved to '{output_file}'.")

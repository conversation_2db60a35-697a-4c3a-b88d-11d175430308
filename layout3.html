<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Ocean Waves - Layout 3</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<link rel="stylesheet" href="shared-styles.css">
	<link rel="stylesheet" href="layout3.css">
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-water"></i>
				<span>Ocean Waves</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-compass"></i>
					<span>Navigation</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-fish"></i>
					<span>Marine Life</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-ship"></i>
					<span>Vessels</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Ocean Status</h2>
					<div class="card animate-fade-in">
						<h3>Wave Height</h3>
						<p>2.5 meters average</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Temperature</h3>
						<p>22°C at surface</p>
					</div>
				</section>

				<section class="column">
					<h2>Marine Activity</h2>
					<div class="card animate-fade-in">
						<h3>Fish Schools</h3>
						<p>High activity detected</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Coral Health</h3>
						<p>Excellent condition</p>
					</div>
				</section>

				<section class="column">
					<h2>Navigation</h2>
					<div class="card animate-fade-in">
						<h3>Current Speed</h3>
						<p>3 knots northward</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Weather</h3>
						<p>Clear skies ahead</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
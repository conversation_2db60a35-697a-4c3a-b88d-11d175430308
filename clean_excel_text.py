import openpyxl
import re
import sys

def clean_text(text):
    """Apply regex cleaning patterns to text"""
    if not isinstance(text, str):
        return text
    
    # Apply the regex patterns
    x = re.sub(r'[^A-Za-z\s]', "", text)
    x = re.sub(r'\s[a-z]\s', ' ', x)
    x = re.sub(r'\sX\s', ' ', x)
    x = re.sub(r'\s+', ' ', x).strip()
    
    return x

def process_excel_file(input_file, output_file, column_to_clean):
    """Process Excel file, clean specified column, and write results"""
    try:
        # Load the workbook and select the active worksheet
        wb = openpyxl.load_workbook(input_file)
        ws = wb.active
        
        # Find the column index from the letter
        col_idx = openpyxl.utils.column_index_from_string(column_to_clean)
        
        # Get the maximum row count
        max_row = ws.max_row
        
        # Find the next available column to write results
        next_col_idx = ws.max_column + 1
        next_col_letter = openpyxl.utils.get_column_letter(next_col_idx)
        
        # Add header for the new column
        ws[f"{next_col_letter}1"] = f"Cleaned {column_to_clean}"
        
        # Process each row
        for row in range(2, max_row + 1):  # Start from row 2 (assuming row 1 is header)
            cell_value = ws[f"{column_to_clean}{row}"].value
            
            if cell_value:
                cleaned_value = clean_text(cell_value)
                ws[f"{next_col_letter}{row}"] = cleaned_value
        
        # Save the workbook to a new file
        wb.save(output_file)
        print(f"Processing complete. Results saved to {output_file}")
        
    except Exception as e:
        print(f"Error processing Excel file: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python clean_excel_text.py input_file.xlsx column_letter [output_file.xlsx]")
        print("Example: python clean_excel_text.py data.xlsx E")
        sys.exit(1)
    
    input_file = sys.argv[1]
    column_to_clean = sys.argv[2].upper()  # Convert to uppercase
    
    # Use default output filename if not provided
    if len(sys.argv) >= 4:
        output_file = sys.argv[3]
    else:
        # Create output filename based on input filename
        output_file = input_file.rsplit('.', 1)[0] + "_cleaned." + input_file.rsplit('.', 1)[1]
    
    process_excel_file(input_file, output_file, column_to_clean)

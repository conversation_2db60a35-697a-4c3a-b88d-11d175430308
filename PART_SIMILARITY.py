# X1 = 'J02023A0050'
# X2 = 'J02023S0050'
# X1 = list(X1)
# X2 = list(X2)
#
# acc = 100
# factor = len(X1)/100
# for i,char in enumerate(X1):
#     try:
#         if X1.index(X1[i]) == X2.index(X1[i]):
#             X1.pop(i)
#             X2.pop(i)
#         else:
#             acc -= factor
#     except Exception as E:
#         print("here")
#         acc -= factor
#     print(X1)
#     print(X2)
#
# print(acc)
import re

import fitz
from thefuzz import fuzz
from thefuzz import process


def extract_all_words(link):
    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))
    all_words = []
    for page_index, page in enumerate(doc_1):
        for ext_word in page.get_text("words"):
            all_words.append(ext_word)
    return all_words


def clean_word(word):
    # Define the pattern to match prefixes and suffixes
    pattern = r'^(no\.\.:|[#:=.,“”"▲]+)|(=|,|:|\.|")$'

    # Use re.sub to remove the prefixes and suffixes
    cleaned_word = re.sub(pattern, '', word)

    return cleaned_word


def find_matched_parts(input_row):
    link = input_row.split("\t")[0]
    part = input_row.split("\t")[-1].strip()
    try:
        matched_parts = []
        for word in extract_all_words(link):
            ratio = fuzz.ratio(part, word[4])
            if ratio >= 60:
                new_word = word[4].removesuffix(")")
                new_word = new_word.removeprefix("(")
                new_word = new_word.removesuffix(":")
                new_word = new_word.removesuffix(".")
                new_word = new_word.removesuffix(",")
                new_word = new_word.removeprefix("=")
                new_word = new_word.removeprefix("#")
                new_word = new_word.removeprefix("no..:")
                if matched_parts not in matched_parts:
                    matched_parts.append(new_word)

        matched_parts = list(set(matched_parts))
        return link + "\t" + part + "\t" + "|".join(matched_parts)

    except Exception as E:
        return link + "\t" + part + "\t" + str(E)


if __name__ == '__main__':

    with open("input.txt", "r") as input_file:
        links_list = input_file.readlines()

    with open("output_2.txt", "w") as output_file:
        output_file.write('document\tpart_list')
        output_file.write('\n')

    for count, result in enumerate(map(find_matched_parts, links_list), start=1):
        with open("output_2.txt", "a", encoding='utf8') as output_file:
            output_file.write(result)
            output_file.write('\n')
        print(f"done_{count}".upper())

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import csv
import json
import re

class MovieScraper:
    def __init__(self, headless=False):
        """Initialize the movie scraper with Chrome driver."""
        self.setup_driver(headless)
        self.movies_data = []

    def setup_driver(self, headless=False):
        """Setup Chrome driver with options."""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Try different ways to initialize the driver
        try:
            service = Service(executable_path='chromedriver.exe')
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except:
            try:
                # Try without specifying path (if chromedriver is in PATH)
                self.driver = webdriver.Chrome(options=chrome_options)
            except:
                print("Error: ChromeDriver not found. Please ensure chromedriver.exe is in the same directory or in PATH.")
                raise

        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 10)

    def get_movie_details(self, movie_element):
        """Extract movie details from a movie element."""
        movie_data = {
            'name': 'N/A',
            'year': 'N/A',
            'length': 'N/A',
            'genre': 'N/A',
            'rating': 'N/A',
            'country': 'N/A'
        }

        try:
            # Get movie name
            try:
                name_element = movie_element.find_element(By.CSS_SELECTOR, ".film-name a")
                movie_data['name'] = name_element.get_attribute('title') or name_element.text.strip()
            except:
                try:
                    name_element = movie_element.find_element(By.CSS_SELECTOR, ".film-detail .film-name")
                    movie_data['name'] = name_element.text.strip()
                except:
                    pass

            # Get year
            try:
                year_element = movie_element.find_element(By.CSS_SELECTOR, ".fdi-item:first-child")
                year_text = year_element.text.strip()
                year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                if year_match:
                    movie_data['year'] = year_match.group()
            except:
                pass

            # Get length/duration
            try:
                duration_elements = movie_element.find_elements(By.CSS_SELECTOR, ".fdi-item")
                for element in duration_elements:
                    text = element.text.strip()
                    if 'min' in text.lower() or 'h' in text.lower():
                        movie_data['length'] = text
                        break
            except:
                pass

            # Get genre
            try:
                genre_elements = movie_element.find_elements(By.CSS_SELECTOR, ".fd-infor .fdi-item")
                for element in genre_elements:
                    text = element.text.strip()
                    # Common genres to identify
                    genres = ['Action', 'Comedy', 'Drama', 'Horror', 'Thriller', 'Romance', 'Sci-Fi', 'Fantasy', 'Adventure', 'Crime']
                    for genre in genres:
                        if genre.lower() in text.lower():
                            movie_data['genre'] = text
                            break
                    if movie_data['genre'] != 'N/A':
                        break
            except:
                pass

            # Get rating
            try:
                rating_element = movie_element.find_element(By.CSS_SELECTOR, ".film-rating .tick-rate")
                movie_data['rating'] = rating_element.text.strip()
            except:
                try:
                    rating_element = movie_element.find_element(By.CSS_SELECTOR, ".imdb-rate")
                    movie_data['rating'] = rating_element.text.strip()
                except:
                    pass

            # Get country
            try:
                country_elements = movie_element.find_elements(By.CSS_SELECTOR, ".fd-infor .fdi-item")
                for element in country_elements:
                    text = element.text.strip()
                    # Common countries
                    countries = ['USA', 'UK', 'Canada', 'France', 'Germany', 'Japan', 'Korea', 'China', 'India', 'Australia']
                    for country in countries:
                        if country.lower() in text.lower():
                            movie_data['country'] = text
                            break
                    if movie_data['country'] != 'N/A':
                        break
            except:
                pass

        except Exception as e:
            print(f"Error extracting movie details: {e}")

        return movie_data

    def scrape_movies_page(self, url):
        """Scrape movies from a single page."""
        print(f"Scraping: {url}")

        try:
            self.driver.get(url)
            time.sleep(3)  # Wait for page to load

            # Wait for movie elements to load
            try:
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".flw-item")))
            except:
                print("No movies found on this page")
                return []

            # Find all movie elements
            movie_elements = self.driver.find_elements(By.CSS_SELECTOR, ".flw-item")
            print(f"Found {len(movie_elements)} movies on this page")

            page_movies = []
            for i, movie_element in enumerate(movie_elements, 1):
                print(f"Processing movie {i}/{len(movie_elements)}")
                movie_data = self.get_movie_details(movie_element)
                if movie_data['name'] != 'N/A':
                    page_movies.append(movie_data)
                    print(f"  ✓ {movie_data['name']} ({movie_data['year']})")
                else:
                    print(f"  ✗ Could not extract movie name")

            return page_movies

        except Exception as e:
            print(f"Error scraping page {url}: {e}")
            return []

    def scrape_multiple_pages(self, base_url, max_pages=5):
        """Scrape multiple pages of movies."""
        print(f"Starting to scrape {max_pages} pages from {base_url}")

        for page in range(1, max_pages + 1):
            if page == 1:
                url = base_url
            else:
                url = f"{base_url}?page={page}"

            page_movies = self.scrape_movies_page(url)
            self.movies_data.extend(page_movies)

            print(f"Page {page} complete. Total movies collected: {len(self.movies_data)}")

            # Small delay between pages
            time.sleep(2)

    def save_to_csv(self, filename="movies_data.csv"):
        """Save scraped data to CSV file."""
        if not self.movies_data:
            print("No data to save")
            return

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['name', 'year', 'length', 'genre', 'rating', 'country']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for movie in self.movies_data:
                writer.writerow(movie)

        print(f"✓ Data saved to {filename}")

    def save_to_json(self, filename="movies_data.json"):
        """Save scraped data to JSON file."""
        if not self.movies_data:
            print("No data to save")
            return

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.movies_data, jsonfile, indent=2, ensure_ascii=False)

        print(f"✓ Data saved to {filename}")

    def print_summary(self):
        """Print summary of scraped data."""
        if not self.movies_data:
            print("No movies scraped")
            return

        print(f"\n📊 SCRAPING SUMMARY")
        print("=" * 50)
        print(f"Total movies scraped: {len(self.movies_data)}")

        # Show first 5 movies as examples
        print(f"\nFirst 5 movies:")
        for i, movie in enumerate(self.movies_data[:5], 1):
            print(f"{i}. {movie['name']} ({movie['year']}) - {movie['genre']} - {movie['rating']}")

    def close(self):
        """Close the browser driver."""
        if hasattr(self, 'driver'):
            self.driver.quit()

def main():
    """Main function to run the movie scraper."""
    print("🎬 Movie Scraper for hdtodayz.to")
    print("=" * 50)

    # Initialize scraper
    scraper = MovieScraper(headless=False)  # Set to True to run without browser window

    try:
        # Scrape movies
        base_url = "https://hdtodayz.to/movie"
        scraper.scrape_multiple_pages(base_url, max_pages=3)  # Adjust number of pages as needed

        # Print summary
        scraper.print_summary()

        # Save data
        scraper.save_to_csv("output.csv")
        scraper.save_to_json("output.json")

    except Exception as e:
        print(f"Error during scraping: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()



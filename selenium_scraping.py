from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

service = Service(executable_path='chromedriver.exe')
driver = webdriver.Chrome(service=service)
driver.get('https://books.toscrape.com/index.html')



while True:
    
    try:
        books_section = driver.find_element("xpath", '//*[@id="default"]/div/div/div/div/section/div[2]/ol')
        books_list = books_section.find_elements(By.TAG_NAME, 'li')

        for book in books_list:
            print(book.find_element("xpath", 'article/h3/a').get_attribute('title'))
            print(book.find_element(By.CLASS_NAME, 'price_color').text)
            print(book.find_element(By.CLASS_NAME, "instock").text)

        next_page_button = driver.find_element(By.CLASS_NAME, "next")
        next_page_url = next_page_button.find_element(By.TAG_NAME, 'a').get_attribute('href')

    except:
        break

    driver.get(next_page_url)

driver.quit()
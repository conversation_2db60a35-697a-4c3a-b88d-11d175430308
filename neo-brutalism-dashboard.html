<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Neo-Brutalism Dashboard</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<style>
		:root {
			--primary: #ff4d00;
			--secondary: #000000;
			--accent: #00ff00;
			--bg: #ffffff;
			--card-bg: #f0f0f0;
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			background: var(--bg);
			font-family: Helvetica, Arial, sans-serif;
			color: var(--secondary);
		}

		.container {
			display: flex;
			min-height: 100vh;
		}

		/* Sidebar */
		.sidebar {
			width: 280px;
			background: var(--card-bg);
			border: 3px solid var(--secondary);
			box-shadow: 8px 8px 0 var(--secondary);
			padding: 20px;
			transition: all 0.3s ease;
			position: relative;
		}

		.sidebar.collapsed {
			width: 80px;
		}

		.sidebar-toggle {
			position: absolute;
			right: -15px;
			top: 20px;
			width: 30px;
			height: 30px;
			background: var(--primary);
			border: 2px solid var(--secondary);
			color: var(--secondary);
			cursor: pointer;
			box-shadow: 3px 3px 0 var(--secondary);
			transform: rotate(-2deg);
		}

		.logo {
			display: flex;
			align-items: center;
			gap: 12px;
			margin-bottom: 40px;
			transform: rotate(-2deg);
		}

		.logo i {
			font-size: 24px;
			color: var(--primary);
		}

		.nav-item {
			display: flex;
			align-items: center;
			gap: 12px;
			padding: 12px;
			margin: 8px 0;
			text-decoration: none;
			color: var(--secondary);
			border: 2px solid transparent;
			transform: rotate(-1deg);
		}

		.nav-item:hover, .nav-item.active {
			background: var(--primary);
			border: 2px solid var(--secondary);
			box-shadow: 4px 4px 0 var(--secondary);
		}

		.sidebar.collapsed .nav-item span {
			display: none;
		}

		/* Main Content */
		.main-content {
			flex: 1;
			padding: 32px;
			margin-left: 20px;
			transition: margin-left 0.3s ease;
		}

		.main-content.expanded {
			margin-left: 100px;
		}

		.columns {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 32px;
			margin-top: 32px;
		}

		.column h2 {
			font-size: 24px;
			margin-bottom: 20px;
			transform: rotate(-2deg);
			display: inline-block;
			background: var(--primary);
			padding: 8px 16px;
			border: 2px solid var(--secondary);
			box-shadow: 4px 4px 0 var(--secondary);
		}

		.card {
			background: var(--card-bg);
			border: 3px solid var(--secondary);
			box-shadow: 8px 8px 0 var(--secondary);
			padding: 24px;
			margin-bottom: 24px;
			transform: rotate(1deg);
			transition: all 0.2s ease;
		}

		.card:hover {
			transform: rotate(-1deg) translateY(-5px);
			box-shadow: 10px 10px 0 var(--secondary);
		}

		.card h3 {
			color: var(--primary);
			margin-bottom: 12px;
			text-transform: uppercase;
		}

		.card p {
			font-family: monospace;
			font-size: 18px;
			margin-top: 8px;
		}

		.stat-value {
			font-size: 32px;
			font-weight: bold;
			color: var(--secondary);
			font-family: monospace;
		}

		@media (max-width: 1024px) {
			.columns {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		@media (max-width: 768px) {
			.columns {
				grid-template-columns: 1fr;
			}
		}
	</style>
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-cube"></i>
				<span>BRUTAL UI</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-chart-bar"></i>
					<span>Dashboard</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-cog"></i>
					<span>Settings</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-user"></i>
					<span>Profile</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Performance</h2>
					<div class="card">
						<h3>CPU Usage</h3>
						<div class="stat-value">87%</div>
						<p>Current Load</p>
					</div>
					<div class="card">
						<h3>Memory</h3>
						<div class="stat-value">16GB</div>
						<p>Available RAM</p>
					</div>
				</section>

				<section class="column">
					<h2>Activity</h2>
					<div class="card">
						<h3>Users</h3>
						<div class="stat-value">2,451</div>
						<p>Active Now</p>
					</div>
					<div class="card">
						<h3>Requests</h3>
						<div class="stat-value">894</div>
						<p>Per Minute</p>
					</div>
				</section>

				<section class="column">
					<h2>System</h2>
					<div class="card">
						<h3>Uptime</h3>
						<div class="stat-value">99.9%</div>
						<p>Last 30 Days</p>
					</div>
					<div class="card">
						<h3>Errors</h3>
						<div class="stat-value">0</div>
						<p>No Issues Found</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Purple Waves - Layout 1</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<link rel="stylesheet" href="shared-styles.css">
	<link rel="stylesheet" href="layout1.css">
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-wave-square"></i>
				<span>Purple Waves</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-home"></i>
					<span>Dashboard</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-chart-bar"></i>
					<span>Analytics</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-cog"></i>
					<span>Settings</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Recent Activity</h2>
					<div class="card animate-fade-in">
						<h3>Project Update</h3>
						<p>Latest changes to the dashboard</p>
					</div>
					<div class="card animate-fade-in">
						<h3>New Features</h3>
						<p>Recently added functionality</p>
					</div>
				</section>

				<section class="column">
					<h2>Statistics</h2>
					<div class="card animate-fade-in">
						<h3>User Growth</h3>
						<p>15% increase this month</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Performance</h3>
						<p>Server response time improved</p>
					</div>
				</section>

				<section class="column">
					<h2>Updates</h2>
					<div class="card animate-fade-in">
						<h3>System Status</h3>
						<p>All systems operational</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Maintenance</h3>
						<p>Scheduled for next week</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
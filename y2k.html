<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Y2K Style</title>
	<style>
		body {
			margin: 0;
			padding: 0;
			background: linear-gradient(45deg, #ff00ff, #00ffff);
			font-family: "Comic Sans MS", cursive;
			color: #ffffff;
			text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
		}

		.container {
			max-width: 800px;
			margin: 40px auto;
			padding: 20px;
		}

		.header {
			text-align: center;
			margin-bottom: 40px;
		}

		.header h1 {
			font-size: 48px;
			background: linear-gradient(to right, #ff00ff, #00ffff);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			text-shadow: none;
		}

		.card {
			background: rgba(255, 255, 255, 0.2);
			backdrop-filter: blur(10px);
			border: 3px solid #ffffff;
			border-radius: 20px;
			padding: 20px;
			margin-bottom: 20px;
			animation: float 3s ease-in-out infinite;
		}

		.button {
			background: linear-gradient(45deg, #ff00ff, #00ffff);
			border: none;
			border-radius: 25px;
			padding: 10px 30px;
			color: white;
			font-size: 18px;
			cursor: pointer;
			margin: 10px;
			box-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
		}

		.sticker {
			position: fixed;
			width: 100px;
			height: 100px;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="%23ff00ff"/><text x="50" y="50" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="20">Y2K</text></svg>');
			animation: spin 10s linear infinite;
		}

		.sticker:nth-child(1) { top: 20px; left: 20px; }
		.sticker:nth-child(2) { top: 20px; right: 20px; }
		.sticker:nth-child(3) { bottom: 20px; left: 20px; }
		.sticker:nth-child(4) { bottom: 20px; right: 20px; }

		@keyframes float {
			0%, 100% { transform: translateY(0); }
			50% { transform: translateY(-10px); }
		}

		@keyframes spin {
			from { transform: rotate(0deg); }
			to { transform: rotate(360deg); }
		}

		.sparkle {
			position: absolute;
			width: 4px;
			height: 4px;
			background: white;
			border-radius: 50%;
			animation: sparkle 1.5s linear infinite;
		}

		@keyframes sparkle {
			0% { transform: scale(0); opacity: 0; }
			50% { transform: scale(1); opacity: 1; }
			100% { transform: scale(0); opacity: 0; }
		}
	</style>
</head>
<body>
	<div class="sticker"></div>
	<div class="sticker"></div>
	<div class="sticker"></div>
	<div class="sticker"></div>

	<div class="container">
		<div class="header">
			<h1>Y2K Design</h1>
			<p>✨ Welcome to the Future ✨</p>
		</div>

		<div class="card">
			<h2>Futuristic Features</h2>
			<p>Experience the digital revolution with our cutting-edge design!</p>
			<button class="button">Click Me!</button>
		</div>

		<div class="card">
			<h2>Cyber Zone</h2>
			<p>Enter the matrix of unlimited possibilities!</p>
			<button class="button">Enter</button>
		</div>
	</div>

	<script>
		// Add sparkles
		function createSparkle() {
			const sparkle = document.createElement('div');
			sparkle.className = 'sparkle';
			sparkle.style.left = Math.random() * window.innerWidth + 'px';
			sparkle.style.top = Math.random() * window.innerHeight + 'px';
			document.body.appendChild(sparkle);
			setTimeout(() => sparkle.remove(), 1500);
		}

		setInterval(createSparkle, 200);
	</script>
</body>
</html>
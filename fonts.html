<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Web Font Browser</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;700&family=Open+Sans:wght@400;700&family=Lato:wght@400;700&family=Montserrat:wght@400;700&family=Poppins:wght@400;700&family=Source+Code+Pro:wght@400;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
	<style>
		:root {
			--bg-color: #1a1b1e;
			--card-bg: #25262b;
			--border-color: #2d2d2d;
			--accent: #6d28d9;
			--text: #ffffff;
			--text-secondary: #a1a1aa;
		}
		
		body {
			font-family: 'Inter', system-ui, sans-serif;
			background: var(--bg-color);
			color: var(--text);
			margin: 0;
			padding: 2rem;
			line-height: 1.5;
		}

		.container {
			max-width: 1400px;
			margin: 0 auto;
		}

		.header {
			margin-bottom: 2rem;
		}

		.search-container {
			position: sticky;
			top: 0;
			background: var(--bg-color);
			padding: 1rem 0;
			z-index: 100;
		}

		.search-box {
			width: 100%;
			padding: 1rem;
			background: var(--card-bg);
			border: 1px solid var(--border-color);
			border-radius: 8px;
			color: var(--text);
			font-size: 16px;
		}

		.font-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
			gap: 1.5rem;
			margin-top: 2rem;
		}

		.font-card {
			background: var(--card-bg);
			padding: 1.5rem;
			border-radius: 8px;
			border: 1px solid var(--border-color);
			transition: all 0.3s ease;
		}

		.font-card:hover {
			transform: translateY(-2px);
			border-color: var(--accent);
			box-shadow: 0 4px 12px rgba(0,0,0,0.2);
		}

		.font-name {
			color: var(--accent);
			font-size: 1.2rem;
			margin-bottom: 1rem;
			font-weight: 600;
		}

		.font-sample {
			font-size: 1.5rem;
			margin: 1rem 0;
			line-height: 1.4;
		}

		.font-info {
			color: var(--text-secondary);
			font-size: 0.9rem;
			margin-top: 1rem;
		}

		.category-filter {
			display: flex;
			gap: 1rem;
			margin: 1rem 0;
			flex-wrap: wrap;
		}

		.category-btn {
			background: var(--card-bg);
			border: 1px solid var(--border-color);
			color: var(--text);
			padding: 0.5rem 1rem;
			border-radius: 4px;
			cursor: pointer;
		}

		.category-btn.active {
			background: var(--accent);
			border-color: var(--accent);
		}

		#loading {
			text-align: center;
			padding: 2rem;
			color: var(--text-secondary);
		}
	</style>
</head>
<body>
	<div class="container">
		<div class="header">
			<h1>Web Fonts Browser</h1>
			<div class="search-container">
				<input type="text" class="search-box" placeholder="Search fonts...">
				<div class="category-filter">
					<button class="category-btn active" data-category="all">All</button>
					<button class="category-btn" data-category="system">System</button>
					<button class="category-btn" data-category="serif">Serif</button>
					<button class="category-btn" data-category="sans-serif">Sans Serif</button>
					<button class="category-btn" data-category="monospace">Monospace</button>

				</div>
			</div>
		</div>

		<div class="font-grid" id="fontGrid"></div>
	</div>

	<script>
		const fonts = [
			// System Fonts
			{ family: 'Arial', category: 'system sans-serif' },
			{ family: 'Helvetica', category: 'system sans-serif' },
			{ family: 'Verdana', category: 'system sans-serif' },
			{ family: 'Times New Roman', category: 'system serif' },
			{ family: 'Georgia', category: 'system serif' },
			{ family: 'Courier New', category: 'system monospace' },
			{ family: 'Consolas', category: 'system monospace' },
			
			// Google Fonts
			{ family: 'Roboto', category: 'sans-serif' },
			{ family: 'Open Sans', category: 'sans-serif' },
			{ family: 'Lato', category: 'sans-serif' },
			{ family: 'Montserrat', category: 'sans-serif' },
			{ family: 'Poppins', category: 'sans-serif' },
			{ family: 'Inter', category: 'sans-serif' },
			{ family: 'Playfair Display', category: 'serif' },
			{ family: 'Source Code Pro', category: 'monospace' }
		];

		function displayFonts() {
			const grid = document.getElementById('fontGrid');
			grid.innerHTML = '';

			fonts.forEach(font => {
				const div = document.createElement('div');
				div.className = 'font-card';
				div.innerHTML = `
					<div class="font-name">${font.family}</div>
					<div class="font-sample" style="font-family: '${font.family}'">
						The quick brown fox jumps over the lazy dog<br>
						ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>
						abcdefghijklmnopqrstuvwxyz<br>
						0123456789
					</div>
					<div class="font-info">
						Category: ${font.category}<br>
						CSS: font-family: '${font.family}', ${font.category};
					</div>
				`;
				grid.appendChild(div);
			});
		}

		// Search functionality
		document.querySelector('.search-box').addEventListener('input', (e) => {
			const searchTerm = e.target.value.toLowerCase();
			document.querySelectorAll('.font-card').forEach(card => {
				const fontName = card.querySelector('.font-name').textContent.toLowerCase();
				card.style.display = fontName.includes(searchTerm) ? 'block' : 'none';
			});
		});

		// Category filter
		document.querySelectorAll('.category-btn').forEach(btn => {
			btn.addEventListener('click', () => {
				const category = btn.dataset.category;
				document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
				btn.classList.add('active');

				document.querySelectorAll('.font-card').forEach(card => {
					const fontCategory = card.querySelector('.font-info').textContent.toLowerCase();
					if (category === 'all' || fontCategory.includes(category.toLowerCase())) {
						card.style.display = 'block';
					} else {
						card.style.display = 'none';
					}
				});
			});
		});

		// Initial display
		displayFonts();
	</script>
</body>
</html>
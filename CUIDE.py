import pandas as pd
from openpyxl import load_workbook, Workbook
from openpyxl.styles import PatternFill, Font, Border, Side
from itertools import repeat
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from multiprocessing import freeze_support
import fitz
import re

class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page

    def __eq__(self, other):
        return (self.word_text == other.word_text and self.word_start == other.word_start
                and self.word_end == other.word_end and self.word_up == other.word_up)

    def __hash__(self):
        return hash((self.word_text, self.word_start, self.word_end, self.word_up))

    @property
    def word_coordinate(self):
        return self.word_up + self.word_down


class Line:
    def __init__(self, line_words):
        self.line_words = line_words

    @property
    def full_line(self):
        return " ".join([word.word_text for word in self.line_words])

    @property
    def line_up(self):
        return min([word.word_up for word in self.line_words])

    @property
    def line_down(self):
        return max([word.word_down for word in self.line_words])

    @property
    def line_start(self):
        return min([word.word_start for word in self.line_words])

    @property
    def line_end(self):
        return max([word.word_end for word in self.line_words])

    @property
    def line_page(self):
        return self.line_words[0].word_page


def extract_all_words(link):
    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))
    all_words = []
    for page_index, page in enumerate(doc_1):
        for ext_word in page.get_text("words"):
            all_words.append([ext_word, page_index])
    return all_words


def fitz_extract_all_words(link):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4
    list_of_lines = []

    link = link.removesuffix('\n')
    doc_1 = fitz.open(link)

    for page_index, page in enumerate(doc_1):
        word_properties_list = [
            Word(w[text_index], w[start_index], w[end_index], w[up_index], w[down_index], page_index)
            for w in page.get_text("words")]

        line_list_of_words = []
        found_words_list = []

        for fixed_word in word_properties_list:
            if fixed_word in found_words_list:
                continue
            for looping_word in word_properties_list:
                if (looping_word.word_up - 4 <= fixed_word.word_up <= looping_word.word_up + 4
                        or looping_word.word_down - 4 <= fixed_word.word_down <= looping_word.word_down + 4):
                    line_list_of_words.append(looping_word)
                    found_words_list.append(looping_word)

            line_list_of_words = list(set(line_list_of_words))
            line_list_of_words.sort(key=lambda x: x.word_start)
            list_of_lines.append(
                Line(line_list_of_words))

            line_list_of_words = []
    list_of_lines.sort(key=lambda x: x.line_up)
    list_of_lines.sort(key=lambda x: x.line_page)

    return list_of_lines


def count_of_pages(link):
    doc = fitz.open(link)
    return doc.page_count


my_pattern = re.compile(
    r"(Additional Resources:.*|"
    r"date [0-9]{2}/[0-9]{2}/20[0-9]{2}|"
    r"page [0-9]+ of [0-9]+|"
    r"CUI Devices is now Same Sky! Learn More|"
    r"sameskydevices.com|"
    r"cuidevices.com|CUI DEVICES|SAME SKY)", 
    re.IGNORECASE
)


def clean_lines(line_list, link):
  new_lines_list = []
  pages_count = count_of_pages(link)
  for line in line_list:
    new_line = re.sub("\s+"," ",re.sub(my_pattern, "", line.full_line))
    new_lines_list.append(new_line)
    if line.line_page+1 == pages_count or line.full_line.lower() == "SAFETY CONSIDERATIONS".lower():
      return new_lines_list
    
def compare(new_lines_list_1, new_lines_list_2):
    changed_lines_1 = "|".join(list(set(new_lines_list_1)-set(new_lines_list_2)))
    changed_lines_2 = "|".join(list(set(new_lines_list_2)-set(new_lines_list_1)))
    return changed_lines_1, changed_lines_2

def main(links):
    # try:
      link_1 = links.split("\t")[0]
      link_2 = links.split("\t")[-1].strip()
      return links.strip()+"\t"+"\t".join(compare(clean_lines(fitz_extract_all_words(link_1), link_1),
                    clean_lines(fitz_extract_all_words(link_2), link_2)))
    # except Exception as E:
        # return "Error"
    

with open("input.txt", "r") as input_file:
    links_list = input_file.readlines()

with open("output.txt", "w") as output_file:
    output_file.write("Document\tLatest\tfeatures")
    output_file.write("\n")


for index, row in enumerate(map(main, links_list), 1):
    with open("output.txt", "a", encoding='utf8') as output_file:
        output_file.write(row)
        output_file.write("\n")

    print(f"Done_{index}".center(50, "-").upper())

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# os.environ['SPARK_HOME'] = r'C:\\Spark\\spark-3.5.1-bin-hadoop3'\n", "# os.environ['PYSPARK_DRIVER_PYTHON'] = 'jupyter'\n", "# os.environ['PYSPARK_DRIVER_PYTHON_OPTS'] = 'lab'\n", "# os.environ['PYSPARK_PYTHON'] = r'python'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["spark = SparkSession.builder.appName(\"RDD_Demo\").getOrCreate()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["numbers = [1,2,3,4]\n", "rdd = spark.sparkContext.parallelize(numbers)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["rdd.collect()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "Py4JJavaError", "evalue": "An error occurred while calling z:org.apache.spark.api.python.PythonRDD.runJob.\n: org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 1.0 failed 1 times, most recent failure: Lost task 0.0 in stage 1.0 (TID 12) (10.199.110.118 executor driver): org.apache.spark.SparkException: Python worker failed to connect back.\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:203)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.create(PythonWorkerFactory.scala:109)\r\n\tat org.apache.spark.SparkEnv.createPythonWorker(SparkEnv.scala:124)\r\n\tat org.apache.spark.api.python.BasePythonRunner.compute(PythonRunner.scala:174)\r\n\tat org.apache.spark.api.python.PythonRDD.compute(PythonRDD.scala:67)\r\n\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:367)\r\n\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:331)\r\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:93)\r\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:166)\r\n\tat org.apache.spark.scheduler.Task.run(Task.scala:141)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$4(Executor.scala:620)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\r\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:94)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:623)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\nCaused by: java.net.SocketTimeoutException: Accept timed out\r\n\tat java.base/sun.nio.ch.NioSocketImpl.timedAccept(NioSocketImpl.java:701)\r\n\tat java.base/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:745)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:698)\r\n\tat java.base/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:639)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:585)\r\n\tat java.base/java.net.ServerSocket.accept(ServerSocket.java:543)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:190)\r\n\t... 17 more\r\n\nDriver stacktrace:\r\n\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:2856)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:2792)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:2791)\r\n\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\r\n\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\r\n\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\r\n\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:2791)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1247)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1247)\r\n\tat scala.Option.foreach(Option.scala:407)\r\n\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1247)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:3060)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2994)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2983)\r\n\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:49)\r\n\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:989)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2398)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2419)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2438)\r\n\tat org.apache.spark.api.python.PythonRDD$.runJob(PythonRDD.scala:181)\r\n\tat org.apache.spark.api.python.PythonRDD.runJob(PythonRDD.scala)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:75)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:52)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\r\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)\r\n\tat py4j.Gateway.invoke(Gateway.java:282)\r\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\r\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\r\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\r\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\nCaused by: org.apache.spark.SparkException: Python worker failed to connect back.\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:203)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.create(PythonWorkerFactory.scala:109)\r\n\tat org.apache.spark.SparkEnv.createPythonWorker(SparkEnv.scala:124)\r\n\tat org.apache.spark.api.python.BasePythonRunner.compute(PythonRunner.scala:174)\r\n\tat org.apache.spark.api.python.PythonRDD.compute(PythonRDD.scala:67)\r\n\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:367)\r\n\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:331)\r\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:93)\r\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:166)\r\n\tat org.apache.spark.scheduler.Task.run(Task.scala:141)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$4(Executor.scala:620)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\r\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:94)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:623)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\t... 1 more\r\nCaused by: java.net.SocketTimeoutException: Accept timed out\r\n\tat java.base/sun.nio.ch.NioSocketImpl.timedAccept(NioSocketImpl.java:701)\r\n\tat java.base/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:745)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:698)\r\n\tat java.base/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:639)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:585)\r\n\tat java.base/java.net.ServerSocket.accept(ServerSocket.java:543)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:190)\r\n\t... 17 more\r\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mPy4JJavaError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mrdd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfirst\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\rdd.py:2888\u001b[0m, in \u001b[0;36mRDD.first\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   2862\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfirst\u001b[39m(\u001b[38;5;28mself\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRDD[T]\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m   2863\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   2864\u001b[0m \u001b[38;5;124;03m    Return the first element in this RDD.\u001b[39;00m\n\u001b[0;32m   2865\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   2886\u001b[0m \u001b[38;5;124;03m    ValueError: RDD is empty\u001b[39;00m\n\u001b[0;32m   2887\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 2888\u001b[0m     rs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtake\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2889\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m rs:\n\u001b[0;32m   2890\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m rs[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\rdd.py:2855\u001b[0m, in \u001b[0;36mRDD.take\u001b[1;34m(self, num)\u001b[0m\n\u001b[0;32m   2852\u001b[0m         taken \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   2854\u001b[0m p \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mrange\u001b[39m(partsScanned, \u001b[38;5;28mmin\u001b[39m(partsScanned \u001b[38;5;241m+\u001b[39m numPartsToTry, totalParts))\n\u001b[1;32m-> 2855\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrunJob\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtakeUpToNumLeft\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2857\u001b[0m items \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m res\n\u001b[0;32m   2858\u001b[0m partsScanned \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m numPartsToTry\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\context.py:2510\u001b[0m, in \u001b[0;36mSparkContext.runJob\u001b[1;34m(self, rdd, partitionFunc, partitions, allowLocal)\u001b[0m\n\u001b[0;32m   2508\u001b[0m mappedRDD \u001b[38;5;241m=\u001b[39m rdd\u001b[38;5;241m.\u001b[39mmapPartitions(partitionFunc)\n\u001b[0;32m   2509\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jvm \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[1;32m-> 2510\u001b[0m sock_info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jvm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mPythonRDD\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrunJob\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jsc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmappedRDD\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jrdd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartitions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2511\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mlist\u001b[39m(_load_from_socket(sock_info, mappedRDD\u001b[38;5;241m.\u001b[39m_jrdd_deserializer))\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\py4j\\java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[1;34m(self, *args)\u001b[0m\n\u001b[0;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[0;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[1;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[0;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\errors\\exceptions\\captured.py:179\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[1;34m(*a, **kw)\u001b[0m\n\u001b[0;32m    177\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdeco\u001b[39m(\u001b[38;5;241m*\u001b[39ma: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Any:\n\u001b[0;32m    178\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 179\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    180\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m Py4JJavaError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    181\u001b[0m         converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\py4j\\protocol.py:326\u001b[0m, in \u001b[0;36mget_return_value\u001b[1;34m(answer, gateway_client, target_id, name)\u001b[0m\n\u001b[0;32m    324\u001b[0m value \u001b[38;5;241m=\u001b[39m OUTPUT_CONVERTER[\u001b[38;5;28mtype\u001b[39m](answer[\u001b[38;5;241m2\u001b[39m:], gateway_client)\n\u001b[0;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m answer[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m==\u001b[39m REFERENCE_TYPE:\n\u001b[1;32m--> 326\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JJavaError(\n\u001b[0;32m    327\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[0;32m    328\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name), value)\n\u001b[0;32m    329\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    330\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JError(\n\u001b[0;32m    331\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m. Trace:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{3}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[0;32m    332\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name, value))\n", "\u001b[1;31mPy4JJavaError\u001b[0m: An error occurred while calling z:org.apache.spark.api.python.PythonRDD.runJob.\n: org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 1.0 failed 1 times, most recent failure: Lost task 0.0 in stage 1.0 (TID 12) (10.199.110.118 executor driver): org.apache.spark.SparkException: Python worker failed to connect back.\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:203)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.create(PythonWorkerFactory.scala:109)\r\n\tat org.apache.spark.SparkEnv.createPythonWorker(SparkEnv.scala:124)\r\n\tat org.apache.spark.api.python.BasePythonRunner.compute(PythonRunner.scala:174)\r\n\tat org.apache.spark.api.python.PythonRDD.compute(PythonRDD.scala:67)\r\n\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:367)\r\n\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:331)\r\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:93)\r\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:166)\r\n\tat org.apache.spark.scheduler.Task.run(Task.scala:141)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$4(Executor.scala:620)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\r\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:94)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:623)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\nCaused by: java.net.SocketTimeoutException: Accept timed out\r\n\tat java.base/sun.nio.ch.NioSocketImpl.timedAccept(NioSocketImpl.java:701)\r\n\tat java.base/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:745)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:698)\r\n\tat java.base/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:639)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:585)\r\n\tat java.base/java.net.ServerSocket.accept(ServerSocket.java:543)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:190)\r\n\t... 17 more\r\n\nDriver stacktrace:\r\n\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:2856)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:2792)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:2791)\r\n\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\r\n\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\r\n\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\r\n\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:2791)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1247)\r\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1247)\r\n\tat scala.Option.foreach(Option.scala:407)\r\n\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1247)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:3060)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2994)\r\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2983)\r\n\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:49)\r\n\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:989)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2398)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2419)\r\n\tat org.apache.spark.SparkContext.runJob(SparkContext.scala:2438)\r\n\tat org.apache.spark.api.python.PythonRDD$.runJob(PythonRDD.scala:181)\r\n\tat org.apache.spark.api.python.PythonRDD.runJob(PythonRDD.scala)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:75)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:52)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\r\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)\r\n\tat py4j.Gateway.invoke(Gateway.java:282)\r\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\r\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\r\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\r\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\nCaused by: org.apache.spark.SparkException: Python worker failed to connect back.\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:203)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.create(PythonWorkerFactory.scala:109)\r\n\tat org.apache.spark.SparkEnv.createPythonWorker(SparkEnv.scala:124)\r\n\tat org.apache.spark.api.python.BasePythonRunner.compute(PythonRunner.scala:174)\r\n\tat org.apache.spark.api.python.PythonRDD.compute(PythonRDD.scala:67)\r\n\tat org.apache.spark.rdd.RDD.computeOrReadCheckpoint(RDD.scala:367)\r\n\tat org.apache.spark.rdd.RDD.iterator(RDD.scala:331)\r\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:93)\r\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:166)\r\n\tat org.apache.spark.scheduler.Task.run(Task.scala:141)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$4(Executor.scala:620)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\r\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\r\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:94)\r\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:623)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\t... 1 more\r\nCaused by: java.net.SocketTimeoutException: Accept timed out\r\n\tat java.base/sun.nio.ch.NioSocketImpl.timedAccept(NioSocketImpl.java:701)\r\n\tat java.base/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:745)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:698)\r\n\tat java.base/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:639)\r\n\tat java.base/java.net.ServerSocket.implAccept(ServerSocket.java:585)\r\n\tat java.base/java.net.ServerSocket.accept(ServerSocket.java:543)\r\n\tat org.apache.spark.api.python.PythonWorkerFactory.createSimpleWorker(PythonWorkerFactory.scala:190)\r\n\t... 17 more\r\n"]}], "source": ["rdd.first()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rdd.take(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}
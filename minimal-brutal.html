<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Minimal Brutal Dashboard</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<style>
		:root {
			--primary: #6b8e9b;
			--accent: #a7c4bc;
			--bg: #f5f9f8;
			--card-bg: #ffffff;
			--text: #2c3e50;
			--text-light: #7f8c8d;
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			background: var(--bg);
			font-family: -apple-system, BlinkMacSystemFont, sans-serif;
			color: var(--text);
			line-height: 1.5;
		}

		.container {
			display: flex;
			min-height: 100vh;
		}

		/* Sidebar */
		.sidebar {
			width: 280px;
			background: linear-gradient(to bottom, var(--bg), #e8f0ed);
			border-right: 2px solid var(--primary);
			padding: 40px 20px;
			transition: all 0.3s ease;
		}

		.sidebar.collapsed {
			width: 80px;
		}

		.logo {
			font-size: 24px;
			font-weight: 700;
			margin-bottom: 60px;
			letter-spacing: -1px;
			color: var(--primary);
		}

		.nav-item {
			display: flex;
			align-items: center;
			gap: 16px;
			padding: 12px;
			margin: 8px 0;
			text-decoration: none;
			color: var(--text);
			border-radius: 0;
			transition: all 0.2s ease;
			border: 1px solid transparent;
		}

		.nav-item:hover, .nav-item.active {
			background: var(--accent);
			color: var(--text);
			transform: translateX(4px);
			border: 1px solid var(--primary);
		}

		.sidebar.collapsed .nav-item span {
			display: none;
		}

		/* Main Content */
		.main-content {
			flex: 1;
			padding: 40px;
			margin-left: 0;
			transition: all 0.3s ease;
		}

		.main-content.expanded {
			margin-left: 0;
		}

		.header {
			margin-bottom: 60px;
		}

		.header h1 {
			font-size: 32px;
			font-weight: 700;
			letter-spacing: -1px;
			color: var(--primary);
		}

		.columns {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 40px;
		}

		.column h2 {
			font-size: 20px;
			margin-bottom: 24px;
			font-weight: 600;
			letter-spacing: -0.5px;
			color: var(--primary);
		}

		.card {
			background: var(--card-bg);
			border: 2px solid var(--primary);
			padding: 24px;
			margin-bottom: 24px;
			transition: all 0.3s ease;
			position: relative;
			box-shadow: 4px 4px 0 var(--accent);
		}

		.card:hover {
			transform: translateY(-2px);
			box-shadow: 6px 6px 0 var(--accent);
		}


		.card h3 {
			font-size: 16px;
			font-weight: 600;
			margin-bottom: 16px;
			text-transform: uppercase;
			letter-spacing: 1px;
		}

		.stat-value {
			font-size: 48px;
			font-weight: 700;
			letter-spacing: -1px;
			margin-bottom: 8px;
			color: var(--primary);
		}

		.stat-label {
			font-size: 14px;
			color: var(--text-light);
			text-transform: uppercase;
			letter-spacing: 1px;
		}

		.toggle-btn {
			position: fixed;
			top: 20px;
			left: 20px;
			width: 40px;
			height: 40px;
			background: var(--bg);
			border: 2px solid var(--primary);
			cursor: pointer;
			z-index: 100;
			transition: all 0.3s ease;
		}

		.toggle-btn:hover {
			background: var(--accent);
			color: var(--text);
		}

		@media (max-width: 1024px) {
			.columns {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		@media (max-width: 768px) {
			.columns {
				grid-template-columns: 1fr;
			}
		}
	</style>
</head>
<body>
	<button class="toggle-btn">
		<i class="fas fa-bars"></i>
	</button>
	
	<div class="container">
		<aside class="sidebar">
			<div class="logo">MB/</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-chart-bar"></i>
					<span>Overview</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-clock"></i>
					<span>Activity</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-cog"></i>
					<span>Settings</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<header class="header">
				<h1>Dashboard Overview</h1>
			</header>

			<div class="columns">
				<section class="column">
					<h2>Performance</h2>
					<div class="card">
						<h3>System Load</h3>
						<div class="stat-value">92%</div>
						<div class="stat-label">Current Usage</div>
					</div>
					<div class="card">
						<h3>Memory</h3>
						<div class="stat-value">64GB</div>
						<div class="stat-label">Total Capacity</div>
					</div>
				</section>

				<section class="column">
					<h2>Analytics</h2>
					<div class="card">
						<h3>Active Users</h3>
						<div class="stat-value">1,892</div>
						<div class="stat-label">Last 24 Hours</div>
					</div>
					<div class="card">
						<h3>Response Time</h3>
						<div class="stat-value">45ms</div>
						<div class="stat-label">Average</div>
					</div>
				</section>

				<section class="column">
					<h2>Status</h2>
					<div class="card">
						<h3>Uptime</h3>
						<div class="stat-value">99.9%</div>
						<div class="stat-label">Last 30 Days</div>
					</div>
					<div class="card">
						<h3>Issues</h3>
						<div class="stat-value">0</div>
						<div class="stat-label">All Systems Normal</div>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.toggle-btn').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
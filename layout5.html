<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Neon Grid - Layout 5</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<link rel="stylesheet" href="shared-styles.css">
	<link rel="stylesheet" href="layout5.css">
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-microchip"></i>
				<span>Neon Grid</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-network-wired"></i>
					<span>Network</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-code"></i>
					<span>Systems</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-robot"></i>
					<span>AI Core</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Network Status</h2>
					<div class="card animate-fade-in">
						<h3>Bandwidth</h3>
						<p>892 MB/s</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Latency</h3>
						<p>12ms average</p>
					</div>
				</section>

				<section class="column">
					<h2>System Load</h2>
					<div class="card animate-fade-in">
						<h3>Processing</h3>
						<p>78% capacity</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Memory</h3>
						<p>12.4 TB active</p>
					</div>
				</section>

				<section class="column">
					<h2>AI Status</h2>
					<div class="card animate-fade-in">
						<h3>Neural Load</h3>
						<p>Operating at 92%</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Learning Rate</h3>
						<p>0.0001 epsilon</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
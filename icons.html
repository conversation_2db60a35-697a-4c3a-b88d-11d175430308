<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Icon Reference</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<style>
		body {
			font-family: 'Inter', system-ui, sans-serif;
			padding: 2rem;
			background: #1a1b1e;
			color: white;
			line-height: 1.5;
		}
		.container {
			max-width: 1200px;
			margin: 0 auto;
		}
		.search-box {
			width: 100%;
			padding: 1rem;
			background: #25262b;
			border: 1px solid #2d2d2d;
			border-radius: 8px;
			color: white;
			font-size: 16px;
			margin-bottom: 2rem;
		}
		.icon-section {
			margin-bottom: 2rem;
		}
		.icon-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
			gap: 1rem;
		}
		.icon-item {
			background: #25262b;
			padding: 1.5rem;
			border-radius: 8px;
			display: flex;
			align-items: center;
			gap: 1rem;
			transition: all 0.3s ease;
			cursor: pointer;
			border: 1px solid #2d2d2d;
		}
		.icon-item:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0,0,0,0.2);
			background: #2c2e33;
		}
		.icon-item i {
			font-size: 24px;
			color: #6d28d9;
			width: 32px;
			text-align: center;
		}
		.icon-info {
			display: flex;
			flex-direction: column;
		}
		.icon-name {
			font-weight: 500;
		}
		.icon-class {
			color: #a1a1aa;
			font-size: 14px;
			font-family: monospace;
		}
		h2 {
			margin-bottom: 1rem;
			color: #6d28d9;
			font-size: 1.5rem;
		}
		.copied {
			background: #6d28d9;
		}
	</style>
</head>
<body>
	<div class="container">
		<h2>Icon Reference</h2>
		<input type="text" class="search-box" placeholder="Search icons...">
		
		<div class="icon-section">
			<h2>Navigation Icons</h2>
			<div class="icon-grid">
				<div class="icon-item" onclick="copyToClipboard('fas fa-chevron-left')">
					<i class="fas fa-chevron-left"></i>
					<div class="icon-info">
						<span class="icon-name">Chevron Left</span>
						<span class="icon-class">fas fa-chevron-left</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-cube')">
					<i class="fas fa-cube"></i>
					<div class="icon-info">
						<span class="icon-name">Cube</span>
						<span class="icon-class">fas fa-cube</span>
					</div>
				</div>
			</div>
		</div>

		<div class="icon-section">
			<h2>Action Icons</h2>
			<div class="icon-grid">
				<div class="icon-item" onclick="copyToClipboard('fas fa-exchange-alt')">
					<i class="fas fa-exchange-alt"></i>
					<div class="icon-info">
						<span class="icon-name">Exchange</span>
						<span class="icon-class">fas fa-exchange-alt</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-cogs')">
					<i class="fas fa-cogs"></i>
					<div class="icon-info">
						<span class="icon-name">Settings</span>
						<span class="icon-class">fas fa-cogs</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-search')">
					<i class="fas fa-search"></i>
					<div class="icon-info">
						<span class="icon-name">Search</span>
						<span class="icon-class">fas fa-search</span>
					</div>
				</div>
			</div>
		</div>

		<div class="icon-section">
			<h2>Document Icons</h2>
			<div class="icon-grid">
				<div class="icon-item" onclick="copyToClipboard('fas fa-file-pdf')">
					<i class="fas fa-file-pdf"></i>
					<div class="icon-info">
						<span class="icon-name">PDF File</span>
						<span class="icon-class">fas fa-file-pdf</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-book')">
					<i class="fas fa-book"></i>
					<div class="icon-info">
						<span class="icon-name">Book</span>
						<span class="icon-class">fas fa-book</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-file-export')">
					<i class="fas fa-file-export"></i>
					<div class="icon-info">
						<span class="icon-name">Export</span>
						<span class="icon-class">fas fa-file-export</span>
					</div>
				</div>
			</div>
		</div>

		<div class="icon-section">
			<h2>Additional Icons</h2>
			<div class="icon-grid">
				<div class="icon-item" onclick="copyToClipboard('fas fa-microchip')">
					<i class="fas fa-microchip"></i>
					<div class="icon-info">
						<span class="icon-name">Microchip</span>
						<span class="icon-class">fas fa-microchip</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-magic')">
					<i class="fas fa-magic"></i>
					<div class="icon-info">
						<span class="icon-name">Magic</span>
						<span class="icon-class">fas fa-magic</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-equals')">
					<i class="fas fa-equals"></i>
					<div class="icon-info">
						<span class="icon-name">Equals</span>
						<span class="icon-class">fas fa-equals</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-calendar-plus')">
					<i class="fas fa-calendar-plus"></i>
					<div class="icon-info">
						<span class="icon-name">Calendar Plus</span>
						<span class="icon-class">fas fa-calendar-plus</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-tags')">
					<i class="fas fa-tags"></i>
					<div class="icon-info">
						<span class="icon-name">Tags</span>
						<span class="icon-class">fas fa-tags</span>
					</div>
				</div>
				<div class="icon-item" onclick="copyToClipboard('fas fa-list')">
					<i class="fas fa-list"></i>
					<div class="icon-info">
						<span class="icon-name">List</span>
						<span class="icon-class">fas fa-list</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script>
		function copyToClipboard(text) {
			navigator.clipboard.writeText(text);
			event.currentTarget.classList.add('copied');
			setTimeout(() => {
				event.currentTarget.classList.remove('copied');
			}, 500);
		}

		document.querySelector('.search-box').addEventListener('input', (e) => {
			const searchTerm = e.target.value.toLowerCase();
			document.querySelectorAll('.icon-item').forEach(item => {
				const iconName = item.querySelector('.icon-name').textContent.toLowerCase();
				const iconClass = item.querySelector('.icon-class').textContent.toLowerCase();
				if (iconName.includes(searchTerm) || iconClass.includes(searchTerm)) {
					item.style.display = 'flex';
				} else {
					item.style.display = 'none';
				}
			});
		});
	</script>
</body>
</html>
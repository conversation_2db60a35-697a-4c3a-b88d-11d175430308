import os
import re
import subprocess as sp
from time import sleep
import concurrent.futures
import pdfplumber
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
import fitz
from multiprocessing import freeze_support
from datetime import date


class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page


def extract_text_from_pdf(file_path):
    doc = pdfplumber.open(file_path)
    text = "\n".join([page.extract_text_simple() for page in doc.pages])
    text = re.sub(' +', ' ', text)
    return text.lower().strip().replace("  ", " ")


def fitz_extract_all_words(link):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4

    word_properties_list = []
    list_of_lines = []

    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))

    for page_index, page in enumerate(doc_1):
        for ext_word in page.get_text("words"):
            word_properties_list.append(
                Word(ext_word[text_index], ext_word[start_index], ext_word[end_index], ext_word[up_index],
                     ext_word[down_index], page_index))

        line_list_of_words = []
        found_words_list = []

        for fixed_word in word_properties_list:
            if fixed_word in found_words_list:
                continue
            for looping_word in word_properties_list:
                if (looping_word.word_up - 4 <= fixed_word.word_up <= looping_word.word_up + 4
                        or looping_word.word_down - 4 <= fixed_word.word_down <= looping_word.word_down + 4):
                    line_list_of_words.append(looping_word)
                    found_words_list.append(looping_word)

            line_list_of_words.sort(key=lambda x: x.word_start)
            list_of_lines.append(line_list_of_words)

            line_list_of_words = []
        word_properties_list = []
    lines_list = []
    for line in list_of_lines:
        lines_list.append(" ".join([word.word_text for word in line]))

    return "\n".join(lines_list)


def count_of_images(link):
    doc = fitz.open(link)
    num_images = 0
    for page_index in range(len(doc)):
        image_list = doc[page_index].get_images()

        # printing number of images found in this page
        if image_list:
            num_images += len(image_list)

    return num_images


def count_of_pages(link):
    doc = fitz.open(link)
    return doc.page_count


def compare_lines(doc_text1: str, doc_text2: str, lines_limit: int):

    lines_list1 = set(doc_text1.split('\n'))
    lines_list2 = set(doc_text2.split("\n"))

    doc_changed_lines_1 = list(lines_list1 - lines_list2)
    doc_changed_lines_2 = list(lines_list2 - lines_list1)

    # get number of lines changed
    for line in doc_changed_lines_1:
        if line.strip() == '':
            doc_changed_lines_1.remove(line)
    for line in doc_changed_lines_2:
        if line.strip() == '':
            doc_changed_lines_2.remove(line)

    num_lines_1 = len(doc_changed_lines_1)
    num_lines_2 = len(doc_changed_lines_2)

    if len(doc_changed_lines_1) > lines_limit or len(doc_changed_lines_2) > lines_limit:
        doc_changed_lines_2 = doc_changed_lines_1 = ['']
        compare_line_status = "Not Equal"

    elif len(doc_changed_lines_1) != len(doc_changed_lines_2):
        compare_line_status = "Not Equal"

    else:
        removed_date_lines_1, removed_date_lines_2 = remove_date(doc_changed_lines_1, doc_changed_lines_2)
        
        removed_special_chars_lines_1, removed_special_chars_lines_2 = remove_special_chars(removed_date_lines_1, removed_date_lines_2)

        compare_line_status = 'Equal' if removed_special_chars_lines_1 == removed_special_chars_lines_2 else "Not Equal"
    return doc_changed_lines_1, doc_changed_lines_2, num_lines_1, num_lines_2, compare_line_status


def arrange_lines(doc_changed_lines_1, doc_changed_lines_2, lines_limit):
    if len(doc_changed_lines_1) < lines_limit:
        for x in range(len(doc_changed_lines_1), lines_limit, 1):
            doc_changed_lines_1.append('')

    if len(doc_changed_lines_2) < lines_limit:
        for x in range(len(doc_changed_lines_2), lines_limit, 1):
            doc_changed_lines_2.append('')

    return doc_changed_lines_1, doc_changed_lines_2


def remove_date(doc_changed_lines_1, doc_changed_lines_2):
    removed_date_lines_1 = []
    removed_date_lines_2 = []
    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)

    for line in doc_changed_lines_1:
        removed_date_lines_1.append(date_pattern.sub("", line).replace(" ", "").strip())
    for line in doc_changed_lines_2:
        removed_date_lines_2.append(date_pattern.sub("", line).replace(" ", "").strip())

    removed_date_lines_1.sort()
    removed_date_lines_2.sort()
    return removed_date_lines_1, removed_date_lines_2

def remove_special_chars(doc_changed_lines_1, doc_changed_lines_2):
    removed_special_chars_lines_1 = []
    removed_special_charse_lines_2 = []
    chars_to_remove = re.compile(r"[^,\.a-zA-Z0-9\s]"
                              , flags=re.IGNORECASE)

    for line in doc_changed_lines_1:
        removed_special_chars_lines_1.append(chars_to_remove.sub("", line).replace(" ", "").strip())
    for line in doc_changed_lines_2:
        removed_special_charse_lines_2.append(chars_to_remove.sub("", line).replace(" ", "").strip())

    removed_special_chars_lines_1.sort()
    removed_special_charse_lines_2.sort()

    return removed_special_chars_lines_1, removed_special_charse_lines_2


def check_revision(doc_changed_lines_1, doc_changed_lines_2):
    revision_status_1 = []
    revision_status_2 = []

    revision_pattern = re.compile(r'(copyright|document created|creation date|date[\s:_]+|www.|http|created at'
                                  r'|downloaded|printed|phone|e-mail|as of|further information|subject to modifications'
                                  r'|rev:|rev\.|revised|generated|subject to change|without notice|updated|phone[\s:+]+'
                                  r'|technical changes reserved|issued:|©|all rights reserved|fax[\s:+]+|date:)'
                                  , flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):

        match1 = revision_pattern.findall(line1)
        match2 = revision_pattern.findall(line2)

        if match1:
            revision_status_1.append("Found")
        elif not match1 and line1 != '':
            revision_status_1.append("Not Found")
        else:
            revision_status_1.append("Found")

        if match2:
            revision_status_2.append("Found")
        elif not match2 and line2 != '':
            revision_status_2.append("Not Found")
        else:
            revision_status_1.append("Found")

    if list(set(revision_status_1)) == list(set(revision_status_2)) == ['Found']:
        return "Found"
    else:
        return 'Not Found'


def check_reach(doc_changed_lines_1, doc_changed_lines_2):
    reach_status_1 = 'Not Found'
    reach_status_2 = 'Not Found'

    reach_pattern = re.compile(r'((ec) no|candidate|reach|svhc|substance|article 59|echa'
                               r'|\([0-9]{1,2}[\s]*(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[\s]*20[0-9]{2}\))',
                               flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = reach_pattern.findall(line1)
        match2 = reach_pattern.findall(line2)
        if match1:
            reach_status_1 = "Found"
        if match2:
            reach_status_2 = "Found"
    if reach_status_1 == 'Found' or reach_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def check_rohs(doc_changed_lines_1, doc_changed_lines_2):
    reach_status_1 = 'Not Found'
    reach_status_2 = 'Not Found'

    reach_pattern = re.compile(r'rohs',
                               flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = reach_pattern.findall(line1)
        match2 = reach_pattern.findall(line2)
        if match1:
            reach_status_1 = "Found"
        if match2:
            reach_status_2 = "Found"
    if reach_status_1 == 'Found' or reach_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def check_lc(doc_changed_lines_1, doc_changed_lines_2):
    lc_status_1 = 'Not Found'
    lc_status_2 = 'Not Found'

    lc_pattern = re.compile(r'Obsolete|Discontinued|Last time buy|Not Recommended for new design|nrnd'
                            r'|Withdrawn|EOL|LTB|Discontinu|Not Recommend|End of life|Not for new design'
                            r'|Inactive|End-of-Life|Not Active|end of service|end-of-service', flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = lc_pattern.findall(line1)
        match2 = lc_pattern.findall(line2)
        if match1:
            lc_status_1 = "Found"
        if match2:
            lc_status_2 = "Found"
    if lc_status_1 == 'Found' or lc_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def assign_status(compare_status, reach_status, lc_status, rohs_status, revision_status, num_lines_1, num_lines_2):
    check_change = []
    if num_lines_1 == num_lines_2 and compare_status != "Equal":
        if revision_status == 'Found':
            check_change.append('Check Revision')

    if lc_status == 'Found':
        check_change.append('Check Lc')

    if reach_status == 'Found':
        check_change.append('Check Reach')

    if rohs_status == 'Found':
        check_change.append('Check Rohs')

    check_change.sort()
    check_change_final = "|".join(check_change)

    if compare_status == 'Equal' and check_change_final == '':
        compare_status_final = 'Equal Manual'
    elif compare_status != 'Equal' and check_change_final == 'Check Revision':
        compare_status_final = 'Equal Manual'
    elif compare_status == 'Equal' and check_change_final != '':
        compare_status_final = 'Equal Parts'
    else:
        compare_status_final = 'Not Equal Manual'
    return compare_status_final, check_change_final


def assign_trust_level(compare_status, text1, text2, num_pages_1, num_pages_2, num_images_1, num_images_2):
    comment1, comment2, comment3, comment4 = '', '', '', ''

    '''Image change'''
    if num_images_1 != num_images_2:
        comment3 = "image change"
    else:
        comment3 = ''

    '''unsearchable'''
    if len(text1) < num_pages_1 * 300 or len(text2) < num_pages_2 * 300:
        comment4 = 'suspected unsearchable'
    else:
        comment4 = ''

    'Deducing accuracy percent'
    trust_level = 'None'
    if compare_status != 'Not Equal Manual':
        trust_level = 100
        if 0 < abs(len(text1) - len(text2)) <= 50:
            trust_level -= 10
            comment1 = 'different number of characters'
        elif abs(len(text1) - len(text2)) > 50:
            trust_level -= 50
            comment1 = 'different number of characters'

        if num_pages_2 != num_pages_1:
            trust_level -= 50
            comment2 = 'different number of pages'

        if comment4 == 'suspected unsearchable':
            trust_level = 0

    comment_list = [comment1, comment2, comment3, comment4]
    comment_list = list(set(comment_list))
    comment_list.sort()

    return trust_level, "|".join(comment_list)


def main(first_columns: str):
    """some variables"""
    custom_lines_limit = 20
    revision_status = 'Found'
    result_row = []
    four_two_lines_list = []

    '''Assign link values'''
    link1 = first_columns.split('\t')[3].removesuffix('\n')
    link2 = first_columns.split('\t')[4].removesuffix('\n')
    first_columns = first_columns.removesuffix('\n')
    try:
        '''Get  number of pages in the document'''
        num_pages_1 = count_of_pages(link1)
        num_pages_2 = count_of_pages(link2)

        if num_pages_1 <= 101 or num_pages_2 <= 101:
            '''Extract all text from document'''
            doc_all_text_1 = fitz_extract_all_words(link1).lower()
            doc_all_text_2 = fitz_extract_all_words(link2).lower()

            if  "Please wait...".lower() in doc_all_text_1 or "Please wait...".lower() in doc_all_text_2:
                result_row.append(first_columns + '\t' + 'Reject\tIPC')
                return '\t'.join(result_row)

            '''Get  number of images in the document'''
            num_images_1 = count_of_images(link1)
            num_images_2 = count_of_images(link2)

            '''Compare lines'''
            changed_lines_1, changed_lines_2, num_lines_1, num_lines_2, compare_status = compare_lines(doc_all_text_1,
                                                                                                       doc_all_text_2,
                                                                                                       custom_lines_limit)

            '''Arranging output'''
            changed_lines_1.sort()
            changed_lines_2.sort()

            # fill blanks
            changed_lines_1, changed_lines_2 = arrange_lines(changed_lines_1, changed_lines_2, custom_lines_limit)
            for changed_line_1, changed_line_2 in zip(changed_lines_1, changed_lines_2):
                four_two_lines_list.append(changed_line_1.strip())
                four_two_lines_list.append(changed_line_2.strip())

            if compare_status != "Equal":
                revision_status = check_revision(changed_lines_1, changed_lines_2)

            reach_status = check_reach(changed_lines_1, changed_lines_2)
            lc_status = check_lc(changed_lines_1, changed_lines_2)
            rohs_status = check_rohs(changed_lines_1, changed_lines_2)

            compare_status, check_change = assign_status(compare_status, reach_status, lc_status, rohs_status
                                                         , revision_status, num_lines_1, num_lines_2)

            trust_level, comments = assign_trust_level(compare_status, doc_all_text_1, doc_all_text_2, num_pages_1,
                                                       num_pages_2, num_images_1, num_images_2)

            '''Assign result values'''
            result_row.append(first_columns + '\t' + 'Done')
            result_row.append(compare_status + '\t' + check_change + '\t' + str(trust_level))
            result_row.append(comments.replace("||", "|").removesuffix("|").removeprefix("|"))
            result_row.append(str(num_pages_1) + '\t' + str(num_pages_2) + '\t' + str(num_pages_1 == num_pages_2))
            result_row.append(str(num_images_1) + '\t' + str(num_images_2))
            result_row.append(str(len(doc_all_text_1)) + '\t' + str(len(doc_all_text_2)) + '\t' + str(
                len(doc_all_text_2) == len(doc_all_text_1)))
            result_row.append(str(len(doc_all_text_1) / num_pages_1) + '\t' + str(len(doc_all_text_2) / num_pages_2))
            result_row.append(str(num_lines_1) + '\t' + str(num_lines_2))
            result_row.append("\t".join(four_two_lines_list))

            return '\t'.join(result_row)

        else:
            result_row.append(first_columns + '\t' + 'Reject\tPage limit exceeded')
            return '\t'.join(result_row)

    except Exception as E:
        result_row = [first_columns + '\t' + 'Error', str(E)]
        return '\t'.join(result_row)


'''Main'''


# if __name__ == '__main__':
def main_process():
    freeze_support()
    console = Console()
    title = '''# EQUAL MANUAL
    > this tool is designed for the following purposes:\n
        1- specifying the documents that are identical.
        2- specifying the documents that are identical if dates are ignored.
        3- notify the user of possible reach or life cycle change.
        5- notify the user of possible unsearchable documents.
        6- notify the user of image change based on the count of images in document.
        7- it can also be used for grouping based on:
                * number of characters in document.
                * number of lines changed in old and latest.
        '''
    my_copyright = '''# © <EMAIL>'''
    title = Markdown(title)
    my_copyright = Markdown(my_copyright)
    console.print(title)
    console.print(my_copyright)

    # title = '''▶PRESS ENTER TO START.'''
    # title = Markdown(title)
    # console.print(title)
    # input()
    # # read links
    with open("input.txt", 'r') as f:
        links_list = f.readlines()
    output_file_name = f'equal_manual_output_{str(date.today())}.txt'
    if not os.path.exists(output_file_name):
        with open(output_file_name, 'w') as of:
            of.write(links_list[0].removesuffix("\n"))
            of.write('\tSTATUS\tCOMPARE_STATUS\tCHECK_CHANGE\tTRUST_LEVEL\tNOTE\tPAGE_COUNT_1\tPAGE_COUNT_2\tEQUAL'
                     '\tIMAGE_COUNT_1\tIMAGE_COUNT_2\tCHAR_COUNT_1\tCHAR_COUNT_2\tEQUAL\tAVG_CHAR_1\tAVG_CHAR_2'
                     '\tLINES_CHANGED_1\tLINES_CHANGED_2\t1_1\t2_1\t1_2\t2_2\t1_3\t2_3\t1_4\t2_4')
            of.write('\n')

    one_time_count = 500
    total_rows = len(links_list)

    with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
              ncols=100) as progress_bar:

        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    with open(output_file_name, 'a', encoding='utf8') as of:
                        of.write(result)
                        of.write('\n')
                    progress_bar.update(1)
        progress_bar.set_description(f"done".upper())


def text_input(input_file_path, output_file_path):
    with open(input_file_path, 'r',encoding= 'utf8') as input_file:
        input_lines = input_file.readlines()
    with open(output_file_path, 'r',encoding= 'utf8') as output_file:
        output_lines = output_file.readlines()

    last_line_output = output_lines[-1].split('\t')[3:5]
    last_line_input = input_lines[-1].split('\t')[3:5]

    if last_line_input == last_line_output[-1]:
        return 'finished'
    elif '\t'.join(last_line_output) in "".join(input_lines):
        print('\t'.join(last_line_output))
        for line in input_lines:
            if '\t'.join(last_line_output) in line:
                line_index = input_lines.index(line)
                break
        input_lines = input_lines[line_index + 1:]
        with open(input_file_path, 'w',encoding= 'utf8') as input_file:
            input_file.write("".join(input_lines))
        return 'not finished'
    else:
        input_lines = input_lines[1:]
        with open(input_file_path, 'w',encoding= 'utf8') as input_file:
            input_file.write("".join(input_lines))
        return 'not finished'


def start_process():
    output_file_name = f'equal_manual_output_{str(date.today())}.txt'
    os.system('cls' if os.name == 'nt' else 'clear')
    extProc = sp.Popen(['python', '-c', 'from EqualManual_SC import main_process; main_process()'])

    sleep(120)

    while True:
        old_file_size = os.stat(output_file_name).st_size
        sleep(60)
        if os.stat('input.txt').st_size == 0:
            return 'finished'
        if os.stat(output_file_name).st_size == old_file_size:
            extProc.terminate()
            return text_input('input.txt', output_file_name)


if __name__ == '__main__':
    while True:
        process_status = start_process()
        if process_status == 'finished':
            break
    input('finished...')

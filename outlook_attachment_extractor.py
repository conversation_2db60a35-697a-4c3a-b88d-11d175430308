import os
import win32com.client
from datetime import datetime

def get_outlook_folder(folder_name):
    """
    Get a specific Outlook folder by name.

    Args:
        folder_name (str): Name of the folder to find

    Returns:
        Folder object or None if not found
    """
    outlook = win32com.client.Dispatch("Outlook.Application").GetNamespace("MAPI")

    # First try to find the folder in the main folders
    for folder in outlook.Folders:
        if folder.Name == folder_name:
            return folder

        # If not found at the top level, check in Inbox subfolders
        inbox = outlook.GetDefaultFolder(6)  # 6 is the index for Inbox
        for subfolder in inbox.Folders:
            if subfolder.Name == folder_name:
                return subfolder

    # If still not found, search all folders recursively
    return search_folders_recursively(outlook.Folders, folder_name)

def search_folders_recursively(folders, target_folder_name):
    """
    Search for a folder recursively through all Outlook folders.

    Args:
        folders: Collection of Outlook folders to search
        target_folder_name (str): Name of the folder to find

    Returns:
        Folder object or None if not found
    """
    for folder in folders:
        # Check if current folder matches
        if folder.Name == target_folder_name:
            return folder

        # Check subfolders if any
        if folder.Folders.Count > 0:
            subfolder_match = search_folders_recursively(folder.Folders, target_folder_name)
            if subfolder_match:
                return subfolder_match

    return None

def save_attachments(folder_name, save_path=None):
    """
    Save all attachments from emails in the specified Outlook folder.

    Args:
        folder_name (str): Name of the Outlook folder containing emails
        save_path (str): Path where attachments will be saved. If None, creates a folder on desktop.

    Returns:
        int: Number of attachments saved
    """
    # Create save directory if it doesn't exist
    if save_path is None:
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(desktop, f"Outlook_Attachments_{timestamp}")

    if not os.path.exists(save_path):
        os.makedirs(save_path)
        print(f"Created directory: {save_path}")

    # Get the specified folder
    target_folder = get_outlook_folder(folder_name)

    if not target_folder:
        print(f"Folder '{folder_name}' not found in Outlook.")
        return 0

    print(f"Found folder: {target_folder.Name}")
    print(f"Number of items in folder: {target_folder.Items.Count}")

    # Process all emails in the folder
    attachment_count = 0
    email_count = 0

    for item in target_folder.Items:
        email_count += 1

        # Check if the item has attachments
        if item.Attachments.Count > 0:
            # Save each attachment directly to the outer folder
            for attachment in item.Attachments:
                # Handle duplicate filenames by adding a timestamp if needed
                base_filename = attachment.FileName
                file_path = os.path.join(save_path, base_filename)

                # If file already exists, add a timestamp to make it unique
                if os.path.exists(file_path):
                    file_name, file_ext = os.path.splitext(base_filename)
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    new_filename = f"{file_name}_{timestamp}{file_ext}"
                    file_path = os.path.join(save_path, new_filename)

                try:
                    attachment.SaveAsFile(file_path)
                    print(f"Saved: {file_path}")
                    attachment_count += 1
                except Exception as e:
                    print(f"Error saving {attachment.FileName}: {str(e)}")

    print(f"\nSummary:")
    print(f"Processed {email_count} emails")
    print(f"Saved {attachment_count} attachments to {save_path}")

    return attachment_count

if __name__ == "__main__":
    # Folder name to search for
    target_folder_name = "shruthi"

    # You can specify a custom save path or leave as None to create a folder on desktop
    custom_save_path = None  # Example: "C:\\Attachments"

    # Save all attachments
    save_attachments(target_folder_name, custom_save_path)

import pandas as pd
import re
from nltk.corpus import wordnet
import os
from multiprocessing import freeze_support
import concurrent.futures
import re
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from itertools import repeat

def clean_text(text):
    if not isinstance(text, str) or not text.strip():
        return ""
    
    # Remove special characters
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
    # Remove combinations of characters and numbers without spaces
    text = re.sub(r'[a-zA-Z]+[0-9]+[a-zA-Z0-9]*|[0-9]+[a-zA-Z]+[a-zA-Z0-9]*', ' ', text)
    # Remove pure numbers
    text = re.sub(r'\b\d+\b', ' ', text)
    text = re.sub(r'\b[a-zA-Z]{1,2}\b','', text)
    # Normalize spaces
    text = ' '.join(text.split())
    return text.lower().strip()

def search_date_rev(line):
    revision_pattern = re.compile(r'(copyright|document created|creation date|date[\s:_]+|www.|http|created at'
                                r'|downloaded|printed|phone|e-mail|as of|further information|subject to modifications'
                                r'|rev:|rev\.|revised|generated|subject to change|without notice|updated|phone[\s:+]+'
                                r'|technical changes reserved|issued:|©|all rights reserved|fax[\s:+]+|date:)'
                                , flags=re.IGNORECASE)

    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)

    date_result = re.search(date_pattern,line)
    revision_result = re.search(revision_pattern,line)

    if date_result or revision_result:
        return "Found"
    else:
        return ""

def check_patterns(line):
    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)
    
    line = re.sub(date_pattern, '', line)
    note = []
    words_of_interest = [
        'candidate', 'reach', 'svhc', 'substance', 'article 59',
        'echa', 'rohs', 'obsolete', 'discontinued', 'last time buy',
        'not recommended for new design', 'nrnd', 'withdrawn', 'eol',
        'ltb', 'discontinu', 'not recommend', 'end of life',
        'not for new design', 'inactive', 'end-of-life', 'not active',
        'end of service', 'end-of-service'
    ]
    
    # Check for words of interest
    for word in words_of_interest:
        if word in line:
            note.append("Word of interest found")
            break
    
    # Check for part numbers
    if (re.search(r'\d{8,}', line) or 
        re.search(r'[a-zA-Z]+[0-9]+[a-zA-Z0-9]*|[0-9]+[a-zA-Z]+[a-zA-Z0-9]*', line) or
        re.search(r"(part no|article no|item no|item number|article number"
                  r"|part number|item #|ordering infomation|order code|Nexans ref"
                  r"|agina ref|aginode ref|Art No|P/N|pn"
                  r"Product ID|product number)", line)):
        note.append("Part number found")

    return note

def get_synsets(word):
    words = word.split()
    all_syns = set()
    
    if len(words) > 1:
        compound_syns = wordnet.synsets(word.replace(" ", "_"))
        if compound_syns:
            all_syns.update(compound_syns)
        
        for w in words:
            all_syns.update(wordnet.synsets(w))
        
        for i in range(len(words)-1):
            compound = f"{words[i]}_{words[i+1]}"
            all_syns.update(wordnet.synsets(compound))
    else:
        all_syns.update(wordnet.synsets(word))
    
    return list(all_syns)


def map_line_to_feature(cleaned_line, feature_db, mapped_feature_db):
    found_sim_features = []
    if cleaned_line in feature_db:
        return 1, cleaned_line
    
    for keyword_mapping in list(mapped_feature_db[['KEYWORD', 'MAPPING']].values):
                clean_feature = clean_text(str(keyword_mapping[0]).lower().strip())
                if clean_feature:
                  if clean_feature == cleaned_line.lower().strip():
                      return 1, (str(keyword_mapping[-1]).lower().strip())
                  
    feature_db.sort(key = lambda x:len(x), reverse=True)
    for feature in feature_db:
        if feature in cleaned_line:
            found_sim_features.append([len(feature)/len(cleaned_line), feature])
            if len(found_sim_features) == 5:
                break
        if found_sim_features:
          all_sim_features = [found_feature for found_feature in found_sim_features if found_feature[0]>.5]
          if all_sim_features:
            return sum([found_feature[0] for found_feature in all_sim_features])/len(all_sim_features),"|".join([found_feature[-1] for found_feature in all_sim_features])
           
    for keyword_mapping in list(mapped_feature_db[['KEYWORD', 'MAPPING']].values):
            clean_feature = clean_text(str(keyword_mapping[0]).lower().strip())
            if clean_feature:
              if clean_feature in cleaned_line.lower().strip() and len(clean_feature)/len(cleaned_line)>.5:
                  return len(clean_feature)/len(cleaned_line), (str(keyword_mapping[-1]).lower().strip())
              
    return 0, ""

    # line_synset = get_synsets(cleaned_line)
    # for feature in feature_db:
    #     single_sims = []
    #     feature_synset = get_synsets(feature)
    #     if not line_synset:
    #         return 0,"Error_not_Found"
    #     elif not feature_synset:
    #         continue
    #     else:
    #       for sense1, sense2 in product(line_synset, feature_synset):
    #         sim = wordnet.wup_similarity(sense1, sense2)
    #         if sim is not None:
    #             if sim < 0.5:
    #                 continue
    #             elif sim > 0.97 and sense2.name().split('.')[0] in cleaned_line:
    #                 return sim,feature
    #             elif sense2.name().split('.')[0] in cleaned_line:
    #               single_sims.append([sim, feature])
    #               if len(single_sims) > 10:
    #                   break
    # if single_sims:
    #   single_sims.sort(key= lambda x:x[0])
    #   return single_sims[0][0],single_sims[0][-1] 

def read_all_excel_files(folder_path):
    dfs = []
    for file in os.listdir(folder_path):
      if file.endswith('.xlsx'):
        try:
          file_path = os.path.join(folder_path, file)
          df = pd.read_excel(file_path)
          dfs.append(df)
          if dfs:
            combined_df = pd.concat(dfs, ignore_index=True)
        except Exception as e:
          pass
    return combined_df

def main(row, feature_db, mapped_feature_db):
  avg_score = -1
  row = row.strip()
  notes = []
  not_found_lines = []
  empty_lines = []
  part_lines = []
  word_of_interest_lines = []
  fixed_columns = row.split("\t")[:3]
  lines_list = row.split("\t")[3:]
  mapped_features_list = []
  for line in lines_list:
    sim_score_list = []
    if not line:
        continue
    cleaned_line = clean_text(line)

    notes.extend(check_patterns(line))
    if "Part number found" in notes:
        part_lines.append(line)

    if "Word of interest found" in notes:
        word_of_interest_lines.append(line)
    
    if not cleaned_line:
        notes.append("Empty line after cleaning")
        empty_lines.append(line)
        continue
    

    
    if search_date_rev(line):
        continue
    
    sim_score, map_feature= map_line_to_feature(cleaned_line, feature_db, mapped_feature_db)
    if sim_score== 0 and  map_feature== "":
      sim_score_list.append(sim_score)
      not_found_lines.append(line)
      continue
    mapped_features_list.append(map_feature)
    sim_score_list.append(sim_score)
    avg_score = sum(sim_score_list) / len(sim_score_list)
  
  if notes:
    notes.sort()
    final_note = "|".join(list(set(notes)))
  else: 
      final_note = ''
  
  return "\t".join(fixed_columns)+"\t"+"|".join(mapped_features_list)+"\t"+final_note+"\t"+str(avg_score)+"\t"+"|".join(not_found_lines)+"\t"+"|".join(empty_lines)\
          +"\t"+"|".join(part_lines)+"\t"+"|".join(word_of_interest_lines)


if __name__ == '__main__':
  freeze_support()
  console = Console()
  title = '''# BLOCK MATCHER'''
  my_copyright = '''# © <EMAIL>'''
  title = Markdown(title)
  my_copyright = Markdown(my_copyright)
  console.print(title)
  console.print(my_copyright)
  one_time_count = 500

  with open('input.txt', 'r',encoding='utf-8', errors='ignore') as input_file:
      links_list = input_file.readlines()
  
  with open("output.txt", 'w', encoding='utf8') as of:
    of.write('VENDOR_CODE\tCM\tLATEST\tMAPPED FEATURE\tNOTES\tSIM_SCORE\tNOT_FOUND_LINES\tEMPTY_LINES\tPART_LINES\tWORD_OF_INTEREST_LINES')
    of.write('\n')

  total_rows = len(links_list)

  feature_db = pd.read_excel("features_db.xlsx")
  feature_db['Feature_lower'] = feature_db['Feature'].str.lower()
  feature_db = feature_db['Feature_lower'].to_list()
  folder_path = r'map'
  pre_mapped_features_list = read_all_excel_files(folder_path)

  with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
              ncols=100) as progress_bar:

          with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
              for i in range(1, len(links_list), one_time_count):
                  batch_links = links_list[i:i + one_time_count]
                  results = executor1.map(main, batch_links, repeat(feature_db),  repeat(pre_mapped_features_list))
                  for result in results:
                      try:
                          with open("output.txt", 'a', encoding='utf8') as of:
                              of.write(result)
                              of.write('\n')
                      except:
                          pass
                      progress_bar.update(1)
          progress_bar.set_description(f"done".upper())

# Outlook Excel Processor

This script extracts Excel attachments from Outlook emails in a specific folder, processes them to extract specific columns, and combines the data into a single output file.

## Features

- Extracts all attachments from emails in a specified Outlook folder
- Processes all Excel (.xlsx) files to extract specific columns
- Combines the data into a single Excel file
- <PERSON><PERSON> duplicate filenames and missing columns

## Prerequisites

- Windows operating system
- Microsoft Outlook installed
- Python 3.6 or higher
- Required Python packages:
  - `pywin32` - For Outlook interaction
  - `pandas` - For Excel file processing
  - `openpyxl` - For Excel file reading/writing

## Installation

1. Make sure you have Python installed. If not, download and install it from [python.org](https://www.python.org/downloads/).

2. Install the required packages:
   ```
   pip install pywin32 pandas openpyxl
   ```

## Usage

1. Run the script:
   ```
   python outlook_excel_processor.py
   ```

2. The script will:
   - Connect to your Outlook
   - Search for the "shruthi" folder
   - Extract all attachments from emails in that folder
   - Save them to a new folder on your desktop
   - Process all Excel files to extract the following columns:
     - DOCUMENT
     - LATEST
     - COMPARE_STATUS
     - V_COMMENT
     - PART_STATUS
     - DATA_CHANGED
     - CHANGED_FET
   - Combine the data into a single Excel file
   - Save the combined file to your desktop

## Output

The script creates two outputs:

1. A folder containing all attachments from the emails
2. A combined Excel file with the extracted data

The combined Excel file will include all the specified columns from all Excel files, plus an additional column called `SOURCE_FILE` that indicates which file each row came from.

## Customization

If you need to modify the script:

- To change the Outlook folder name, modify the `target_folder_name` variable in the `main()` function
- To extract different columns, modify the `columns_to_extract` list in the `combine_excel_files()` function

## Troubleshooting

- **Folder not found**: Make sure the folder name "shruthi" exists in your Outlook and is spelled correctly
- **No Excel files**: Ensure that the emails in the folder contain Excel attachments
- **Missing columns**: The script will handle missing columns by adding them with empty values
- **Outlook security prompts**: You may need to allow the script to access Outlook when prompted

## How It Works

The script works in two main steps:

1. **Attachment Extraction**:
   - Connects to Outlook using the win32com library
   - Searches for the specified folder
   - Extracts all attachments from emails in that folder
   - Saves them to a folder on your desktop

2. **Excel Processing**:
   - Scans the folder for all Excel (.xlsx) files
   - Opens each file and extracts the specified columns
   - Combines all the data into a single dataframe
   - Saves the combined data to a new Excel file

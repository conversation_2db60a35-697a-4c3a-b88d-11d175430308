:root {
	--primary: #059669;
	--primary-light: #10b981;
	--bg: #ecfdf5;
	--sidebar-bg: #ffffff;
	--card-bg: #ffffff;
	--text: #064e3b;
	--text-light: #047857;
}

body {
	background-color: var(--bg);
	color: var(--text);
}

/* Sidebar Styles */
.sidebar {
	width: 240px;
	background: var(--sidebar-bg);
	padding: 1.5rem;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
	border-radius: 0 24px 24px 0;
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 2px solid var(--primary-light);
}

.logo i {
	font-size: 24px;
	color: var(--primary);
}

.logo span {
	font-size: 20px;
	font-weight: 600;
	color: var(--primary);
}

.nav-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	color: var(--text-light);
	text-decoration: none;
	border-radius: 12px;
	margin-bottom: 8px;
	transition: all 0.3s ease;
}

.nav-item:hover, .nav-item.active {
	background: var(--primary);
	color: white;
}

.nav-item i {
	font-size: 18px;
}

.sidebar-toggle {
	background: var(--primary);
	color: white;
}

/* Main Content Styles */
.main-content {
	margin-left: 240px;
	position: relative;
	padding: 2rem;
}

.main-content.expanded {
	margin-left: 80px;
}

/* Columns Layout */
.columns {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 2rem;
	margin-top: 1rem;
}

.column h2 {
	margin-bottom: 1.5rem;
	color: var(--primary);
	font-weight: 600;
}

/* Card Styles */
.card {
	background: var(--card-bg);
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
	border: 1px solid #e5e7eb;
	border-radius: 16px;
	padding: 1.5rem;
}

.card h3 {
	color: var(--text);
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.card p {
	color: var(--text-light);
}

/* Decorative Shapes */
.main-content::before {
	content: '';
	position: fixed;
	top: 40px;
	right: 40px;
	width: 300px;
	height: 300px;
	background: radial-gradient(circle at center, var(--primary-light) 0%, transparent 70%);
	opacity: 0.1;
	border-radius: 50%;
	z-index: -1;
	animation: float 10s ease-in-out infinite;
}

.main-content::after {
	content: '';
	position: fixed;
	bottom: 40px;
	left: 340px;
	width: 200px;
	height: 200px;
	background: var(--primary);
	opacity: 0.1;
	clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
	z-index: -1;
	animation: spin 30s linear infinite;
}

/* Animations */
@keyframes float {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-20px); }
}

@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
	.columns {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.columns {
		grid-template-columns: 1fr;
	}
}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import memory_profiler as mem_profile\n", "import random\n", "import time\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import col\n", "import os"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JAVA HOME is set to: {java_home}\n", "SPARK_HOME is set to: {spark_home}\n"]}], "source": ["\n", "#Check Java installation\n", "java_home= os.getenv('JAVA_HOME')\n", "if java_home is None:\n", "\tprint(\"JAVA HOME environment variable is not set.\")\n", "else:\n", "\tprint(\"JAVA HOME is set to: {java_home}\")\n", "#Check Spark installation\n", "spark_home =  os.getenv('SPARK_HOME')\n", "if spark_home is None:\n", "\tprint(\"SPARK HOME environment variable is not set.\")\n", "else:\n", "\tprint(\"SPARK_HOME is set to: {spark_home}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SparkSession created successfully!\n"]}], "source": ["try:\n", "\tspark = SparkSession.builder.appName(\"City Population Analysis\").getOrCreate()\n", "\tprint(\"SparkSession created successfully!\")\n", "except Exception as e:\n", "\tprint(f\"Error creating SparkSession: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.csv(r\"C:\\Users\\<USER>\\Downloads\\simplemaps_worldcities_basicv1.77\\worldcities.csv\",\n", "                    header =True, inferSchema = True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+--------------------+--------+---------+--------------------+----+----+----------+\n", "|                city|     lat|      lng|             country|iso2|iso3|population|\n", "+--------------------+--------+---------+--------------------+----+----+----------+\n", "|               Tokyo| 35.6897| 139.6922|               Japan|  JP| JPN|  37732000|\n", "|             Jakarta|  -6.175| 106.8275|           Indonesia|  ID| IDN|  33756000|\n", "|               Delhi|   28.61|    77.23|               India|  IN| IND|  32226000|\n", "|           Guangzhou|   23.13|   113.26|               China|  CN| CHN|  26940000|\n", "|              Mumbai| 19.0761|  72.8775|               India|  IN| IND|  24973000|\n", "|              Manila| 14.5958| 120.9772|         Philippines|  PH| PHL|  24922000|\n", "|            Shanghai| 31.2286| 121.4747|               China|  CN| CHN|  24073000|\n", "|           Sao Paulo|  -23.55| -46.6333|              Brazil|  BR| BRA|  23086000|\n", "|               Seoul|   37.56|   126.99|        Korea, South|  KR| KOR|  23016000|\n", "|         Mexico City| 19.4333| -99.1333|              Mexico|  MX| MEX|  21804000|\n", "|               Cairo| 30.0444|  31.2358|               Egypt|  EG| EGY|  20296000|\n", "|            New York| 40.6943| -73.9249|       United States|  US| USA|  18908608|\n", "|               Dhaka| 23.7639|  90.3889|          Bangladesh|  BD| BGD|  18627000|\n", "|             Beijing| 39.9067| 116.3975|               China|  CN| CHN|  18522000|\n", "|             Kolkata| 22.5675|    88.37|               India|  IN| IND|  18502000|\n", "|             Bangkok| 13.7525| 100.4942|            Thailand|  TH| THA|  18007000|\n", "|            Shenzhen| 22.5415| 114.0596|               China|  CN| CHN|  17619000|\n", "|              Moscow| 55.7558|  37.6172|              Russia|  RU| RUS|  17332000|\n", "|        Buenos Aires|-34.6033| -58.3817|           Argentina|  AR| ARG|  16710000|\n", "|               Lagos|   6.455|   3.3841|             Nigeria|  NG| NGA|  16637000|\n", "|            Istanbul| 41.0136|   28.955|              Turkey|  TR| TUR|  16079000|\n", "|             Karachi|   24.86|    67.01|            Pakistan|  PK| PAK|  15738000|\n", "|           Bangalore| 12.9789|  77.5917|               India|  IN| IND|  15386000|\n", "|    Ho Chi Minh City| 10.7756| 106.7019|             Vietnam|  VN| VNM|  15136000|\n", "|               Osaka| 34.6939| 135.5022|               Japan|  JP| JPN|  15126000|\n", "|             Chengdu|   30.66| 104.0633|               China|  CN| CHN|  14645000|\n", "|              Tehran| 35.6892|  51.3889|                Iran|  IR| IRN|  14148000|\n", "|            Kinshasa| -4.3219|  15.3119|    Congo (Kinshasa)|  CD| COD|  12836000|\n", "|      Rio de Janeiro|-22.9111| -43.2056|              Brazil|  BR| BRA|  12592000|\n", "|             Chennai| 13.0825|   80.275|               India|  IN| IND|  12395000|\n", "|               Xi'an| 34.2611| 108.9422|               China|  CN| CHN|  12328000|\n", "|              Lahore| 31.5497|  74.3436|            Pakistan|  PK| PAK|  12306000|\n", "|           Chongqing| 29.5637| 106.5504|               China|  CN| CHN|  12135000|\n", "|         Los Angeles| 34.1141|-118.4068|       United States|  US| USA|  11922389|\n", "|             Baoding|  38.874|  115.464|               China|  CN| CHN|  11544036|\n", "|              London| 51.5072|  -0.1275|      United Kingdom|  GB| GBR|  11262000|\n", "|               Paris| 48.8567|   2.3522|              France|  FR| FRA|  11060000|\n", "|               Linyi| 35.1038| 118.3564|               China|  CN| CHN|  11018365|\n", "|            Dongguan|  23.021|  113.752|               China|  CN| CHN|  10646000|\n", "|           Hyderabad| 17.3617|  78.4747|               India|  IN| IND|  10494000|\n", "|             Tianjin| 39.1336| 117.2054|               China|  CN| CHN|  10368000|\n", "|                Lima|  -12.06| -77.0375|                Peru|  PE| PER|  10320000|\n", "|               Wuhan| 30.5934| 114.3046|               China|  CN| CHN|  10251000|\n", "|             Nanyang| 32.9987| 112.5292|               China|  CN| CHN|   9713112|\n", "|            Hangzhou|  30.267|  120.153|               China|  CN| CHN|   9523000|\n", "|              <PERSON><PERSON><PERSON>| 23.0214| 113.1216|               China|  CN| CHN|   9498863|\n", "|              Nagoya| 35.1833|    136.9|               Japan|  JP| JPN|   9197000|\n", "|            Tongshan|  34.204|  117.284|               China|  CN| CHN|   9083790|\n", "|              Luanda| -8.8383|  13.2344|              Angola|  AO| AGO|   9051000|\n", "|             Zhoukou|  33.625| 114.6418|               China|  CN| CHN|   9026015|\n", "|             Ganzhou|  25.831|  114.933|               China|  CN| CHN|   8970014|\n", "|        Kuala Lumpur|  3.1478| 101.6953|            Malaysia|  MY| MYS|   8911000|\n", "|                Heze| 35.2343| 115.4796|               China|  CN| CHN|   8795939|\n", "|            Quanzhou| 24.8744| 118.6757|               China|  CN| CHN|   8782285|\n", "|        Johannesburg|-26.2044|  28.0456|        South Africa|  ZA| ZAF|   8500000|\n", "|             Chicago| 41.8375| -87.6866|       United States|  US| USA|   8497759|\n", "|             Nanjing| 32.0608| 118.7789|               China|  CN| CHN|   8422000|\n", "|              Jining| 35.4151| 116.5871|               China|  CN| CHN|   8357897|\n", "|               Hanoi|    21.0|   105.85|             Vietnam|  VN| VNM|   8246600|\n", "|                Pune| 18.5203|  73.8567|               India|  IN| IND|   8231000|\n", "|              Fuyang|   32.89|  115.814|               China|  CN| CHN|   8200264|\n", "|           Ahmedabad| 23.0225|  72.5714|               India|  IN| IND|   8009000|\n", "|              Bogota|  4.7111| -74.0722|            Colombia|  CO| COL|   7968095|\n", "|            Shenyang| 41.8025| 123.4281|               China|  CN| CHN|   7964000|\n", "|       Dar es Salaam| -6.8161|  39.2803|            Tanzania|  TZ| TZA|   7962000|\n", "|            Khartoum|    15.6|     32.5|               Sudan|  SD| SDN|   7869000|\n", "|            Shangqiu|  34.415|  115.656|               China|  CN| CHN|   7816831|\n", "|           Hong Kong|    22.3|    114.2|           Hong Kong|  HK| HKG|   7450000|\n", "|            Cangzhou| 38.3047| 116.8387|               China|  CN| CHN|   7300783|\n", "|              Riyadh| 24.6333|  46.7167|        Saudi Arabia|  SA| SAU|   7237000|\n", "|            Santiago|-33.4372| -70.6506|               Chile|  CL| CHL|   7171000|\n", "|             Xingtai| 37.0717| 114.5048|               China|  CN| CHN|   7111106|\n", "|           Zhumadian|  33.014|  114.022|               China|  CN| CHN|   7008427|\n", "|          Chattogram|  22.335|  91.8325|          Bangladesh|  BD| BGD|   7000000|\n", "|            Surabaya| -7.2458| 112.7378|           Indonesia|  ID| IDN|   6998000|\n", "|           Zhanjiang| 21.2701| 110.3575|               China|  CN| CHN|   6981236|\n", "|               Bijie|  27.284|  105.292|               China|  CN| CHN|   6899636|\n", "|            Yancheng| 33.3482| 120.1626|               China|  CN| CHN|   6709629|\n", "|            Hengyang|  26.894|  112.572|               China|  CN| CHN|   6645243|\n", "|               Zunyi|  27.722|  107.031|               China|  CN| CHN|   6606675|\n", "|            Shaoyang| 27.2395| 111.4679|               China|  CN| CHN|   6563520|\n", "|               Surat| 21.1702|  72.8311|               India|  IN| IND|   6538000|\n", "|            Shangrao| 28.4551| 117.9431|               China|  CN| CHN|   6435300|\n", "|             Xinyang|  32.149|  114.091|               China|  CN| CHN|   6234401|\n", "|              Madrid| 40.4169|  -3.7033|               Spain|  ES| ESP|   6211000|\n", "|             Baghdad| 33.3153|  44.3661|                Iraq|  IQ| IRQ|   6183000|\n", "|             Maoming| 21.6627| 110.9255|               China|  CN| CHN|   6174050|\n", "|             Ji<PERSON>ang|  23.551| 116.3727|               China|  CN| CHN|   6089400|\n", "|               Miami|  25.784| -80.2101|       United States|  US| USA|   6080145|\n", "|           Singapore|     1.3|    103.8|           Singapore|  SG| SGP|   5983000|\n", "|             Houston|  29.786| -95.3885|       United States|  US| USA|   5970127|\n", "|           Lia<PERSON>eng| 36.4559| 115.9852|               China|  CN| CHN|   5952128|\n", "|           Huanggang| 30.4537| 114.8724|               China|  CN| CHN|   5882719|\n", "|              Dalian|    38.9|    121.6|               China|  CN| CHN|   5871474|\n", "|              Dallas| 32.7935| -96.7667|       United States|  US| USA|   5830932|\n", "|             Qingdao| 36.0669| 120.3827|               China|  CN| CHN|   5818255|\n", "|               Yulin|  22.654|  110.181|               China|  CN| CHN|   5796766|\n", "|              Douala|    4.05|      9.7|            Cameroon|  CM| CMR|   5768400|\n", "|              Qujing|  25.491|  103.796|               China|  CN| CHN|   5765775|\n", "|           Nangandao| 35.3036| 113.9268|               China|  CN| CHN|   5708191|\n", "|        Philadelphia| 40.0077| -75.1339|       United States|  US| USA|   5683533|\n", "|              Pudong| 31.2347| 121.5064|               China|  CN| CHN|   5681512|\n", "|             Toronto| 43.7417| -79.3733|              Canada|  CA| CAN|   5647656|\n", "|           Zhengzhou|  34.764|  113.684|               China|  CN| CHN|   5621593|\n", "|              Dezhou|  37.436|  116.359|               China|  CN| CHN|   5611194|\n", "|            Nanchong| 30.8372| 106.1106|               China|  CN| CHN|   5607565|\n", "|               Jinan| 36.6702| 117.0207|               China|  CN| CHN|   5606374|\n", "|                Giza|  29.987|  31.2118|               Egypt|  EG| EGY|   5598402|\n", "|             Nairobi| -1.2864|  36.8172|               Kenya|  KE| KEN|   5545000|\n", "|         Guadalajara| 20.6767|-103.3475|              Mexico|  MX| MEX|   5525000|\n", "|              Ankara|   39.93|    32.85|              Turkey|  TR| TUR|   5503985|\n", "|              Tai'an|  36.202|  117.087|               China|  CN| CHN|   5472217|\n", "|            Langfang| 39.5383| 116.6835|               China|  CN| CHN|   5464087|\n", "|              Dazhou| 31.2093| 107.4678|               China|  CN| CHN|   5385422|\n", "|    Saint Petersburg| 59.9375|  30.3086|              Russia|  RU| RUS|   5384342|\n", "|           Monterrey| 25.6667|   -100.3|              Mexico|  MX| MEX|   5341171|\n", "|      Belo Horizonte|-19.9167| -43.9333|              Brazil|  BR| BRA|   5328000|\n", "|              Suzhou| 33.6333| 116.9683|               China|  CN| CHN|   5324476|\n", "|            Yongzhou|   26.42|  111.613|               China|  CN| CHN|   5289824|\n", "|             Changde|  29.031|  111.699|               China|  CN| CHN|   5279102|\n", "|           Xiangyang|   32.01|  112.122|               China|  CN| CHN|   5260951|\n", "|             Rangoon|  16.795|    96.16|               Burma|  MM| MMR|   5209541|\n", "|             Atlanta| 33.7628|  -84.422|       United States|  US| USA|   5180179|\n", "|          Washington| 38.9047| -77.0163|       United States|  US| USA|   5116378|\n", "|            Zhaotong|  27.338|  103.717|               China|  CN| CHN|   5092611|\n", "|           Zhangzhou|  24.513|  117.647|               China|  CN| CHN|   5054328|\n", "|           Melbourne|-37.8142| 144.9631|           Australia|  AU| AUS|   5031195|\n", "|              Yichun|  27.816|  114.417|               China|  CN| CHN|   5007702|\n", "|              Bozhou| 33.8626| 115.7742|               China|  CN| CHN|   4996844|\n", "|              Suqian| 33.9331| 118.2831|               China|  CN| CHN|   4986192|\n", "|             Abidjan|  5.3167|  -4.0333|       Côte d’Ivoire|  CI| CIV|   4980000|\n", "|               Ji'an| 27.0912| 114.9668|               China|  CN| CHN|   4956600|\n", "|              Guilin|  25.275|  110.296|               China|  CN| CHN|   4931137|\n", "|        Pingdingshan|  33.735| 113.2999|               China|  CN| CHN|   4904701|\n", "|              Berlin|   52.52|   13.405|             Germany|  DE| DEU|   4890363|\n", "|          Alexandria| 31.1975|  29.8925|               Egypt|  EG| EGY|   4870000|\n", "|            Mianyang|  31.468|  104.679|               China|  CN| CHN|   4868243|\n", "|              Sydney|-33.8678|   151.21|           Australia|  AU| AUS|   4840600|\n", "|         Huanglongsi|  34.795|  114.345|               China|  CN| CHN|   4824016|\n", "|           Barcelona| 41.3828|   2.1769|               Spain|  ES| ESP|   4800000|\n", "|            Yuncheng| 35.0267|  111.007|               China|  CN| CHN|   4774508|\n", "|           Cape Town|-33.9253|  18.4239|        South Africa|  ZA| ZAF|   4770313|\n", "|            Changsha|  28.228|  112.939|               China|  CN| CHN|   4766296|\n", "|              Jeddah| 21.5433|  39.1728|        Saudi Arabia|  SA| SAU|   4697000|\n", "|              Weinan| 34.5206|  109.471|               China|  CN| CHN|   4688744|\n", "|            Chenzhou|   25.77|  113.016|               China|  CN| CHN|   4667134|\n", "|            Jiangmen| 22.5789| 113.0815|               China|  CN| CHN|   4630300|\n", "|            Jiujiang|  29.661|  115.954|               China|  CN| CHN|   4600276|\n", "|               Xinpu| 34.5966| 119.2214|               China|  CN| CHN|   4599360|\n", "|               <PERSON>bin|  28.752|  104.643|               China|  CN| CHN|   4588804|\n", "|             Huaihua| 27.5698| 110.0016|               China|  CN| CHN|   4587594|\n", "|            Yangzhou| 32.3912| 119.4363|               China|  CN| CHN|   4559797|\n", "|             Taizhou| 32.4567| 119.9229|               China|  CN| CHN|   4512762|\n", "|             Kunming| 25.0464| 102.7094|               China|  CN| CHN|   4422686|\n", "|              Yiyang| 28.5549|  112.356|               China|  CN| CHN|   4413800|\n", "|           Changchun|  43.897|  125.326|               China|  CN| CHN|   4408154|\n", "|               Lu'an|  31.736|   116.52|               China|  CN| CHN|   4393699|\n", "|        Jiangguanchi| 34.0244| 113.8201|               China|  CN| CHN|   4379998|\n", "|             Meizhou|  24.289|  116.122|               China|  CN| CHN|   4378800|\n", "|              Urumqi| 43.8225|  87.6125|               China|  CN| CHN|   4335017|\n", "|              Suzhou|    31.3| 120.6194|               China|  CN| CHN|   4330000|\n", "|              Boston| 42.3188| -71.0852|       United States|  US| USA|   4328315|\n", "|               Izmir|   38.42|    27.14|              Turkey|  TR| TUR|   4320519|\n", "|             G<PERSON><PERSON>g|  23.112|  109.599|               China|  CN| CHN|   4316262|\n", "|             Shantou|  23.354|  116.682|               China|  CN| CHN|   4312192|\n", "|               Kabul| 34.5253|  69.1783|         Afghanistan|  AF| AFG|   4273156|\n", "|         Xiaoganzhan|  30.918|  113.957|               China|  CN| CHN|   4270371|\n", "|              Bamako| 12.6392|  -8.0028|                Mali|  ML| MLI|   4227569|\n", "|              Luzhou|  28.871|  105.442|               China|  CN| CHN|   4218427|\n", "|               He<PERSON>i| 31.8206| 117.2273|               China|  CN| CHN|   4216940|\n", "|            Hengshui|  37.739|  115.669|               China|  CN| CHN|   4212933|\n", "|           Fortaleza| -3.7275| -38.5275|              Brazil|  BR| BRA|   4167996|\n", "|              Anqing| 30.5318| 117.1153|               China|  CN| CHN|   4165284|\n", "|             Liuzhou| 24.3264| 109.4281|               China|  CN| CHN|   4157934|\n", "|         Zhangjiakou|  40.769|  114.886|               China|  CN| CHN|   4118908|\n", "|            Zhaoqing|   23.05| 112.4667|               China|  CN| CHN|   4113594|\n", "|        Shijiazhuang| 38.0425|   114.51|               China|  CN| CHN|   4098243|\n", "|              Ningbo| 29.8603| 121.6245|               China|  CN| CHN|   4087523|\n", "|             Qiqihar| 47.3549| 123.9182|               China|  CN| CHN|   4067489|\n", "|             Phoenix| 33.5722|-112.0892|       United States|  US| USA|   4064275|\n", "|              Fuzhou|  27.949|  116.358|               China|  CN| CHN|   4047200|\n", "|             Chifeng|  42.257|  118.888|               China|  CN| CHN|   4035967|\n", "|            Xiaoxita|  30.692|  111.287|               China|  CN| CHN|   4017607|\n", "|               Amman| 31.9497|  35.9328|              Jordan|  JO| JOR|   4007526|\n", "|             Chuzhou|  32.256|  118.333|               China|  CN| CHN|   3987054|\n", "|              Linfen|  36.088|  111.519|               China|  CN| CHN|   3976481|\n", "|            Qingyuan|  23.682|  113.056|               China|  CN| CHN|   3969473|\n", "|            Xianyang| 34.3299| 108.7088|               China|  CN| CHN|   3959842|\n", "|               Loudi| 27.6998| 111.9944|               China|  CN| CHN|   3931800|\n", "|             Binzhou|  37.383|  117.971|               China|  CN| CHN|   3928568|\n", "|             Zhuzhou|  27.829|  113.133|               China|  CN| CHN|   3902738|\n", "|             Taiyuan| 37.8704| 112.5497|               China|  CN| CHN|   3875053|\n", "|             Nanning| 22.8167| 108.3275|               China|  CN| CHN|   3837978|\n", "|              Harbin| 45.7576| 126.6409|               China|  CN| CHN|   3830000|\n", "|               Abuja|  9.0667|   7.4833|             Nigeria|  NG| NGA|   3770000|\n", "|            Yokohama| 35.4442| 139.6381|               Japan|  JP| JPN|   3757630|\n", "|              Suihua|  46.654|  126.969|               China|  CN| CHN|   3756167|\n", "|           Zaozhuang| 34.8109| 117.3238|               China|  CN| CHN|   3729140|\n", "|             Detroit| 42.3834| -83.1024|       United States|  US| USA|   3725908|\n", "|              Xiamen| 24.4796| 118.0889|               China|  CN| CHN|   3707090|\n", "|            Neijiang| 29.5802|  105.058|               China|  CN| CHN|   3702847|\n", "|            Montreal| 45.5089| -73.5617|              Canada|  CA| CAN|   3675219|\n", "|              Fuzhou| 26.0743| 119.2964|               China|  CN| CHN|   3671192|\n", "|            Baicheng|  23.903|  106.619|               China|  CN| CHN|   3669400|\n", "|                Wuhu| 31.3526| 118.4331|               China|  CN| CHN|   3644420|\n", "|            Yulinshi| 38.2858| 109.7341|               China|  CN| CHN|   3634750|\n", "|               Medan|  3.5894|  98.6739|           Indonesia|  ID| IDN|   3632000|\n", "|             Wenzhou| 27.9938| 120.6993|               China|  CN| CHN|   3604446|\n", "|           Changzhou|  31.811|  119.974|               China|  CN| CHN|   3601079|\n", "|              Puyang| 35.7627| 115.0292|               China|  CN| CHN|   3598740|\n", "|             Jiaozuo| 35.2157| 113.2419|               China|  CN| CHN|   3590700|\n", "|            Nanchang|  28.683|  115.858|               China|  CN| CHN|   3576547|\n", "|             Seattle| 47.6211|-122.3244|       United States|  US| USA|   3561397|\n", "|              Ibadan|  7.3964|   3.9167|             Nigeria|  NG| NGA|   3552000|\n", "|          Casablanca| 33.5333|  -7.5833|             Morocco|  MA| MAR|   3499000|\n", "|              Kumasi|     6.7|   -1.625|               Ghana|  GH| GHA|   3490030|\n", "|              Deyang|  31.127|  104.398|               China|  CN| CHN|   3456161|\n", "|               Busan|   35.18|  129.075|        Korea, South|  KR| KOR|   3453198|\n", "|              Hohhot|  40.842|  111.749|               China|  CN| CHN|   3446100|\n", "|               Hechi|  24.693|  108.085|               China|  CN| CHN|   3417945|\n", "|             Algiers| 36.7539|   3.0589|             Algeria|  DZ| DZA|   3415811|\n", "|            Tangshan| 39.6294| 118.1739|               China|  CN| CHN|   3399231|\n", "|              Shiyan|  32.629| 110.7987|               China|  CN| CHN|   3398000|\n", "|             Lucknow|   26.85|    80.95|               India|  IN| IND|   3382000|\n", "|             Mashhad| 36.3264|  59.5433|                Iran|  IR| IRN|   3372090|\n", "|       San Francisco| 37.7558|-122.4449|       United States|  US| USA|   3364979|\n", "|             Boankra|  6.6944|  -1.4028|               Ghana|  GH| GHA|   3348000|\n", "|               Dubai| 25.2631|  55.2972|United Arab Emirates|  AE| ARE|   3331420|\n", "|              Anshan|  41.108|  122.994|               China|  CN| CHN|   3325372|\n", "|            Ba<PERSON><PERSON>shi|  34.363|  107.238|               China|  CN| CHN|   3321853|\n", "|             Qinzhou|  21.981|  108.654|               China|  CN| CHN|   3304400|\n", "|             Guiyang|  26.647|   106.63|               China|  CN| CHN|   3299724|\n", "|              Bengbu|  32.917|  117.389|               China|  CN| CHN|   3296408|\n", "|              Bazhou|  31.868|  106.748|               China|  CN| CHN|   3283148|\n", "|             Suining|  30.533|  105.593|               China|  CN| CHN|   3252619|\n", "|                Wuxi|  31.491|  120.312|               China|  CN| CHN|   3245179|\n", "|    <PERSON><PERSON><PERSON>| 32.5833|    73.75|            Pakistan|  PK| PAK|   3219375|\n", "|            Hanzhong| 33.0664| 107.0232|               China|  CN| CHN|   3211462|\n", "|              Putian| 25.4526| 119.0078|               China|  CN| CHN|   3210714|\n", "|           Zhenjiang|  32.188|  119.424|               China|  CN| CHN|   3210418|\n", "|            Guang'an| 30.4564| 106.6326|               China|  CN| CHN|   3205476|\n", "|          Faisalabad| 31.4167|  73.0911|            Pakistan|  PK| PAK|   3203846|\n", "|            Changzhi|  36.195|  113.117|               China|  CN| CHN|   3180884|\n", "|             Tongren| 27.7316| 109.1895|               China|  CN| CHN|   3168800|\n", "|              Leshan|  29.552|  103.766|               China|  CN| CHN|   3160168|\n", "|Santa Cruz de la ...|-17.7892| -63.1975|             Bolivia|  BO| BOL|   3151676|\n", "|         Qinhuangdao| 39.8882| 119.5202|               China|  CN| CHN|   3146300|\n", "|              Jaipur|    26.9|     75.8|               India|  IN| IND|   3073350|\n", "|             Xinzhou|  38.416|  112.734|               China|  CN| CHN|   3067501|\n", "|             Lanzhou| 36.0606| 103.8268|               China|  CN| CHN|   3067141|\n", "|              Wuzhou| 23.4767|  111.279|               China|  CN| CHN|   3061100|\n", "|              Athens| 37.9842|  23.7281|              Greece|  GR| GRC|   3059764|\n", "|           San Diego| 32.8313|-117.1222|       United States|  US| USA|   3046560|\n", "|         Addis Ababa|    9.03|    38.74|            Ethiopia|  ET| ETH|   3041002|\n", "|            Taichung| 24.1439| 120.6794|              Taiwan|  TW| TWN|   3033885|\n", "|             <PERSON><PERSON><PERSON>| 32.6314| 117.0194|               China|  CN| CHN|   3033528|\n", "|      Guatemala City| 14.6133| -90.5353|           Guatemala|  GT| GTM|   3014000|\n", "|         Kuwait City| 29.3697|  47.9783|              Kuwait|  KW| KWT|   3000000|\n", "|            Budapest| 47.4925|  19.0514|             Hungary|  HU| HUN|   2997958|\n", "|            Qincheng| 34.5809| 105.7311|               China|  CN| CHN|   2984659|\n", "|              <PERSON><PERSON><PERSON>|  35.417|  119.527|               China|  CN| CHN|   2968365|\n", "|         Quezon City|   14.65| 121.0475|         Philippines|  PH| PHL|   2960048|\n", "|               Sanaa| 15.3483|  44.2064|               Yemen|  YE| YEM|   2957000|\n", "|            Tashkent| 41.3111|  69.2797|          Uzbekistan|  UZ| UZB|   2956384|\n", "|                Kyiv|   50.45|  30.5233|             Ukraine|  UA| UKR|   2952301|\n", "|             Meishan| 30.0771| 103.8484|               China|  CN| CHN|   2950545|\n", "|             Incheon| 37.4833| 126.6333|        Korea, South|  KR| KOR|   2936117|\n", "|          Birmingham|   52.48|  -1.9025|      United Kingdom|  GB| GBR|   2919600|\n", "|              Ningde| 26.6662| 119.5477|               China|  CN| CHN|   2910000|\n", "|           Zhongshan|  22.517| 113.3925|               China|  CN| CHN|   2909633|\n", "|              Weihai| 37.5133| 122.1205|               China|  CN| CHN|   2906548|\n", "|               Bursa| 40.1833|    29.05|              Turkey|  TR| TUR|   2901396|\n", "|         Minneapolis| 44.9635| -93.2678|       United States|  US| USA|   2892569|\n", "|          Mbuji-Mayi|   -6.15|     23.6|    Congo (Kinshasa)|  CD| COD|   2892000|\n", "|              Haikou| 20.0186| 110.3488|               China|  CN| CHN|   2873358|\n", "|            Tongliao|  43.654|  122.243|               China|  CN| CHN|   2873168|\n", "|            Chaoyang|  41.571|  120.453|               China|  CN| CHN|   2872857|\n", "|              La Paz|-16.4958| -68.1333|             Bolivia|  BO| BOL|   2867504|\n", "|           Pyongyang| 39.0167| 125.7475|        Korea, North|  KP| PRK|   2863000|\n", "|               Tampa| 27.9945| -82.4447|       United States|  US| USA|   2861173|\n", "|            Shaoguan|  24.811|  113.597|               China|  CN| CHN|   2855131|\n", "|              Heyuan| 23.7443| 114.7002|               China|  CN| CHN|   2837686|\n", "|            Brasilia|-15.7939| -47.8828|              Brazil|  BR| BRA|   2817068|\n", "|            <PERSON><PERSON><PERSON><PERSON>|   15.65|  32.4833|               Sudan|  SD| SDN|   2805396|\n", "|              Malang|   -7.98|   112.62|           Indonesia|  ID| IDN|   2795209|\n", "|           Stuttgart| 48.7775|     9.18|             Germany|  DE| DEU|   2787724|\n", "|              Daqing|  46.589|  125.104|               China|  CN| CHN|   2781562|\n", "|                Rome| 41.8933|  12.4828|               Italy|  IT| ITA|   2748109|\n", "|            Brooklyn| 40.6501| -73.9496|       United States|  US| USA|   2736074|\n", "|           Kaohsiung|  22.615| 120.2975|              Taiwan|  TW| TWN|   2733964|\n", "|            Xiangtan| 27.8313| 112.9454|               China|  CN| CHN|   2726181|\n", "|             Longyan|  25.076|  117.017|               China|  CN| CHN|   2723637|\n", "|              Baotou| 40.6213| 109.9532|               China|  CN| CHN|   2709378|\n", "|              Handan|  36.601|  114.487|               China|  CN| CHN|   2708015|\n", "|             Jinzhou|  41.129|  121.148|               China|  CN| CHN|   2703853|\n", "|              Kanpur| 26.4499|  80.3319|               India|  IN| IND|   2701324|\n", "|              Denver|  39.762|-104.8758|       United States|  US| USA|   2687458|\n", "|             Nanping| 26.6418| 118.1774|               China|  CN| CHN|   2680645|\n", "|            Gazipura| 23.9889|   90.375|          Bangladesh|  BD| BGD|   2674697|\n", "|             Shanwei| 22.7872| 115.3756|               China|  CN| CHN|   2672819|\n", "|            Chaozhou|  23.658|  116.622|               China|  CN| CHN|   2656600|\n", "|           Guayaquil|   -2.19| -79.8875|             Ecuador|  EC| ECU|   2650288|\n", "|             Weifang|  36.708|  119.162|               China|  CN| CHN|   2636154|\n", "|             Huai'an|  33.551|  119.113|               China|  CN| CHN|   2632788|\n", "|                Zibo| 36.8138|  118.055|               China|  CN| CHN|   2631647|\n", "|              Ankang| 32.6854|  109.029|               China|  CN| CHN|   2629906|\n", "|           Mogadishu|  2.0392|  45.3419|             Somalia|  SO| SOM|   2610483|\n", "|              Munich| 48.1375|   11.575|             Germany|  DE| DEU|   2606021|\n", "|               Gulou| 26.0865|  119.298|               China|  CN| CHN|   2606000|\n", "|              Taipei| 25.0375| 121.5625|              Taiwan|  TW| TWN|   2603150|\n", "|              Bekasi| -6.2349| 106.9923|           Indonesia|  ID| IDN|   2590257|\n", "|            Damascus| 33.5131|  36.2919|               Syria|  SY| SYR|   2584771|\n", "|             Sanming| 26.2634| 117.6389|               China|  CN| CHN|   2580000|\n", "|           Yangjiang|  21.857|  111.983|               China|  CN| CHN|   2555600|\n", "|             Jiamusi|    46.8|  130.319|               China|  CN| CHN|   2552097|\n", "|               <PERSON><PERSON><PERSON>|  33.583| 114.0109|               China|  CN| CHN|   2544266|\n", "|            Medellin|  6.2308| -75.5906|            Colombia|  CO| COL|   2529403|\n", "|              Dingxi|  35.608|  104.592|               China|  CN| CHN|   2524097|\n", "|            Shaoxing| 30.0511| 120.5833|               China|  CN| CHN|   2521964|\n", "|              Yantai| 37.4646| 121.4478|               China|  CN| CHN|   2511053|\n", "|             Huizhou|  23.112|  114.416|               China|  CN| CHN|   2509243|\n", "|              Lishui|  28.468|  119.923|               China|  CN| CHN|   2507396|\n", "|            Xuanzhou|  30.939|  118.759|               China|  CN| CHN|   2500063|\n", "|          Khowrhesht|  36.205|  49.6872|                Iran|  IR| IRN|   2500000|\n", "|            Mirzapur|  25.146|   82.569|               India|  IN| IND|   2496970|\n", "|              Zigong|  29.339|  104.779|               China|  CN| CHN|   2489256|\n", "|             Hamburg|   53.55|     10.0|             Germany|  DE| DEU|   2484800|\n", "|           Guangyuan|  32.436|  105.844|               China|  CN| CHN|   2484122|\n", "|                Cali|  3.4206| -76.5222|            Colombia|  CO| COL|   2471474|\n", "|            Huangshi| 30.2011|  115.039|               China|  CN| CHN|   2469079|\n", "|              Xining| 36.6224| 101.7804|               China|  CN| CHN|   2467965|\n", "|              Lusaka|-15.4167|  28.2833|              Zambia|  ZM| ZMB|   2467563|\n", "|         Ouagadougou| 12.3686|  -1.5275|        Burkina Faso|  BF| BFA|   2453496|\n", "|             Yaounde|  3.8667|  11.5167|            Cameroon|  CM| CMR|   2440462|\n", "|              Zhuhai| 22.2716| 113.5769|               China|  CN| CHN|   2439585|\n", "|             Huludao|  40.711|  120.837|               China|  CN| CHN|   2434194|\n", "|             Baoshan|  25.112|   99.161|               China|  CN| CHN|   2431211|\n", "|               Mecca| 21.4225|  39.8233|        Saudi Arabia|  SA| SAU|   2427924|\n", "|           Vancouver|   49.25|   -123.1|              Canada|  CA| CAN|   2426160|\n", "|            Lianshan| 40.7523|  120.828|               China|  CN| CHN|   2426000|\n", "|              Beirut| 33.8983|  35.5057|             Lebanon|  LB| LBN|   2421354|\n", "|            Salvador|-12.9747| -38.4767|              Brazil|  BR| BRA|   2418005|\n", "|           Bucharest| 44.4325|  26.1039|             Romania|  RO| ROU|   2412530|\n", "|              Longba| 33.3702| 104.9603|               China|  CN| CHN|   2407272|\n", "|              Nagpur| 21.1497|  79.0806|               India|  IN| IND|   2405665|\n", "|              Queens| 40.7498| -73.7976|       United States|  US| USA|   2405464|\n", "|               Jilin| 43.8519| 126.5481|               China|  CN| CHN|   2396000|\n", "|             Tieling| 42.2237|  123.726|               China|  CN| CHN|   2388294|\n", "|               Accra|    5.55|     -0.2|               Ghana|  GH| GHA|   2388000|\n", "|               Yunfu| 22.9152| 112.0445|               China|  CN| CHN|   2383350|\n", "|         Bekasi Kota| -6.2333|    107.0|           Indonesia|  ID| IDN|   2381053|\n", "|               Daegu| 35.8717| 128.6017|        Korea, South|  KR| KOR|   2376044|\n", "|           Ghaziabad|   28.67|    77.42|               India|  IN| IND|   2375820|\n", "|             Luoyang| 34.6197| 112.4539|               China|  CN| CHN|   2372571|\n", "|            Brisbane|-27.4678| 153.0281|           Australia|  AU| AUS|   2360241|\n", "|              Anshun| 26.2531| 105.9476|               China|  CN| CHN|   2353100|\n", "|           Riverside| 33.9381|-117.3949|       United States|  US| USA|   2332836|\n", "|             Yingkou|  40.625|  122.219|               China|  CN| CHN|   2328582|\n", "|             Colombo|  6.9344|  79.8428|           Sri Lanka|  LK| LKA|   2323826|\n", "|            Yanjiang|  30.129|  104.627|               China|  CN| CHN|   2308631|\n", "|                Baku| 40.3953|  49.8822|          Azerbaijan|  AZ| AZE|   2300500|\n", "|        Antananarivo|  -18.91|   47.525|          Madagascar|  MG| MDG|   2300000|\n", "|          Mudanjiang| 44.5514| 129.6329|               China|  CN| CHN|   2290208|\n", "|             Fukuoka|   33.59| 130.4017|               Japan|  JP| JPN|   2286000|\n", "|              Yan'an|   36.65|  109.494|               China|  CN| CHN|   2282581|\n", "|            Jincheng|  35.491|  112.852|               China|  CN| CHN|   2279146|\n", "|             Nantong|  31.981|  120.894|               China|  CN| CHN|   2261382|\n", "|             Lincang|  23.884|  100.089|               China|  CN| CHN|   2257991|\n", "|                Yuxi|  24.347|  102.527|               China|  CN| CHN|   2249502|\n", "|           Las Vegas| 36.2333|-115.2654|       United States|  US| USA|   2248047|\n", "|             Caracas| 10.4806| -66.9036|           Venezuela|  VE| VEN|   2245744|\n", "|           Tangerang| -6.1703| 106.6403|           Indonesia|  ID| IDN|   2237006|\n", "|              Laibin| 23.7501| 109.2212|               China|  CN| CHN|   2233900|\n", "|               Konya| 37.8667|  32.4833|              Turkey|  TR| TUR|   2232374|\n", "|              Supaul|  26.126|   86.605|               India|  IN| IND|   2229076|\n", "|              Vienna| 48.2083|  16.3725|             Austria|  AT| AUT|   2223236|\n", "|             Esfahan| 32.6447|  51.6675|                Iran|  IR| IRN|   2219343|\n", "|           Baltimore| 39.3051| -76.6144|       United States|  US| USA|   2196524|\n", "|             Shengli|  37.434| 118.6746|               China|  CN| CHN|   2193518|\n", "|             Dandong| 40.1167| 124.3833|               China|  CN| CHN|   2188436|\n", "|           Qinbaling| 35.7278|   107.64|               China|  CN| CHN|   2179716|\n", "|             Gaoping| 30.7824| 106.1281|               China|  CN| CHN|   2174000|\n", "|                Awka|  6.2069|   7.0678|             Nigeria|  NG| NGA|   2171900|\n", "|             Taizhou| 28.6557| 121.4208|               China|  CN| CHN|   2162461|\n", "|           Ma'anshan|  31.669|  118.507|               China|  CN| CHN|   2159930|\n", "|              Harare|-17.8292|  31.0522|            Zimbabwe|  ZW| ZWE|   2150000|\n", "|               Perth|-31.9559| 115.8606|           Australia|  AU| AUS|   2141834|\n", "|           St. Louis| 38.6359| -90.2451|       United States|  US| USA|   2130881|\n", "|          Phnom Penh| 11.5694| 104.9211|            Cambodia|  KH| KHM|   2129371|\n", "|               Depok|  -6.394| 106.8225|           Indonesia|  ID| IDN|   2123349|\n", "|           Stockholm| 59.3294|  18.0686|              Sweden|  SE| SWE|   2121000|\n", "|              Puning|  23.298|  116.166|               China|  CN| CHN|   2118200|\n", "|             Huaibei|  33.956|  116.798|               China|  CN| CHN|   2114276|\n", "|             Kowloon| 22.3167| 114.1833|           Hong Kong|  HK| HKG|   2108419|\n", "|             Cordoba|-31.4167| -64.1833|           Argentina|  AR| ARG|   2106734|\n", "|            Haiphong| 20.8651| 106.6838|             Vietnam|  VN| VNM|   2103500|\n", "|      Zamboanga City|  6.9042| 122.0761|         Philippines|  PH| PHL|   2100000|\n", "|            Chongzuo|  22.377|  107.365|               China|  CN| CHN|   2099400|\n", "|          Rawalpindi|    33.6|  73.0333|            Pakistan|  PK| PAK|   2098231|\n", "|            Portland| 45.5371|  -122.65|       United States|  US| USA|   2095808|\n", "|                Kano|    12.0|   8.5167|             Nigeria|  NG| NGA|   2095000|\n", "|              Yushan|  31.322|  120.985|               China|  CN| CHN|   2092496|\n", "|              Havana| 23.1367| -82.3589|                Cuba|  CU| CUB|   2089532|\n", "|              Hezhou| 24.4042| 111.5672|               China|  CN| CHN|   2072600|\n", "|           Pingliang| 35.5424| 106.6649|               China|  CN| CHN|   2068033|\n", "|            Vadodara|    22.3|     73.2|               India|  IN| IND|   2065771|\n", "|              Manaus| -3.1189| -60.0217|              Brazil|  BR| BRA|   2063547|\n", "|            Qingyang| 24.7816| 118.5517|               China|  CN| CHN|   2061600|\n", "|         San Antonio| 29.4632| -98.5238|       United States|  US| USA|   2058444|\n", "|              Rajkot|    22.3|  70.7833|               India|  IN| IND|   2043000|\n", "|           Shangzhou|  33.868| 109.9244|               China|  CN| CHN|   2041231|\n", "|      Vishakhapatnam| 17.7042|  83.2978|               India|  IN| IND|   2035922|\n", "|           Sanmenxia| 34.7732| 111.2004|               China|  CN| CHN|   2034872|\n", "|            Baicheng|   45.62|  122.838|               China|  CN| CHN|   2032356|\n", "|          Gujranwala| 32.1567|    74.19|            Pakistan|  PK| PAK|   2027001|\n", "|              Aleppo|    36.2|    37.16|               Syria|  SY| SYR|   2003671|\n", "|             Tijuana|  32.525|-117.0333|              Mexico|  MX| MEX|   2002000|\n", "|             Bamenda|  5.9614|  10.1517|            Cameroon|  CM| CMR|   2000000|\n", "|               Minsk|    53.9|  27.5667|             Belarus|  BY| BLR|   1995471|\n", "|              Indore| 22.7167|  75.8472|               India|  IN| IND|   1994397|\n", "|               Karaj| 35.8272|  50.9489|                Iran|  IR| IRN|   1973470|\n", "|             Kananga|  -5.897|  22.4488|    Congo (Kinshasa)|  CD| COD|   1971704|\n", "|            Peshawar| 34.0144|  71.5675|            Pakistan|  PK| PAK|   1970042|\n", "|             Sapporo| 43.0619| 141.3544|               Japan|  JP| JPN|   1959313|\n", "|          Sacramento| 38.5677|-121.4685|       United States|  US| USA|   1957738|\n", "|             Tilburg|   51.55|   5.0833|         Netherlands|  NL| NLD|   1944588|\n", "|           Pingxiang|  27.659|  113.887|               China|  CN| CHN|   1933200|\n", "|            Ecatepec| 19.6097|   -99.06|              Mexico|  MX| MEX|   1929926|\n", "|              Almaty|   43.24|   76.915|          Kazakhstan|  KZ| KAZ|   1916822|\n", "|              Austin| 30.3005| -97.7522|       United States|  US| USA|   1905945|\n", "|            Yinchuan|  38.485|  106.225|               China|  CN| CHN|   1901793|\n", "|              Santos|-23.9369|  -46.325|              Brazil|  BR| BRA|   1897551|\n", "|            Blantyre|-15.7861|  35.0058|              Malawi|  MW| MWI|   1895973|\n", "|               Thane| 19.1972|  72.9722|               India|  IN| IND|   1886941|\n", "|             Orlando| 28.4773|  -81.337|       United States|  US| USA|   1882705|\n", "|              Tainan| 22.9833| 120.1833|              Taiwan|  TW| TWN|   1874686|\n", "|              Xiping|  40.082| 113.2981|               China|  CN| CHN|   1873000|\n", "|              Multan| 30.1978|  71.4697|            Pakistan|  PK| PAK|   1871843|\n", "|          Santa Cruz|   -17.8| -63.1833|             Bolivia|  BO| BOL|   1867673|\n", "|       Port Harcourt|  4.8242|   7.0336|             Nigeria|  NG| NGA|   1865000|\n", "|                Jixi|  45.295|  130.969|               China|  CN| CHN|   1862165|\n", "|              Fushun|  41.881|  123.957|               China|  CN| CHN|   1861372|\n", "|              Warsaw|   52.23|  21.0111|              Poland|  PL| POL|   1860281|\n", "|              Beihai|  21.481|   109.12|               China|  CN| CHN|   1853227|\n", "|               Fuxin|  42.022|   121.67|               China|  CN| CHN|   1819339|\n", "|               Wuwei|  37.929|  102.638|               China|  CN| CHN|   1815059|\n", "|              Siping| 43.1668| 124.3506|               China|  CN| CHN|   1814733|\n", "|            San Juan| 18.3985|  -66.061|         Puerto Rico|  PR| PRI|   1814587|\n", "|              Mersin|    36.8|  34.6333|              Turkey|  TR| TUR|   1814468|\n", "|              Bhopal| 23.2599|  77.4126|               India|  IN| IND|   1798218|\n", "|               Mosul|   36.34|    43.13|                Iraq|  IQ| IRQ|   1792000|\n", "|          Lubumbashi|-11.6647|  27.4794|    Congo (Kinshasa)|  CD| COD|   1786397|\n", "|               Davao|    7.07|    125.6|         Philippines|  PH| PHL|   1776949|\n", "|            Curitiba|-25.4297| -49.2711|              Brazil|  BR| BRA|   1773733|\n", "|            San Jose| 37.3012| -121.848|       United States|  US| USA|   1771413|\n", "|          Shuyangzha| 34.1299| 118.7734|               China|  CN| CHN|   1770000|\n", "|               Adana|    37.0|  35.3213|              Turkey|  TR| TUR|   1765981|\n", "|               Quito|   -0.22| -78.5125|             Ecuador|  EC| ECU|   1763275|\n", "|          Pittsburgh| 40.4397| -79.9763|       United States|  US| USA|   1749091|\n", "|         Brazzaville| -4.2694|  15.2711| Congo (Brazzaville)|  CG| COG|   1733263|\n", "|      Hyderabad City| 25.3792|  68.3683|            Pakistan|  PK| PAK|   1732693|\n", "|          Diyarbakir|   37.91|    40.24|              Turkey|  TR| TUR|   1732396|\n", "|        Indianapolis| 39.7771| -86.1458|       United States|  US| USA|   1729849|\n", "|    Pimpri-Chinchwad| 18.6186|  73.8037|               India|  IN| IND|   1727692|\n", "|              Masqat| 23.5889|  58.4083|                Oman|  OM| OMN|   1720000|\n", "|          Montevideo|-34.8836| -56.1819|             Uruguay|  UY| URY|   1719453|\n", "|            Shuozhou| 39.3317| 112.4329|               China|  CN| CHN|   1714857|\n", "|           Manhattan| 40.7834| -73.9662|       United States|  US| USA|   1694263|\n", "|          Cincinnati| 39.1413|  -84.506|       United States|  US| USA|   1692702|\n", "|         Kansas City| 39.1238| -94.5541|       United States|  US| USA|   1689556|\n", "|               Patna|  25.594|  85.1376|               India|  IN| IND|   1684222|\n", "|         Tegucigalpa| 14.1058| -87.2047|            Honduras|  HN| HND|   1682725|\n", "|             Kampala|  0.3136|  32.5811|              Uganda|  UG| UGA|   1680600|\n", "|           Cleveland| 41.4764| -81.6805|       United States|  US| USA|   1680247|\n", "|             Sanzhou|   30.82|    108.4|               China|  CN| CHN|   1680000|\n", "|            Changshu|  31.656|  120.753|               China|  CN| CHN|   1677050|\n", "|               He<PERSON>e| 50.2401|  127.521|               China|  CN| CHN|   1673899|\n", "|             Conakry|  9.5092| -13.7122|              Guinea|  GN| GIN|   1667864|\n", "|            Ximeicun| 24.9612| 118.3849|               China|  CN| CHN|   1663542|\n", "|       Caloocan City|   14.65|   120.97|         Philippines|  PH| PHL|   1661584|\n", "|            Masvingo|-20.0744|  30.8328|            Zimbabwe|  ZW| ZWE|   1638528|\n", "|             Zhongli|  24.965| 121.2168|              Taiwan|  TW| TWN|   1632616|\n", "|         Novosibirsk|   55.05|    82.95|              Russia|  RU| RUS|   1625631|\n", "|            Bilaspur|   22.09|    82.15|               India|  IN| IND|   1625502|\n", "|            Semarang|   -6.99| 110.4225|           Indonesia|  ID| IDN|   1621384|\n", "|          Jingdezhen| 29.2917| 117.1986|               China|  CN| CHN|   1618979|\n", "|            Ludhiana|   30.91|    75.85|               India|  IN| IND|   1618879|\n", "|            Liaoyang|  41.279|  123.176|               China|  CN| CHN|   1604580|\n", "|        Chengtangcun| 35.0833|   117.15|               China|  CN| CHN|   1603659|\n", "|            Rajshahi| 24.3667|     88.6|          Bangladesh|  BD| BGD|   1600000|\n", "|         Balandougou| 13.3558|  -9.5517|                Mali|  ML| MLI|   1596882|\n", "|            Jiangyin|  31.839|  120.295|               China|  CN| CHN|   1595138|\n", "|            Valencia|   39.47|  -0.3764|               Spain|  ES| ESP|   1595000|\n", "|                Agra|   27.18|    78.02|               India|  IN| IND|   1585704|\n", "|  <PERSON> de los Aldama| 21.1167|-101.6833|              Mexico|  MX| MEX|   1579803|\n", "|              Puebla| 19.0333| -98.1833|              Mexico|  MX| MEX|   1576259|\n", "|            Columbus| 39.9862| -82.9855|       United States|  US| USA|   1572546|\n", "|            Yopougon|  5.3167|  -4.0667|       Côte d’Ivoire|  CI| CIV|   1571065|\n", "|                Hebi|  35.748|  114.297|               China|  CN| CHN|   1569208|\n", "|              Shiraz|   29.61|  52.5425|                Iran|  IR| IRN|   1565572|\n", "|             Madurai|  9.9252|  78.1198|               India|  IN| IND|   1561129|\n", "|              Huzhou| 30.8925| 120.0875|               China|  CN| CHN|   1558826|\n", "|              Tabriz| 38.0814|  46.3006|                Iran|  IR| IRN|   1558693|\n", "|          Jamshedpur| 22.7925|  86.1842|               India|  IN| IND|   1558000|\n", "|           Maracaibo| 10.6333| -71.6333|           Venezuela|  VE| VEN|   1551539|\n", "|               Sofia|    42.7|    23.33|            Bulgaria|  BG| BGR|   1547779|\n", "|            San Jose|  9.9325|   -84.08|          Costa Rica|  CR| CRI|   1543000|\n", "|           <PERSON><PERSON><PERSON><PERSON>| 25.4358|  81.8464|               India|  IN| IND|   1536218|\n", "|           Palembang| -2.9861| 104.7556|           Indonesia|  ID| IDN|   1535952|\n", "|            Kawasaki| 35.5167|    139.7|               Japan|  JP| JPN|   1531646|\n", "|                Kobe|   34.69| 135.1956|               Japan|  JP| JPN|   1521707|\n", "|             Jiaxing|  30.747|  120.756|               China|  CN| CHN|   1518654|\n", "|              Kigali| -1.9439|  30.0594|              Rwanda|  RW| RWA|   1518632|\n", "|         Zhangjiajie|  29.117|  110.479|               China|  CN| CHN|   1517027|\n", "|              Baiyin| 36.5451| 104.1389|               China|  CN| CHN|   1512110|\n", "|             Guiping|    23.4| 110.0833|               China|  CN| CHN|   1511011|\n", "|           Lianjiang| 21.7333| 110.2833|               China|  CN| CHN|   1509400|\n", "|           Jianguang| 28.1958| 115.7833|               China|  CN| CHN|   1507402|\n", "|             Yucheng|   30.01| 103.0415|               China|  CN| CHN|   1507258|\n", "|              Xushan| 30.1667| 121.2333|               China|  CN| CHN|   1502000|\n", "|         Panama City|  8.9833| -79.5167|              Panama|  PA| PAN|   1500189|\n", "|       Nneyi-Umuleri|  6.3333|   6.8333|             Nigeria|  NG| NGA|   1500000|\n", "|             Leizhou| 20.9143| 110.0967|               China|  CN| CHN|   1494700|\n", "|             Gwangju| 35.1653| 126.8486|        Korea, South|  KR| KOR|   1490092|\n", "|        Katako-Kombe|    -3.4|    24.42|    Congo (Kinshasa)|  CD| COD|   1488922|\n", "|              Recife|   -8.05|    -34.9|              Brazil|  BR| BRA|   1488920|\n", "|               Nasik| 19.9975|  73.7898|               India|  IN| IND|   1486053|\n", "|            Valencia| 10.1833|    -68.0|           Venezuela|  VE| VEN|   1484430|\n", "|             Onitsha|  6.1667|   6.7833|             Nigeria|  NG| NGA|   1483000|\n", "|           Abu Dhabi| 24.4667|  54.3667|United Arab Emirates|  AE| ARE|   1483000|\n", "|             Zapopan| 20.7167|   -103.4|              Mexico|  MX| MEX|   1476491|\n", "|             Daejeon|   36.35|  127.385|        Korea, South|  KR| KOR|   1475221|\n", "|               Bronx| 40.8501| -73.8662|       United States|  US| USA|   1472654|\n", "|       Yekaterinburg| 56.8356|  60.6128|              Russia|  RU| RUS|   1468833|\n", "|             Huazhou|  32.688|  112.087|               China|  CN| CHN|   1468061|\n", "|              Jinhua|  29.079|  119.647|               China|  CN| CHN|   1463990|\n", "|               Kyoto| 35.0117| 135.7683|               Japan|  JP| JPN|   1463723|\n", "|           Amsterdam| 52.3728|   4.8936|         Netherlands|  NL| NLD|   1459402|\n", "|              Pizhou|  34.398|   117.89|               China|  CN| CHN|   1458038|\n", "|            Kismaayo| -0.3581|  42.5453|             Somalia|  SO| SOM|   1458000|\n", "|             Yang<PERSON>| 31.8767|  120.556|               China|  CN| CHN|   1447600|\n", "|      Virginia Beach| 36.7335| -76.0435|       United States|  US| USA|   1443020|\n", "|               Dakar| 14.6928| -17.4467|             Senegal|  SN| SEN|   1438725|\n", "|             Goiania|-16.6667|   -49.25|              Brazil|  BR| BRA|   1437237|\n", "|           Charlotte| 35.2083| -80.8303|       United States|  US| USA|   1427444|\n", "|              <PERSON><PERSON><PERSON><PERSON>| 27.7833|  120.625|               China|  CN| CHN|   1424667|\n", "|              Muscat| 23.6139|  58.5922|                Oman|  OM| OMN|   1421409|\n", "|             Kharkiv| 49.9925|  36.2311|             Ukraine|  UA| UKR|   1421125|\n", "|             Wenling| 28.3667| 121.3667|               China|  CN| CHN|   1416199|\n", "|             Gaozhou| 21.9244| 110.8422|               China|  CN| CHN|   1414100|\n", "|           Faridabad| 28.4211|  77.3078|               India|  IN| IND|   1414050|\n", "|              Medina|   24.47|    39.61|        Saudi Arabia|  SA| SAU|   1411599|\n", "|              Khulna| 22.8167|    89.55|          Bangladesh|  BD| BGD|   1400689|\n", "|         Ulaanbaatar| 47.9203| 106.9172|            Mongolia|  MN| MNG|   1396288|\n", "|              Fuqing| 25.7167| 119.3833|               China|  CN| CHN|   1390487|\n", "|             Kayseri| 38.7225|  35.4875|              Turkey|  TR| TUR|   1389680|\n", "|       Tel Aviv-Yafo|   32.08|    34.78|              Israel|  IL| ISR|   1388400|\n", "|             Wuzhong| 37.9978| 106.1986|               China|  CN| CHN|   1382713|\n", "|              Pingdu| 36.7769| 119.9884|               China|  CN| CHN|   1378900|\n", "|           Sangereng| -6.2889| 106.7181|           Indonesia|  ID| IDN|   1378466|\n", "|            Yangquan| 37.8571| 113.5804|               China|  CN| CHN|   1368502|\n", "|              Samsun| 41.2903|  36.3336|              Turkey|  TR| TUR|   1368488|\n", "|               Yutan|  28.278|  112.552|               China|  CN| CHN|   1368117|\n", "|          Copenhagen| 55.6761|  12.5683|             Denmark|  DK| DNK|   1366301|\n", "|            Helsinki| 60.1708|  24.9375|             Finland|  FI| FIN|   1360075|\n", "|              Prague| 50.0875|  14.4214|             Czechia|  CZ| CZE|   1357326|\n", "|               Milan| 45.4669|     9.19|               Italy|  IT| ITA|   1354196|\n", "|            Auckland|-36.8406|   174.74|         New Zealand|  NZ| NZL|   1346091|\n", "|            Santiago| 19.4572| -70.6889|  Dominican Republic|  DO| DOM|   1343423|\n", "|             Chizhou| 30.6654| 117.4916|               China|  CN| CHN|   1342764|\n", "|            Makassar| -5.1619| 119.4362|           Indonesia|  ID| IDN|   1338663|\n", "|            Liangshi|   27.26| 111.7442|               China|  CN| CHN|   1335900|\n", "|        Porto Alegre|-30.0331|   -51.23|              Brazil|  BR| BRA|   1332570|\n", "|           Huangshan| 29.7149| 118.3376|               China|  CN| CHN|   1330565|\n", "|        Barranquilla| 10.9833| -74.8019|            Colombia|  CO| COL|   1326588|\n", "|           Al Basrah|  30.515|    47.81|                Iraq|  IQ| IRQ|   1326564|\n", "|               Benxi| 41.4868|  123.685|               China|  CN| CHN|   1326018|\n", "|             Saitama| 35.8614| 139.6456|               Japan|  JP| JPN|   1325843|\n", "|           Guarulhos|-23.4628| -46.5328|              Brazil|  BR| BRA|   1324781|\n", "|              Juarez|  31.745| -106.485|              Mexico|  MX| MEX|   1321004|\n", "|            Mandalay| 21.9831|  96.0844|               Burma|  MM| MMR|   1319452|\n", "|              Xintai|  35.909|  117.768|               China|  CN| CHN|   1315942|\n", "|              Wusong| 30.9333| 117.7667|               China|  CN| CHN|   1311726|\n", "|             Calgary|   51.05|-114.0667|              Canada|  CA| CAN|   1306784|\n", "|              Meerut|   28.98|    77.71|               India|  IN| IND|   1305429|\n", "|               Yushu|  44.826|   126.55|               China|  CN| CHN|   1304436|\n", "|               Belem| -1.4558| -48.5039|              Brazil|  BR| BRA|   1303389|\n", "|           Kuaidamao| 41.7283| 125.9397|               China|  CN| CHN|   1302778|\n", "|             Huazhou|  21.664| 110.6396|               China|  CN| CHN|   1302100|\n", "|             Baishan|  41.944| 126.4147|               China|  CN| CHN|   1296127|\n", "|            Adelaide|-34.9275|    138.6|           Australia|  AU| AUS|   1295714|\n", "|            Haicheng| 40.8824| 122.6852|               China|  CN| CHN|   1293877|\n", "|           Milwaukee| 43.0642| -87.9675|       United States|  US| USA|   1290940|\n", "|          Providence|  41.823| -71.4187|       United States|  US| USA|   1290271|\n", "|        Jacksonville| 30.3322| -81.6749|       United States|  US| USA|   1288298|\n", "|             Yicheng|   31.36|  119.815|               China|  CN| CHN|   1285785|\n", "|             Cacuaco| -8.8053|  13.2444|              Angola|  AO| AGO|   1279488|\n", "|               Porto| 41.1621|   -8.622|            Portugal|  PT| PRT|   1278210|\n", "|             Rosario|-32.9575| -60.6394|           Argentina|  AR| ARG|   1276000|\n", "|           Canagatan|    18.0|    121.8|         Philippines|  PH| PHL|   1273219|\n", "|              Soweto|-26.2678|  27.8585|        South Africa|  ZA| ZAF|   1271628|\n", "|               Bagam|    1.13| 104.0531|           Indonesia|  ID| IDN|   1269820|\n", "|            Jabalpur| 23.1667|  79.9333|               India|  IN| IND|   1267564|\n", "|             Rucheng|  32.246|  120.591|               China|  CN| CHN|   1267066|\n", "|             Huaiyin| 33.5819|  119.028|               China|  CN| CHN|   1264000|\n", "|              Dublin|   53.35|  -6.2603|             Ireland|  IE| IRL|   1263219|\n", "|               Kazan| 55.7964|  49.1089|              Russia|  RU| RUS|   1259173|\n", "|               <PERSON>an| 26.8552| 100.2259|               China|  CN| CHN|   1253878|\n", "|            Shaoyang|  32.912| 119.8526|               China|  CN| CHN|   1253548|\n", "|           Balikesir| 39.6333|  27.8833|              Turkey|  TR| TUR|   1250610|\n", "|         Comayaguela|  14.098|   -87.21|            Honduras|  HN| HND|   1250000|\n", "|               Laiwu| 36.1833| 117.6667|               China|  CN| CHN|   1248636|\n", "|             Sharjah| 25.3575|  55.3908|United Arab Emirates|  AE| ARE|   1247749|\n", "|            Jingling|  30.664|  113.167|               China|  CN| CHN|   1247400|\n", "|              Kalyan| 19.2502|  73.1602|               India|  IN| IND|   1246381|\n", "|    Nizhniy Novgorod| 56.3269|  44.0075|              Russia|  RU| RUS|   1244254|\n", "|           Yongcheng| 33.9299|   116.45|               China|  CN| CHN|   1240382|\n", "|            Sumedang|   -6.84| 107.9208|           Indonesia|  ID| IDN|   1240000|\n", "|             Can Tho| 10.0333| 105.7833|             Vietnam|  VN| VNM|   1237300|\n", "|            Brussels| 50.8467|   4.3525|             Belgium|  BE| BEL|   1235192|\n", "|               Suwon| 37.2667| 127.0167|        Korea, South|  KR| KOR|   1234300|\n", "|                <PERSON>wu| 29.3069| 120.0753|               China|  CN| CHN|   1234015|\n", "|              Beidao|  34.602|  105.918|               China|  CN| CHN|   1225000|\n", "|         Vasai-Virar| 19.3607|  72.7956|               India|  IN| IND|   1221233|\n", "|           Xiangshui| 26.5964| 104.8314|               China|  CN| CHN|   1221000|\n", "|             Dadukou| 26.5824| 101.7184|               China|  CN| CHN|   1214121|\n", "|            Campinas|-22.9058| -47.0608|              Brazil|  BR| BRA|   1213792|\n", "|           Lingcheng|    22.7|   110.35|               China|  CN| CHN|   1211637|\n", "|        Shuangyashan| 46.6762| 131.1416|               China|  CN| CHN|   1208803|\n", "|             Mombasa|   -4.05|  39.6667|               Kenya|  KE| KEN|   1208333|\n", "|           Najafgarh| 28.6092|  76.9798|               India|  IN| IND|   1203180|\n", "|               Xinyu| 27.8186| 114.9167|               China|  CN| CHN|   1202499|\n", "|                 Qom|   34.64|  50.8764|                Iran|  IR| IRN|   1201158|\n", "|            Hargeysa|  9.5631|  44.0675|             Somalia|  SO| SOM|   1200000|\n", "|              Baidoa|  3.1167|    43.65|             Somalia|  SO| SOM|   1200000|\n", "|             Zhangye| 38.9248| 100.4499|               China|  CN| CHN|   1199515|\n", "|            Varanasi| 25.3189|  83.0128|               India|  IN| IND|   1198491|\n", "|           Hiroshima| 34.3914| 132.4519|               Japan|  JP| JPN|   1198021|\n", "|          Chiang Mai| 18.7953|  98.9986|            Thailand|  TH| THA|   1198000|\n", "|            Belgrade|   44.82|    20.46|              Serbia|  RS| SRB|   1197714|\n", "|           Maiduguri| 11.8333|    13.15|             Nigeria|  NG| NGA|   1197497|\n", "|         Chelyabinsk| 55.1547|  61.3758|              Russia|  RU| RUS|   1196680|\n", "|        Batam Centre|    1.13| 104.0531|           Indonesia|  ID| IDN|   1196396|\n", "|           Rongcheng|  26.218|  104.103|               China|  CN| CHN|   1189813|\n", "|            Mbandaka|  0.0478|  18.2558|    Congo (Kinshasa)|  CD| COD|   1187837|\n", "|                Doha| 25.2867|  51.5333|               Qatar|  QA| QAT|   1186023|\n", "|               Ahvaz| 31.3047|  48.6783|                Iran|  IR| IRN|   1184788|\n", "|            Shymkent| 42.3167|  69.5958|          Kazakhstan|  KZ| KAZ|   1184113|\n", "|             Tripoli| 32.8872|  13.1914|               Libya|  LY| LBY|   1183000|\n", "|            Srinagar|   34.09|    74.79|               India|  IN| IND|   1180570|\n", "|           Nashville| 36.1715| -86.7842|       United States|  US| USA|   1177657|\n", "|            Liaoyuan|  42.888| 125.1447|               China|  CN| CHN|   1176239|\n", "|          Aurangabad|   19.88|    75.32|               India|  IN| IND|   1175116|\n", "|             Cilacap| -7.7167|  109.017|           Indonesia|  ID| IDN|   1174964|\n", "|      Salt Lake City| 40.7776|-111.9311|       United States|  US| USA|   1173879|\n", "|                Omsk| 54.9833|  73.3667|              Russia|  RU| RUS|   1172070|\n", "|              Pikine|   14.75|    -17.4|             Senegal|  SN| SEN|   1170791|\n", "|              Samara| 53.2028|  50.1408|              Russia|  RU| RUS|   1169719|\n", "|             Guankou| 28.1637| 113.6433|               China|  CN| CHN|   1168056|\n", "|      Bandar Lampung|   -5.45| 105.2667|           Indonesia|  ID| IDN|   1166761|\n", "|             Raleigh| 35.8324| -78.6429|       United States|  US| USA|   1163152|\n", "|            Lianyuan|  27.692|  111.664|               China|  CN| CHN|   1162928|\n", "|           Rongcheng| 29.8402| 112.9048|               China|  CN| CHN|   1162770|\n", "|             Dhanbad| 23.7998|  86.4305|               India|  IN| IND|   1162472|\n", "|         <PERSON><PERSON>| 19.7475|   96.115|               Burma|  MM| MMR|   1160242|\n", "|                 Aba|  5.1167|   7.3667|             Nigeria|  NG| NGA|   1160000|\n", "|             Kaiyuan| 36.0193|   113.86|               China|  CN| CHN|   1160000|\n", "|               Zhuji| 29.7167| 120.2333|               China|  CN| CHN|   1157938|\n", "|             Yingtan| 28.2721| 117.0395|               China|  CN| CHN|   1154223|\n", "|            Edmonton| 53.5344|-113.4903|              Canada|  CA| CAN|   1151635|\n", "|             Leiyang| 26.4223| 112.8598|               China|  CN| CHN|   1151554|\n", "|               Ulsan|   35.55| 129.3167|        Korea, South|  KR| KOR|   1150116|\n", "|              Yichun|  47.728|  128.841|               China|  CN| CHN|   1148126|\n", "|          Benin City|  6.3333|   5.6222|             Nigeria|  NG| NGA|   1147188|\n", "|           Bujumbura| -3.3833|  29.3667|             Burundi|  BI| BDI|   1143202|\n", "|              Guyuan|   36.01|  106.257|               China|  CN| CHN|   1142142|\n", "|             Xiantao|  30.328|  113.443|               China|  CN| CHN|   1140100|\n", "|              Rostov| 47.2225|    39.71|              Russia|  RU| RUS|   1137704|\n", "|              Maputo|-25.9667|  32.5833|          Mozambique|  MZ| MOZ|   1133200|\n", "|              Bukavu|    -2.5|  28.8667|    Congo (Kinshasa)|  CD| COD|   1133000|\n", "|            Amritsar|   31.64|    74.86|               India|  IN| IND|   1132383|\n", "|             Shagamu|  6.8333|     3.65|             Nigeria|  NG| NGA|   1132270|\n", "|           Yingchuan| 34.1511| 113.4733|               China|  CN| CHN|   1131896|\n", "|             Aligarh|   27.88|    78.08|               India|  IN| IND|   1131160|\n", "|       Santo <PERSON>| 18.4667|   -69.95|  Dominican Republic|  DO| DOM|   1128678|\n", "|               Bogor| -6.5966| 106.7972|           Indonesia|  ID| IDN|   1127408|\n", "|             Bishkek| 42.8747|  74.6122|          Kyrgyzstan|  KG| KGZ|   1120827|\n", "|             Tbilisi| 41.7225|  44.7925|             Georgia|  GE| GEO|   1118035|\n", "|            Guwahati| 26.1722|  91.7458|               India|  IN| IND|   1116267|\n", "|                 Ufa| 54.7261|  55.9475|              Russia|  RU| RUS|   1115560|\n", "|                 Fes| 34.0433|  -5.0033|             Morocco|  MA| MAR|   1112072|\n", "|              Mwanza| -2.5167|     32.9|            Tanzania|  TZ| TZA|   1104521|\n", "|            <PERSON><PERSON>|   10.95| 106.8167|             Vietnam|  VN| VNM|   1104000|\n", "|            Mexicali| 32.6633|-115.4678|              Mexico|  MX| MEX|   1102342|\n", "|             Sevilla|   37.39|    -5.99|               Spain|  ES| ESP|   1100000|\n", "|               Ikare|  7.5167|     5.75|             Nigeria|  NG| NGA|   1099931|\n", "|             Dongtai|  32.795|  120.519|               China|  CN| CHN|   1098100|\n", "|            Dingzhou|  38.516|   114.99|               China|  CN| CHN|   1095986|\n", "|            Xibeijie| 39.7334|  98.4943|               China|  CN| CHN|   1095947|\n", "|              Tamale|  9.4075|  -0.8533|               Ghana|  GH| GHA|   1095808|\n", "|               Yuyao|   30.05|   121.15|               China|  CN| CHN|   1095000|\n", "|            Hanchuan|  30.652| 113.8274|               China|  CN| CHN|   1092700|\n", "|         Gongzhuling| 43.5053| 124.8224|               China|  CN| CHN|   1092692|\n", "|           N'Djamena|   12.11|    15.05|                Chad|  TD| TCD|   1092066|\n", "|              Ubungo| -6.7889|  39.2056|            Tanzania|  TZ| TZA|   1086912|\n", "|             Cologne| 50.9364|   6.9528|             Germany|  DE| DEU|   1084831|\n", "|         Krasnoyarsk| 56.0089|  92.8719|              Russia|  RU| RUS|   1083865|\n", "|             Zhufeng|    36.0| 119.4167|               China|  CN| CHN|   1081960|\n", "|               Ezhou| 30.3914| 114.8949|               China|  CN| CHN|   1079353|\n", "|              Astana| 51.1472|  71.4222|          Kazakhstan|  KZ| KAZ|   1078362|\n", "|      Nezahualcoyotl| 19.4081| -99.0186|              Mexico|  MX| MEX|   1077208|\n", "|          Nouakchott| 18.0858| -15.9785|          Mauritania|  MR| MRT|   1077169|\n", "|               Haora|   22.58|  88.3294|               India|  IN| IND|   1077075|\n", "|             Tongjin| 37.2333|    127.2|        Korea, South|  KR| KOR|   1076369|\n", "|              Xiashi| 30.5333| 120.6833|               China|  CN| CHN|   1076199|\n", "|             Yerevan| 40.1814|  44.5144|             Armenia|  AM| ARM|   1075800|\n", "|              Ranchi|   23.36|    85.33|               India|  IN| IND|   1073440|\n", "|            Richmond| 37.5295| -77.4756|       United States|  US| USA|   1073223|\n", "|Ciudad Nezahualco...| 19.4006| -99.0148|              Mexico|  MX| MEX|   1072676|\n", "|             Gwalior| 26.2124|  78.1772|               India|  IN| IND|   1069276|\n", "|              Ottawa| 45.4247|  -75.695|              Canada|  CA| CAN|   1068821|\n", "|            Zhongwei| 37.5002| 105.1968|               China|  CN| CHN|   1067336|\n", "|                Oslo| 59.9133|  10.7389|              Norway|  NO| NOR|   1064235|\n", "|              Goyang|   37.65|    126.8|        Korea, South|  KR| KOR|   1061929|\n", "|              Sendai| 38.2682| 140.8694|               Japan|  JP| JPN|   1061177|\n", "|              Mizhou|   35.99| 119.3801|               China|  CN| CHN|   1060000|\n", "|              Xishan| 27.6461|  113.497|               China|  CN| CHN|   1060000|\n", "|        Barquisimeto| 10.0636| -69.3347|           Venezuela|  VE| VEN|   1059092|\n", "|              Hegang| 47.3501|  130.298|               China|  CN| CHN|   1058665|\n", "|          Chandigarh|   30.75|    76.78|               India|  IN| IND|   1055450|\n", "|            Voronezh| 51.6717|  39.2106|              Russia|  RU| RUS|   1051995|\n", "|             Managua| 12.1364| -86.2514|           Nicaragua|  NI| NIC|   1051236|\n", "|            Haldwani|   29.22|    79.52|               India|  IN| IND|   1050000|\n", "|          Vijayawada| 16.5193|  80.6305|               India|  IN| IND|   1048240|\n", "|                Perm|    58.0|  56.3167|              Russia|  RU| RUS|   1048005|\n", "|       Fangchenggang| 21.6867| 108.3547|               China|  CN| CHN|   1046068|\n", "|           Jiancheng| 30.3931| 104.5465|               China|  CN| CHN|   1045900|\n", "|             Cazenga| -8.8214|  13.2911|              Angola|  AO| AGO|   1045722|\n", "|           Kisangani|  0.5167|     25.2|    Congo (Kinshasa)|  CD| COD|   1040000|\n", "|           Shouguang|  36.857|  118.791|               China|  CN| CHN|   1039205|\n", "|             Memphis| 35.1087| -89.9663|       United States|  US| USA|   1037998|\n", "|            Sao Luis| -2.5283| -44.3044|              Brazil|  BR| BRA|   1037775|\n", "|             Jodhpur|   26.28|    73.02|               India|  IN| IND|   1033918|\n", "|              Matola|-25.9667|  32.4667|          Mozambique|  MZ| MOZ|   1032197|\n", "|            Ogbomoso|  8.1333|     4.25|             Nigeria|  NG| NGA|   1032000|\n", "|               Sanya| 18.2533| 109.5036|               China|  CN| CHN|   1031396|\n", "|          Rangapukur|   25.56|    89.25|          Bangladesh|  BD| BGD|   1031388|\n", "|            Ashgabat| 37.9375|    58.38|        Turkmenistan|  TM| TKM|   1030063|\n", "|              Wutong| 30.6333| 120.5333|               China|  CN| CHN|   1029754|\n", "|              Linhai|   28.85| 121.1167|               China|  CN| CHN|   1028813|\n", "|             Denizli| 37.7833|  29.0964|              Turkey|  TR| TUR|   1027782|\n", "|              Niamey|  13.515|   2.1175|               Niger|  NE| NER|   1026848|\n", "|   Shubra al Khaymah| 30.1286|  31.2422|               Egypt|  EG| EGY|   1025569|\n", "|          Wafangdian| 39.6271| 121.9796|               China|  CN| CHN|   1024876|\n", "|          Zhongxiang|  31.169| 112.5853|               China|  CN| CHN|   1022514|\n", "|            Monrovia|  6.3133| -10.8014|             Liberia|  LR| LBR|   1021762|\n", "|       San Cristobal|  7.7667| -72.2333|           Venezuela|  VE| VEN|   1015623|\n", "|           Islamabad| 33.6931|  73.0639|            Pakistan|  PK| PAK|   1014825|\n", "|               Xinyi| 22.3549| 110.9468|               China|  CN| CHN|   1013900|\n", "|             <PERSON><PERSON>| 10.8266| 106.7609|             Vietnam|  VN| VNM|   1013795|\n", "|             Morelia| 19.7683|-101.1894|              Mexico|  MX| MEX|   1011704|\n", "|               Odesa| 46.4775|  30.7326|             Ukraine|  UA| UKR|   1010537|\n", "|              Raipur| 21.2444|  81.6306|               India|  IN| IND|   1010087|\n", "|            Changwon| 35.2708| 128.6631|        Korea, South|  KR| KOR|   1009998|\n", "|            Arequipa|   -16.4| -71.5333|                Peru|  PE| PER|   1008290|\n", "|           Volgograd| 48.7086|  44.5147|              Russia|  RU| RUS|   1004763|\n", "|             Zaoyang|  32.129|  112.772|               China|  CN| CHN|   1004741|\n", "|              Xingyi|  25.092| 104.8955|               China|  CN| CHN|   1004132|\n", "|            Shuizhai| 33.4433| 114.8994|               China|  CN| CHN|   1003698|\n", "|                Kota|   25.18|    75.83|               India|  IN| IND|   1001694|\n", "|              Quetta| 30.1833|     67.0|            Pakistan|  PK| PAK|   1001205|\n", "|            <PERSON><PERSON>| 15.9333| 108.2667|             Vietnam|  VN| VNM|   1000000|\n", "|       <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|  7.2833|  -2.8833|               Ghana|  GH| GHA|   1000000|\n", "|            Bareilly|  28.364|   79.415|               India|  IN| IND|   1000000|\n", "|       Oklahoma City| 35.4676| -97.5136|       United States|  US| USA|    998806|\n", "|            Bordeaux|   44.84|    -0.58|              France|  FR| FRA|    994920|\n", "|           Xingcheng| 24.1347|   115.73|               China|  CN| CHN|    993000|\n", "|             Taixing|  32.159|  120.029|               China|  CN| CHN|    990200|\n", "|            Xinhualu| 34.3964| 113.7402|               China|  CN| CHN|    989900|\n", "|            Lilongwe|-13.9833|  33.7833|              Malawi|  MW| MWI|    989318|\n", "|      Port-au-Prince| 18.5333| -72.3333|               Haiti|  HT| HTI|    987310|\n", "|           Yingcheng|   24.21|  113.401|               China|  CN| CHN|    986400|\n", "|           Al Mi<PERSON>lad| 11.0339|    27.74|               Sudan|  SD| SDN|    985000|\n", "|            <PERSON><PERSON><PERSON>| 22.7689| 111.5697|               China|  CN| CHN|    984100|\n", "|           Pekanbaru|  0.5092| 101.4453|           Indonesia|  ID| IDN|    983356|\n", "|               Natal| -6.9838| -60.2699|              Brazil|  BR| BRA|    980588|\n", "|               Chiba| 35.6073| 140.1064|               Japan|  JP| JPN|    975014|\n", "|              Kirkuk| 35.4667|  44.3167|                Iraq|  IQ| IRQ|    975000|\n", "|            Hartford| 41.7661| -72.6834|       United States|  US| USA|    974684|\n", "|             Huilong|   31.87|  121.703|               China|  CN| CHN|    972525|\n", "|             Wuchuan|  21.441|  110.779|               China|  CN| CHN|    972400|\n", "|              Dnipro| 48.4675|    35.04|             Ukraine|  UA| UKR|    968502|\n", "|         Narayanganj|   23.62|     90.5|          Bangladesh|  BD| BGD|    967951|\n", "|            Gqeberha|-33.9581|     25.6|        South Africa|  ZA| ZAF|    967677|\n", "|              Malaga| 36.7194|    -4.42|               Spain|  ES| ESP|    967250|\n", "|           Marrakech|   31.63|  -8.0089|             Morocco|  MA| MAR|    966987|\n", "|           Cebu City|   10.32|   123.75|         Philippines|  PH| PHL|    964169|\n", "|          Louisville| 38.1663| -85.6485|       United States|  US| USA|    963812|\n", "|              Asmara| 15.3228|   38.925|             Eritrea|  ER| ERI|    963000|\n", "|          Coimbatore| 11.0167|  76.9556|               India|  IN| IND|    959823|\n", "|              Maceio| -9.6658|  -35.735|              Brazil|  BR| BRA|    957916|\n", "|                Nada| 19.5209| 109.5808|               China|  CN| CHN|    954259|\n", "|             Taishan| 22.2486|  112.785|               China|  CN| CHN|    953900|\n", "|            Teresina| -5.0949| -42.8042|              Brazil|  BR| BRA|    953172|\n", "|             Solapur|   17.68|    75.92|               India|  IN| IND|    951558|\n", "|            Freetown|  8.4844| -13.2344|        Sierra Leone|  SL| SLE|    951000|\n", "|  <PERSON>| 18.4855| -69.8734|  Dominican Republic|  DO| DOM|    948855|\n", "|           Krasnodar| 45.0333|  38.9667|              Russia|  RU| RUS|    948827|\n", "|           Vientiane|   17.98|   102.63|                Laos|  LA| LAO|    948487|\n", "|             Tangier| 35.7767|  -5.8039|             Morocco|  MA| MAR|    947952|\n", "|               Anqiu|  36.478|  119.219|               China|  CN| CHN|    947723|\n", "|          Kermanshah| 34.3325|  47.0933|                Iran|  IR| IRN|    946651|\n", "|            Feicheng| 36.1861| 116.7719|               China|  CN| CHN|    946627|\n", "|  Kibanseke Premiere| -4.4419|   15.395|    Congo (Kinshasa)|  CD| COD|    946372|\n", "|       Seberang Jaya|  5.4083| 100.3695|            Malaysia|  MY| MYS|    946092|\n", "|             Buffalo| 42.9018| -78.8487|       United States|  US| USA|    944589|\n", "|               Hubli| 15.3502|  75.1376|               India|  IN| IND|    943788|\n", "|             El Alto|-16.5047| -68.1633|             Bolivia|  BO| BOL|    943000|\n", "|             Cankaya| 39.9244|  32.8856|              Turkey|  TR| TUR|    942553|\n", "|          Hwasu-dong| 37.1997| 126.8314|        Korea, South|  KR| KOR|    941489|\n", "|            Setagaya| 35.6466| 139.6532|               Japan|  JP| JPN|    940071|\n", "|            Kecioren|    40.0|  32.8667|              Turkey|  TR| TUR|    939279|\n", "|           Jerusalem| 31.7789|  35.2256|              Israel|  IL| ISR|    936425|\n", "|      Khartoum North| 15.6333|  32.6333|               Sudan|  SD| SDN|    936349|\n", "|             Meishan| 34.1736|  112.839|               China|  CN| CHN|    936000|\n", "|              <PERSON>shin|  6.5333|     3.35|             Nigeria|  NG| NGA|    935400|\n", "|            Trujillo|  -8.112| -79.0288|                Peru|  PE| PER|    935147|\n", "|          Kitakyushu| 33.8833| 130.8833|               Japan|  JP| JPN|    935084|\n", "|      Aguascalientes|  21.876| -102.296|              Mexico|  MX| MEX|    934424|\n", "|         New Orleans| 30.0687| -89.9288|       United States|  US| USA|    932759|\n", "|          Fort Worth| 32.7817| -97.3474|       United States|  US| USA|    924663|\n", "|               Taihe| 30.8706| 105.3784|               China|  CN| CHN|    924531|\n", "|                Riga| 56.9489|  24.1064|              Latvia|  LV| LVA|    920643|\n", "|              Xin'an|  34.286|  118.355|               China|  CN| CHN|    920628|\n", "|            Taihecun|  45.771|  131.003|               China|  CN| CHN|    920471|\n", "|             Kashgar| 39.4681|  75.9938|               China|  CN| CHN|    920000|\n", "|             Songnam| 37.4333|   127.15|        Korea, South|  KR| KOR|    918771|\n", "|        Trichinopoly| 10.7903|  78.7047|               India|  IN| IND|    916857|\n", "|           Cartagena|    10.4|    -75.5|            Colombia|  CO| COL|    914552|\n", "|            Qingzhou| 36.6853| 118.4796|               China|  CN| CHN|    914047|\n", "|              Naples| 40.8333|    14.25|               Italy|  IT| ITA|    913462|\n", "| Santiago del Estero|-27.7833| -64.2667|           Argentina|  AR| ARG|    911506|\n", "| Naucalpan de Juarez| 19.4753| -99.2378|              Mexico|  MX| MEX|    910407|\n", "|                Daye| 30.0961| 114.9804|               China|  CN| CHN|    909724|\n", "|            Hengzhou| 22.6799| 109.2614|               China|  CN| CHN|    909400|\n", "|              Padang|   -0.95| 100.3531|           Indonesia|  ID| IDN|    909040|\n", "|          Bridgeport| 41.1918| -73.1954|       United States|  US| USA|    908251|\n", "|              Owerri|   5.485|    7.035|             Nigeria|  NG| NGA|    908109|\n", "|            Zhuanghe|  39.681|  122.967|               China|  CN| CHN|    905852|\n", "|      Bobo-Dioulasso| 11.1833|  -4.2833|        Burkina Faso|  BF| BFA|    903887|\n", "|           Ad Dammam| 26.4333|     50.1|        Saudi Arabia|  SA| SAU|    903312|\n", "|              Quzhou| 28.9702| 118.8593|               China|  CN| CHN|    902767|\n", "|             Donetsk| 48.0028|  37.8053|             Ukraine|  UA| UKR|    901645|\n", "|              Ashmun| 30.2941|  31.0342|               Egypt|  EG| EGY|    901191|\n", "|               Bunia|  1.5667|    30.25|    Congo (Kinshasa)|  CD| COD|    900666|\n", "|            Jiaozhou| 36.2647| 120.0334|               China|  CN| CHN|    900500|\n", "|        Campo Grande|-20.4839|  -54.615|              Brazil|  BR| BRA|    897938|\n", "|             Wuchang|  44.924|  127.158|               China|  CN| CHN|    897705|\n", "|         Sao Goncalo|-22.8269| -43.0539|              Brazil|  BR| BRA|    896744|\n", "|         Bucaramanga|  7.1333|    -73.0|            Colombia|  CO| COL|    893040|\n", "|              Merida|   20.97|   -89.62|              Mexico|  MX| MEX|    892363|\n", "|            Yangchun| 22.1704| 111.7916|               China|  CN| CHN|    891800|\n", "|           Osmangazi| 40.1983|    29.06|              Turkey|  TR| TUR|    891250|\n", "|            Esenyurt| 41.0342|    28.68|              Turkey|  TR| TUR|    891120|\n", "|           Moradabad| 28.8319|  78.7764|               India|  IN| IND|    889810|\n", "|              Bangui|  4.3733|  18.5628|Central African R...|  CF| CAF|    889231|\n", "|            Abeokuta|  7.1608|   3.3483|             Nigeria|  NG| NGA|    888924|\n", "|              Cancun| 21.1606| -86.8475|              Mexico|  MX| MEX|    888797|\n", "|            Antipolo| 14.5842| 121.1763|         Philippines|  PH| PHL|    887399|\n", "|            Dengtalu| 36.1386| 114.1066|               China|  CN| CHN|    887000|\n", "|         Taguig City|   14.52|   121.05|         Philippines|  PH| PHL|    886722|\n", "|               Tabuk| 28.3972|  36.5789|        Saudi Arabia|  SA| SAU|    886036|\n", "|            Zhoushan| 29.9856| 122.2074|               China|  CN| CHN|    882932|\n", "|              Tucson| 32.1541|-110.8787|       United States|  US| USA|    878320|\n", "|     As Sulaymaniyah| 35.5572|  45.4356|                Iraq|  IQ| IRQ|    878146|\n", "|           Chihuahua| 28.6369|-106.0769|              Mexico|  MX| MEX|    878062|\n", "|               Klang|  3.0333|   101.45|            Malaysia|  MY| MYS|    878000|\n", "|            Tiruppur| 11.1085|  77.3411|               India|  IN| IND|    877778|\n", "|             Gurgaon|  28.456|   77.029|               India|  IN| IND|    876824|\n", "|           <PERSON><PERSON>| 33.4258|  43.2992|                Iraq|  IQ| IRQ|    874543|\n", "|              Hai'an|  32.535|  120.563|               China|  CN| CHN|    874334|\n", "|             Laiyang| 36.9758| 120.7136|               China|  CN| CHN|    874127|\n", "|             Barinas|  8.6333|    -70.2|           Venezuela|  VE| VEN|    873962|\n", "|           Jalandhar| 31.2569|  75.4432|               India|  IN| IND|    873725|\n", "|           Marseille| 43.2964|     5.37|              France|  FR| FRA|    873076|\n", "|Kaifeng Chengguan...| 34.8519| 114.3481|               China|  CN| CHN|    872000|\n", "|           Eskisehir| 39.7767|  30.5206|              Turkey|  TR| TUR|    871187|\n", "|               Gaomi| 36.3833|   119.75|               China|  CN| CHN|    868715|\n", "|               Lhasa| 29.6534|  91.1719|               China|  CN| CHN|    867891|\n", "|                Ipoh|  4.5972|  101.075|            Malaysia|  MY| MYS|    866772|\n", "|             El Paso| 31.8476|  -106.43|       United States|  US| USA|    865270|\n", "|            Saltillo| 25.4231|-100.9919|              Mexico|  MX| MEX|    864431|\n", "|            Dushanbe| 38.5367|    68.78|          Tajikistan|  TJ| TJK|    863400|\n", "|               Ikeja|     6.6|     3.35|             Nigeria|  NG| NGA|    861300|\n", "|           El Dorado|  24.808| -107.397|              Mexico|  MX| MEX|    858638|\n", "|          Cochabamba|-17.3883| -66.1597|             Bolivia|  BO| BOL|    856198|\n", "|          Portsmouth| 50.8058|  -1.0872|      United Kingdom|  GB| GBR|    855679|\n", "|              Tyumen|   57.15|  65.5333|              Russia|  RU| RUS|    855600|\n", "|         Southampton| 50.9025|  -1.4042|      United Kingdom|  GB| GBR|    855569|\n", "|          Hermosillo| 29.0989|-110.9542|              Mexico|  MX| MEX|    855563|\n", "|                Wuxi|   26.58|  111.841|               China|  CN| CHN|    853197|\n", "|              Leping|  28.978|  117.152|               China|  CN| CHN|    852800|\n", "|            Cheongju| 36.6333| 127.4833|        Korea, South|  KR| KOR|    852018|\n", "|              <PERSON><PERSON><PERSON>| 38.4261|    77.25|               China|  CN| CHN|    851374|\n", "|                Sale| 34.0333|     -6.8|             Morocco|  MA| MAR|    850403|\n", "|              Hailun| 47.4667| 126.9667|               China|  CN| CHN|    850000|\n", "|             Macheng|  31.173|  115.008|               China|  CN| CHN|    849090|\n", "|               Akure|    7.25|    5.195|             Nigeria|  NG| NGA|    847903|\n", "|              Ilorin|     8.5|     4.55|             Nigeria|  NG| NGA|    847582|\n", "|               Erbil| 36.1912|  44.0092|                Iraq|  IQ| IRQ|    846000|\n", "|           Kathmandu|   27.71|    85.32|               Nepal|  NP| NPL|    845767|\n", "|             Saratov| 51.5333|  46.0167|              Russia|  RU| RUS|    845300|\n", "|              Iguacu|  -22.74|   -43.47|              Brazil|  BR| BRA|    844583|\n", "|            Zijinglu| 34.7667| 112.9667|               China|  CN| CHN|    843900|\n", "|               Turin| 45.0792|   7.6761|               Italy|  IT| ITA|    841600|\n", "|                Yuci| 37.6823| 112.7281|               China|  CN| CHN|    840000|\n", "|               Dehui|  44.535|  125.703|               China|  CN| CHN|    839786|\n", "|    Pietermaritzburg|-29.6167|  30.3833|        South Africa|  ZA| ZAF|    839327|\n", "|              Durban|-29.8833|    31.05|        South Africa|  ZA| ZAF|    838634|\n", "|        Bhubaneshwar| 20.2644|  85.8281|               India|  IN| IND|    837737|\n", "|            Denpasar|   -8.65| 115.2167|           Indonesia|  ID| IDN|    834881|\n", "|           Tongchuan|  34.897|  108.945|               China|  CN| CHN|    834437|\n", "|         <PERSON><PERSON>|   -7.12|   -34.88|              Brazil|  BR| BRA|    833932|\n", "|           Samarinda|    -0.5| 117.1378|           Indonesia|  ID| IDN|    831460|\n", "|          Chengxiang|  31.564|  121.174|               China|  CN| CHN|    831113|\n", "|          Rongjiawan| 29.1409| 113.1087|               China|  CN| CHN|    826000|\n", "|          Weichanglu| 37.1792| 119.9333|               China|  CN| CHN|    824708|\n", "|               Sakai| 34.5733| 135.4831|               Japan|  JP| JPN|    824408|\n", "|              Renqiu|  38.686|  116.084|               China|  CN| CHN|    822455|\n", "|               Omaha| 41.2627| -96.0529|       United States|  US| USA|    821345|\n", "|               Xindi|  29.827|  113.476|               China|  CN| CHN|    819446|\n", "|               Wu'an|  36.697|  114.204|               China|  CN| CHN|    819000|\n", "|            Qingping|  34.539|  113.391|               China|  CN| CHN|    817000|\n", "|              Gaoyou| 32.7847| 119.4432|               China|  CN| CHN|    811800|\n", "|Sao Bernardo do C...|   -23.7|   -46.55|              Brazil|  BR| BRA|    810729|\n", "|              Yiyang| 26.4221| 112.3999|               China|  CN| CHN|    810447|\n", "|              Hejian| 38.4466| 116.0995|               China|  CN| CHN|    810306|\n", "|                Puxi| 35.2125|  114.735|               China|  CN| CHN|    809535|\n", "|           Bhayandar|   19.29|    72.85|               India|  IN| IND|    809378|\n", "|            Androtsy|   -24.1|     46.3|          Madagascar|  MG| MDG|    809313|\n", "|            Culiacan| 24.8069|-107.3939|              Mexico|  MX| MEX|    808416|\n", "|              Cucuta|  7.8942| -72.5039|            Colombia|  CO| COL|    806378|\n", "|             Danyang|    32.0|  119.586|               China|  CN| CHN|    806300|\n", "|            Dongyang| 29.2667| 120.2167|               China|  CN| CHN|    804398|\n", "|              Krakow| 50.0614|  19.9372|              Poland|  PL| POL|    804237|\n", "|          Pasig City| 14.5605| 121.0765|         Philippines|  PH| PHL|    803159|\n", "|        Thessaloniki| 40.6403|  22.9347|              Greece|  GR| GRC|    802572|\n", "|           Queretaro| 20.5875|-100.3928|              Mexico|  MX| MEX|    801940|\n", "|             Palermo|  2.8917| -75.4375|            Colombia|  CO| COL|    800000|\n", "|              Xigaze|   29.25|  88.8833|               China|  CN| CHN|    798153|\n", "|               Qamdo|  31.143|    97.17|               China|  CN| CHN|    798067|\n", "|             McAllen| 26.2252| -98.2467|       United States|  US| USA|    797341|\n", "|          Libreville|  0.3903|   9.4542|               Gabon|  GA| GAB|    797003|\n", "|              Seyhan| 36.9831|  35.3328|              Turkey|  TR| TUR|    795012|\n", "|      San Pedro Sula|    15.5| -88.0333|            Honduras|  HN| HND|    793835|\n", "|             Niigata| 37.9161| 139.0364|               Japan|  JP| JPN|    790646|\n", "|           Hempstead| 40.6629| -73.6089|       United States|  US| USA|    789763|\n", "|               Leeds| 53.7975|  -1.5436|      United Kingdom|  GB| GBR|    789194|\n", "|           Hamamatsu| 34.7108| 137.7275|               Japan|  JP| JPN|    788211|\n", "|        Pointe-Noire| -4.7975|  11.8503| Congo (Brazzaville)|  CG| COG|    787799|\n", "|          Xiangxiang| 27.7186| 112.5502|               China|  CN| CHN|    787216|\n", "|          Birmingham| 33.5279| -86.7971|       United States|  US| USA|    782111|\n", "|           Chaohucun| 31.6244| 117.8902|               China|  CN| CHN|    782000|\n", "|             Bucheon|    37.5| 126.7833|        Korea, South|  KR| KOR|    781119|\n", "|             Lubango|-14.9167|     13.5|              Angola|  AO| AGO|    776249|\n", "|                Homs| 34.7308|  36.7094|               Syria|  SY| SYR|    775404|\n", "|              Bilbao| 43.2569|  -2.9236|               Spain|  ES| ESP|    775000|\n", "|             Zouping|  36.863|  117.743|               China|  CN| CHN|    774517|\n", "|           Frankfurt| 50.1106|   8.6822|             Germany|  DE| DEU|    773068|\n", "|     San Luis <PERSON>| 22.1511|-100.9761|              Mexico|  MX| MEX|    772828|\n", "|                Dali| 25.6065| 100.2676|               China|  CN| CHN|    771128|\n", "|              Fuyang| 30.0553|   119.95|               China|  CN| CHN|    771000|\n", "|             Khujand| 40.2833|  69.6333|          Tajikistan|  TJ| TJK|    770000|\n", "|               Korla| 41.7259|  86.1746|               China|  CN| CHN|    770000|\n", "|         Albuquerque| 35.1054|-106.6465|       United States|  US| USA|    769986|\n", "|             Hamhung| 39.9167| 127.5333|        Korea, North|  KP| PRK|    768551|\n", "|             Erzurum| 39.9086|  41.2769|              Turkey|  TR| TUR|    767848|\n", "|              Zagreb| 45.8131|  15.9775|             Croatia|  HR| HRV|    767131|\n", "|             Al `Ayn| 24.2075|  55.7447|United Arab Emirates|  AE| ARE|    766936|\n", "|              Songzi|  30.174|  111.757|               China|  CN| CHN|    765911|\n", "|             Patiala|   30.34|    76.38|               India|  IN| IND|    763280|\n", "|               Laixi| 36.8667| 120.5333|               China|  CN| CHN|    762900|\n", "|             Zhongba|  31.771|  104.755|               China|  CN| CHN|    762140|\n", "|          Bahawalpur| 29.3956|  71.6836|            Pakistan|  PK| PAK|    762111|\n", "|            Qingnian| 36.8494| 115.7061|               China|  CN| CHN|    760781|\n", "|              Kaduna| 10.5167|   7.4333|             Nigeria|  NG| NGA|    760084|\n", "|            Winnipeg| 49.8844| -97.1464|              Canada|  CA| CAN|    758515|\n", "|             Trabzon|  41.005|  39.7225|              Turkey|  TR| TUR|    758237|\n", "|           Guangshui|  31.617|  113.826|               China|  CN| CHN|    755910|\n", "|          Baardheere|  2.3333|  42.2833|             Somalia|  SO| SOM|    755500|\n", "|           Shishgarh|   28.72|    79.32|               India|  IN| IND|    753815|\n", "|              Nerima| 35.7356| 139.6517|               Japan|  JP| JPN|    752608|\n", "|              Sizhan| 38.9846| 106.3828|               China|  CN| CHN|    751389|\n", "|      Ciudad Guayana|  8.3667|   -62.65|           Venezuela|  VE| VEN|    751331|\n", "|               Natal| -5.7833|    -35.2|              Brazil|  BR| BRA|    751300|\n", "|             Lichuan|  30.291| 108.9364|               China|  CN| CHN|    750670|\n", "|             Licheng| 31.4306| 119.4788|               China|  CN| CHN|    749522|\n", "|         <PERSON>|-23.6572| -46.5333|              Brazil|  BR| BRA|    748919|\n", "|              Ota-ku| 35.5614| 139.7161|               Japan|  JP| JPN|    748081|\n", "|           Gaalkacyo|  6.7697|  47.4308|             Somalia|  SO| SOM|    745000|\n", "+--------------------+--------+---------+--------------------+----+----+----------+\n", "only showing top 1000 rows\n", "\n"]}], "source": ["df.show(1000)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+----------------+--------+--------+----------------+----+----+----------+\n", "|            city|     lat|     lng|         country|iso2|iso3|population|\n", "+----------------+--------+--------+----------------+----+----+----------+\n", "|         Jakarta|  -6.175|106.8275|       Indonesia|  ID| IDN|  33756000|\n", "|       Guangzhou|   23.13|  113.26|           China|  CN| CHN|  26940000|\n", "|          Mumbai| 19.0761| 72.8775|           India|  IN| IND|  24973000|\n", "|          Manila| 14.5958|120.9772|     Philippines|  PH| PHL|  24922000|\n", "|     Mexico City| 19.4333|-99.1333|          Mexico|  MX| MEX|  21804000|\n", "|         Kolkata| 22.5675|   88.37|           India|  IN| IND|  18502000|\n", "|         Bangkok| 13.7525|100.4942|        Thailand|  TH| THA|  18007000|\n", "|        Shenzhen| 22.5415|114.0596|           China|  CN| CHN|  17619000|\n", "|           Lagos|   6.455|  3.3841|         Nigeria|  NG| NGA|  16637000|\n", "|       Bangalore| 12.9789| 77.5917|           India|  IN| IND|  15386000|\n", "|Ho Chi Minh City| 10.7756|106.7019|         Vietnam|  VN| VNM|  15136000|\n", "|        Kinshasa| -4.3219| 15.3119|Congo (Kinshasa)|  CD| COD|  12836000|\n", "|  Rio de Janeiro|-22.9111|-43.2056|          Brazil|  BR| BRA|  12592000|\n", "|         Chennai| 13.0825|  80.275|           India|  IN| IND|  12395000|\n", "|        Dongguan|  23.021| 113.752|           China|  CN| CHN|  10646000|\n", "|       Hyderabad| 17.3617| 78.4747|           India|  IN| IND|  10494000|\n", "|            Lima|  -12.06|-77.0375|            Peru|  PE| PER|  10320000|\n", "|          <PERSON><PERSON><PERSON>| 23.0214|113.1216|           China|  CN| CHN|   9498863|\n", "|          Luanda| -8.8383| 13.2344|          Angola|  AO| AGO|   9051000|\n", "|    Kuala Lumpur|  3.1478|101.6953|        Malaysia|  MY| MYS|   8911000|\n", "+----------------+--------+--------+----------------+----+----+----------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["tropic_of_cancer =23.43663\n", "tropic_of_capicorn = -23.43663\n", "filtered_df = df.filter((col(\"lat\") >= tropic_of_capicorn) & (col(\"lat\") <= tropic_of_cancer))\n", "filtered_df.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["<function Row.index(value, start=0, stop=9223372036854775807, /)>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["ordered_df = filtered_df.orderBy(col(\"population\").desc())\n", "largest_pop_city = ordered_df.first()\n", "largest_pop_city.index"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SparkSession created successfully!\n"]}], "source": ["try:\n", "\tspark = SparkSession.builder.appName(\"City Population Analysis\").getOrCreate()\n", "\tprint(\"SparkSession created successfully!\")\n", "except Exception as e:\n", "\tprint(f\"Error creating SparkSession: {e}\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"ename": "Py4JJavaError", "evalue": "An error occurred while calling o230.jdbc.\n: java.lang.ClassNotFoundException: com.microsoft.sqlserver.jdbc.SQLServerDriver\r\n\tat java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445)\r\n\tat java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:593)\r\n\tat java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.DriverRegistry$.register(DriverRegistry.scala:46)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.$anonfun$driverClass$1(JDBCOptions.scala:103)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.$anonfun$driverClass$1$adapted(JDBCOptions.scala:103)\r\n\tat scala.Option.foreach(Option.scala:407)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.<init>(JDBCOptions.scala:103)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.<init>(JDBCOptions.scala:41)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JdbcRelationProvider.createRelation(JdbcRelationProvider.scala:34)\r\n\tat org.apache.spark.sql.execution.datasources.DataSource.resolveRelation(DataSource.scala:346)\r\n\tat org.apache.spark.sql.DataFrameReader.loadV1Source(DataFrameReader.scala:229)\r\n\tat org.apache.spark.sql.DataFrameReader.$anonfun$load$2(DataFrameReader.scala:211)\r\n\tat scala.Option.getOrElse(Option.scala:189)\r\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:211)\r\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:172)\r\n\tat org.apache.spark.sql.DataFrameReader.jdbc(DataFrameReader.scala:249)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:75)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:52)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\r\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)\r\n\tat py4j.Gateway.invoke(Gateway.java:282)\r\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\r\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\r\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\r\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mPy4JJavaError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[1;32mIn[42], line 8\u001b[0m\n\u001b[0;32m      2\u001b[0m properties \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEUROPE\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m156628\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpassword\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdriver\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcom.microsoft.sqlserver.jdbc.SQLServerDriver\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m      6\u001b[0m }\n\u001b[0;32m      7\u001b[0m table_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCode_Backlog\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m----> 8\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mspark\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjdbc\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtable_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproperties\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproperties\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\sql\\readwriter.py:946\u001b[0m, in \u001b[0;36mDataFrameReader.jdbc\u001b[1;34m(self, url, table, column, lowerBound, upperBound, numPartitions, predicates, properties)\u001b[0m\n\u001b[0;32m    944\u001b[0m     jpredicates \u001b[38;5;241m=\u001b[39m utils\u001b[38;5;241m.\u001b[39mtoJArray(gateway, gateway\u001b[38;5;241m.\u001b[39mjvm\u001b[38;5;241m.\u001b[39mjava\u001b[38;5;241m.\u001b[39mlang\u001b[38;5;241m.\u001b[39mString, predicates)\n\u001b[0;32m    945\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_df(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jreader\u001b[38;5;241m.\u001b[39mjdbc(url, table, jpredicates, jprop))\n\u001b[1;32m--> 946\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_df(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jreader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjdbc\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjprop\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\py4j\\java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[1;34m(self, *args)\u001b[0m\n\u001b[0;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[0;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[0;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[1;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[0;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyspark\\errors\\exceptions\\captured.py:179\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[1;34m(*a, **kw)\u001b[0m\n\u001b[0;32m    177\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdeco\u001b[39m(\u001b[38;5;241m*\u001b[39ma: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Any:\n\u001b[0;32m    178\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 179\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    180\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m Py4JJavaError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    181\u001b[0m         converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python311\\site-packages\\py4j\\protocol.py:326\u001b[0m, in \u001b[0;36mget_return_value\u001b[1;34m(answer, gateway_client, target_id, name)\u001b[0m\n\u001b[0;32m    324\u001b[0m value \u001b[38;5;241m=\u001b[39m OUTPUT_CONVERTER[\u001b[38;5;28mtype\u001b[39m](answer[\u001b[38;5;241m2\u001b[39m:], gateway_client)\n\u001b[0;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m answer[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m==\u001b[39m REFERENCE_TYPE:\n\u001b[1;32m--> 326\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JJavaError(\n\u001b[0;32m    327\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[0;32m    328\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name), value)\n\u001b[0;32m    329\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    330\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m Py4JError(\n\u001b[0;32m    331\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error occurred while calling \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m. Trace:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{3}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39m\n\u001b[0;32m    332\u001b[0m         \u001b[38;5;28mformat\u001b[39m(target_id, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m, name, value))\n", "\u001b[1;31mPy4JJavaError\u001b[0m: An error occurred while calling o230.jdbc.\n: java.lang.ClassNotFoundException: com.microsoft.sqlserver.jdbc.SQLServerDriver\r\n\tat java.base/java.net.URLClassLoader.findClass(URLClassLoader.java:445)\r\n\tat java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:593)\r\n\tat java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.DriverRegistry$.register(DriverRegistry.scala:46)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.$anonfun$driverClass$1(JDBCOptions.scala:103)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.$anonfun$driverClass$1$adapted(JDBCOptions.scala:103)\r\n\tat scala.Option.foreach(Option.scala:407)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.<init>(JDBCOptions.scala:103)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JDBCOptions.<init>(JDBCOptions.scala:41)\r\n\tat org.apache.spark.sql.execution.datasources.jdbc.JdbcRelationProvider.createRelation(JdbcRelationProvider.scala:34)\r\n\tat org.apache.spark.sql.execution.datasources.DataSource.resolveRelation(DataSource.scala:346)\r\n\tat org.apache.spark.sql.DataFrameReader.loadV1Source(DataFrameReader.scala:229)\r\n\tat org.apache.spark.sql.DataFrameReader.$anonfun$load$2(DataFrameReader.scala:211)\r\n\tat scala.Option.getOrElse(Option.scala:189)\r\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:211)\r\n\tat org.apache.spark.sql.DataFrameReader.load(DataFrameReader.scala:172)\r\n\tat org.apache.spark.sql.DataFrameReader.jdbc(DataFrameReader.scala:249)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:75)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:52)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)\r\n\tat py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:374)\r\n\tat py4j.Gateway.invoke(Gateway.java:282)\r\n\tat py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)\r\n\tat py4j.commands.CallCommand.execute(CallCommand.java:79)\r\n\tat py4j.ClientServerConnection.waitForCommands(ClientServerConnection.java:182)\r\n\tat py4j.ClientServerConnection.run(ClientServerConnection.java:106)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\n"]}], "source": ["url = \"*************************************************\"\n", "properties = {\n", "    \"user\": r\"EUROPE\\\\156628\",\n", "    \"password\": \"\",\n", "    \"driver\": \"com.microsoft.sqlserver.jdbc.SQLServerDriver\"\n", "}\n", "table_name = \"Code_Backlog\"\n", "df = spark.read.jdbc(url=url, table=table_name, properties=properties)\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["list_x = [1, 2, 3, 4]\n", "x = 3\n", "for _ in range(len(list_x)):\n", "  if x == list_x[_]:\n", "    index = _\n", "print(index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def\ttictoc(func):\n", "\tdef wrapper(num_people):\n", "\t\tt1 = time.time()\n", "\t\tm1 = mem_profile.memory_usage()\n", "\t\tfunc(num_people)\n", "\t\tt2 = time.time()-t1\n", "\t\tm2 = - m1[0] + mem_profile.memory_usage()[0]\n", "\t\tprint(f'{func.__name__} ran in {t2} seconds and took total of {m2} Mb')\n", "\t\n", "\treturn wrapper\n", "\n", "\n", "\n", "names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']\n", "majors = ['Math', 'Engineering', 'CompSci', 'Arts', 'Business']\n", "\n", "\n", "@tictoc\n", "def people_list(num_people):\n", "    result = []\n", "    for i in range(num_people):\n", "        person = {\n", "                    'id': i,\n", "                    'name': random.choice(names),\n", "                    'major': random.choice(majors)\n", "                }\n", "        result.append(person)\n", "    return result\n", "\n", "@tictoc\n", "def people_generator(num_people):\n", "    for i in range(num_people):\n", "        person = {\n", "                    'id': i,\n", "                    'name': random.choice(names),\n", "                    'major': random.choice(majors)\n", "                }\n", "        yield person\n", "\n", "\n", "# people_list(1000000)\n", "# people_generator(1000000)\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Built Into Tomorrow Benefits •\t Operating\ttemperature\trange\tof\t−55°C\tto\t+150°C\t •\t Capacitance\tofferings\tranging\tfrom\t1.0pF\tto\t0.10μF • EIA 0603, 0805, 1206, 1210, 1812 and 2220 case sizes • DC voltage ratings of 500V, 630V, 1 KV, 1.5 KV and 2 KV • Extremely low ESR and ESL • High thermal stability • High ripple current capability • No capacitance shift with voltage • Negligible capacitance shift with respect to temperature  • No piezoelectric noise • Lead (Pb)-Free, RoHS and REACH compliant Applications • High frequency power converters • Wide bandgap (WBG), silicon carbide (SiC) and gallium  nitride (GaN) systems • Snubber (high dV/dT) • Resonant circuits (LLC, Wireless Charging, etc.) • Timing • Filtering Surface Mount Multilayer Ceramic Chip Capacitors (SMD MLCCs) High Voltage, High Temperature 150°C, X8G Dielectric,  500 – 2,000 VDC (Commercial & Automotive Grade) Lead-free Overview KEMET’s\tX8G\tHV\t\tClass\tI\tdielectric\tfeatures\ta\t150°C\t maximum operating temperature, offering the latest in high  temperature dielectric technology and reliability for extreme  temperature applications and under the hood applications.  X8G exhibits no change in capacitance with respect to  voltage and boasts a minimal change in capacitance  with reference to ambient temperature. It is a suitable  replacement for higher capacitance and larger footprint  devices that fail to offer capacitance stability. Capacitance  change\tis\tlimited\tto\t±30ppm/°C\tfrom\t-55°C\tto\t+150°C. Driven by the demand for a more robust and reliable  component, X8G dielectric capacitors were developed  for critical applications where reliability and capacitance  stability at higher operating temperatures are a concern. These capacitors are widely used in automotive for under  the hood and harsh environment as well as general high  temperature applications.  In addition to commercial grade, automotive grade devices  are available and meet Automotive Electronics Council’s  AEC-Q200\tqualification\trequirements.\tAlso\tavailable\t with\tflexible\ttermination\ttechnology\twhich\tinhibits\tthe\t transfer of board stress to the rigid ceramic body, therefore  mitigating\tflex\tcracks\twhich\tcan\tresult\tin\tlow\tIR\tor\tshort\t circuit failures.\n"]}], "source": ["import fitz\n", "import re\n", "doc = fitz.open(r\"\\\\**************\\pdfs2\\2022\\4\\3\\2\\6\\49\\478131\\kmt_\\manual\\c2220c272fdtactu.pdf\")\n", "\n", "print(\" \".join(re.sub(' +', ' ', doc[0].get_text(\"text\")).strip().split('\\n')[3:]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}
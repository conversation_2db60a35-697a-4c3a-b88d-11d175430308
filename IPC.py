from PyPDF2 import PdfReader
from lxml import etree
import os
import base64
import re
import sys
import argparse
import json

def extract_xfa_data(pdf_path, verbose=False):
    """
    Extract XFA data from a dynamic PDF form.

    Args:
        pdf_path (str): Path to the PDF file
        verbose (bool): Whether to print detailed information

    Returns:
        dict: Dictionary containing form field names and values

    Raises:
        FileNotFoundError: If PDF file doesn't exist
        ValueError: If no XFA data found in PDF
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        reader = PdfReader(pdf_path)

        if verbose:
            print(f"PDF Version: {reader.pdf_header}")
            print(f"Number of Pages: {len(reader.pages)}")

        # Check if AcroForm exists
        if "/AcroForm" not in reader.trailer["/Root"]:
            if verbose:
                print("PDF does not contain AcroForm. Checking for alternative form structures...")
                # Try to extract form fields directly
                fields = extract_acroform_fields(reader)
                if fields:
                    return fields
            raise ValueError("PDF does not contain AcroForm")

        acroform = reader.trailer["/Root"]["/AcroForm"]
        if verbose:
            print("AcroForm found in PDF")
            print(f"AcroForm keys: {list(acroform.keys())}")

        # First try to extract regular form fields
        fields = extract_acroform_fields(reader)
        if fields and verbose:
            print(f"Found {len(fields)} regular form fields")

        # Then check for XFA data
        xfa = acroform.get("/XFA")

        if not xfa:
            if verbose:
                print("No XFA data found in PDF. Using regular form fields if available.")
            if fields:
                return fields
            raise ValueError("No XFA data or regular form fields found in PDF")

        if verbose:
            print("XFA data found in PDF")
            if isinstance(xfa, list):
                print(f"XFA array length: {len(xfa)}")

        # Extract XFA packets
        xfa_packets = {}

        if isinstance(xfa, list):
            # XFA array contains pairs of packet names and their data
            for i in range(0, len(xfa), 2):
                if i + 1 < len(xfa):
                    packet_name = xfa[i]
                    if isinstance(packet_name, str):
                        packet_data = xfa[i + 1].get_object()
                        if hasattr(packet_data, 'get_data'):
                            xfa_packets[packet_name] = packet_data.get_data()
                            if verbose:
                                print(f"Found XFA packet: {packet_name} ({len(packet_data.get_data())} bytes)")

        if verbose and xfa_packets:
            print(f"XFA packets found: {list(xfa_packets.keys())}")

        # Look for datasets packet which contains form data
        if 'datasets' in xfa_packets:
            if verbose:
                print("Using 'datasets' packet for form data")
            xml_data = xfa_packets['datasets']
            xfa_fields = parse_xfa_xml(xml_data, verbose)
            # Merge with regular form fields if any
            if fields:
                fields.update(xfa_fields)
                return fields
            return xfa_fields
        elif 'template' in xfa_packets:
            if verbose:
                print("Using 'template' packet for form data")
            xml_data = xfa_packets['template']
            xfa_fields = parse_xfa_xml(xml_data, verbose)
            # Merge with regular form fields if any
            if fields:
                fields.update(xfa_fields)
                return fields
            return xfa_fields
        else:
            if verbose:
                print("Could not find form data in XFA packets")
                if 'form' in xfa_packets:
                    print("Trying 'form' packet as fallback")
                    xml_data = xfa_packets['form']
                    xfa_fields = parse_xfa_xml(xml_data, verbose)
                    if fields:
                        fields.update(xfa_fields)
                        return fields
                    return xfa_fields

            # If we have regular form fields but no XFA data, return those
            if fields:
                return fields

            raise ValueError("Could not find form data in XFA")

    except Exception as e:
        raise ValueError(f"Error processing PDF: {str(e)}")


def extract_acroform_fields(reader):
    """
    Extract regular AcroForm fields from a PDF.

    Args:
        reader (PdfReader): PyPDF2 reader object

    Returns:
        dict: Dictionary of form field names and values
    """
    fields = {}
    try:
        # Get form fields
        for page in reader.pages:
            if '/Annots' in page:
                for annot in page['/Annots']:
                    obj = annot.get_object()
                    if '/T' in obj and '/V' in obj:
                        field_name = obj['/T']
                        field_value = obj['/V']
                        # Convert PDF string objects to Python strings
                        if hasattr(field_value, 'get_data'):
                            field_value = field_value.get_data().decode('utf-8', errors='ignore')
                        elif isinstance(field_value, bytes):
                            field_value = field_value.decode('utf-8', errors='ignore')
                        fields[field_name] = field_value
    except Exception as e:
        print(f"Warning: Error extracting AcroForm fields: {str(e)}")

    return fields

def parse_xfa_xml(xml_bytes, verbose=False):
    """
    Parse XFA XML data and extract form fields.

    Args:
        xml_bytes (bytes): Raw XML data from PDF
        verbose (bool): Whether to print detailed information

    Returns:
        dict: Dictionary of form fields and their values
    """
    try:
        # Remove any XML declaration and DOCTYPE
        xml_str = xml_bytes.decode('utf-8', errors='ignore')
        xml_str = re.sub(r'<\?xml.*?\?>', '', xml_str)
        xml_str = re.sub(r'<!DOCTYPE.*?>', '', xml_str)

        if verbose:
            print(f"XML data size: {len(xml_str)} characters")
            print("First 100 characters of XML data:")
            print(xml_str[:100] + "...")

        # Parse XML
        try:
            root = etree.fromstring(xml_str.encode('utf-8'))
        except Exception as xml_error:
            if verbose:
                print(f"Error parsing XML: {str(xml_error)}")
                print("Trying to clean XML data...")
            # Try to clean XML data
            xml_str = re.sub(r'[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\U00010000-\U0010FFFF]', '', xml_str)
            root = etree.fromstring(xml_str.encode('utf-8'))

        # Remove namespaces for easier parsing
        for elem in root.getiterator():
            if not hasattr(elem.tag, 'find'): continue
            i = elem.tag.find('}')
            if i >= 0:
                elem.tag = elem.tag[i+1:]

        if verbose:
            print(f"Root element tag: {root.tag}")
            print(f"Number of child elements: {len(root)}")

        # Extract form fields
        fields = {}
        field_elements = []

        # Look for field elements
        for elem in root.iter():
            # Check for common XFA field elements
            if elem.tag in ['field', 'exclGroup', 'subform']:
                field_elements.append(elem)

            # Extract text content from elements
            if elem.text and elem.text.strip():
                # Get full path to element for better field naming
                path = get_element_path(elem)
                fields[path] = elem.text.strip()

                # Also store with just the tag name for compatibility
                fields[elem.tag] = elem.text.strip()

        # Process field elements to extract values and names
        for field_elem in field_elements:
            name = field_elem.get('name', '')
            if name:
                # Look for value elements
                value_elem = field_elem.find('.//value')
                if value_elem is not None and value_elem.text:
                    fields[name] = value_elem.text.strip()

                # Look for items/text elements (for dropdown/list fields)
                items = field_elem.findall('.//items/text')
                if items:
                    item_values = [item.text.strip() for item in items if item.text]
                    fields[f"{name}_items"] = ", ".join(item_values)

        if verbose:
            print(f"Extracted {len(fields)} fields from XML data")

        return fields

    except Exception as e:
        if verbose:
            print(f"Error parsing XFA XML: {str(e)}")
        raise ValueError(f"Error parsing XFA XML: {str(e)}")


def get_element_path(element, max_depth=3):
    """
    Get a path representation of an element's position in the XML tree.

    Args:
        element: The XML element
        max_depth: Maximum number of parent elements to include in path

    Returns:
        str: Path representation of the element
    """
    path = [element.tag]
    parent = element.getparent()
    depth = 0

    while parent is not None and depth < max_depth:
        if hasattr(parent.tag, 'find'):
            i = parent.tag.find('}')
            if i >= 0:
                tag = parent.tag[i+1:]
            else:
                tag = parent.tag
            path.append(tag)
        parent = parent.getparent()
        depth += 1

    # Reverse to get root-to-leaf order
    path.reverse()
    return '/'.join(path)

def print_form_fields(fields, output_format='text'):
    """
    Print form fields in a formatted way.

    Args:
        fields (dict): Dictionary of form fields and values
        output_format (str): Output format ('text', 'json')
    """
    if not fields:
        if output_format == 'json':
            print(json.dumps({}))
        else:
            print("No form fields found")
        return

    # Convert binary data to base64 for JSON serialization
    if output_format == 'json':
        json_safe_fields = {}
        for key, value in fields.items():
            if isinstance(value, bytes):
                json_safe_fields[key] = f"<binary data, {len(value)} bytes>"
            elif hasattr(value, 'get_data') and callable(getattr(value, 'get_data')):
                json_safe_fields[key] = f"<binary data object, use .get_data() to access>"
            elif isinstance(value, dict):
                # Handle nested dictionaries
                json_safe_dict = {}
                for k, v in value.items():
                    if isinstance(v, bytes):
                        json_safe_dict[k] = f"<binary data, {len(v)} bytes>"
                    elif hasattr(v, 'get_data') and callable(getattr(v, 'get_data')):
                        json_safe_dict[k] = f"<binary data object, use .get_data() to access>"
                    else:
                        json_safe_dict[k] = str(v)
                json_safe_fields[key] = json_safe_dict
            else:
                json_safe_fields[key] = str(value)
        print(json.dumps(json_safe_fields, indent=2, ensure_ascii=False))
    else:
        print("\nForm Fields:")
        print("-" * 50)
        for field_name, value in sorted(fields.items()):
            if isinstance(value, bytes):
                print(f"{field_name}: <binary data, {len(value)} bytes>")
            elif hasattr(value, 'get_data') and callable(getattr(value, 'get_data')):
                print(f"{field_name}: <binary data object, use .get_data() to access>")
            else:
                print(f"{field_name}: {value}")
        print("-" * 50)

def analyze_pdf_structure(pdf_path):
    """
    Analyze the structure of a PDF file and print detailed information.

    Args:
        pdf_path (str): Path to the PDF file
    """
    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        return

    try:
        reader = PdfReader(pdf_path)
        print(f"\nPDF Analysis for: {pdf_path}")
        print("-" * 50)
        print(f"PDF Version: {reader.pdf_header}")
        print(f"Number of Pages: {len(reader.pages)}")

        # Check for encryption
        print(f"Encrypted: {reader.is_encrypted}")

        # Check for AcroForm
        has_acroform = "/AcroForm" in reader.trailer["/Root"]
        print(f"Has AcroForm: {has_acroform}")

        if has_acroform:
            acroform = reader.trailer["/Root"]["/AcroForm"]
            print(f"AcroForm keys: {list(acroform.keys())}")

            # Check for XFA
            has_xfa = "/XFA" in acroform
            print(f"Has XFA: {has_xfa}")

            if has_xfa:
                xfa = acroform.get("/XFA")
                if isinstance(xfa, list):
                    print(f"XFA array length: {len(xfa)}")
                    # Print XFA packet names
                    packet_names = [xfa[i] for i in range(0, len(xfa), 2) if i < len(xfa) and isinstance(xfa[i], str)]
                    print(f"XFA packets: {packet_names}")

        # Check for form fields
        fields_count = 0
        for page in reader.pages:
            if '/Annots' in page:
                for annot in page['/Annots']:
                    obj = annot.get_object()
                    if '/T' in obj and '/V' in obj:
                        fields_count += 1

        print(f"Number of form fields: {fields_count}")
        print("-" * 50)

    except Exception as e:
        print(f"Error analyzing PDF: {str(e)}")


def extract_xfa_packet(pdf_path, packet_name, verbose=False):
    """
    Extract and parse a specific XFA packet from a PDF.

    Args:
        pdf_path (str): Path to the PDF file
        packet_name (str): Name of the XFA packet to extract
        verbose (bool): Whether to print detailed information

    Returns:
        dict: Extracted fields from the packet
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        reader = PdfReader(pdf_path)

        # Check if AcroForm exists
        if "/AcroForm" not in reader.trailer["/Root"]:
            raise ValueError("PDF does not contain AcroForm")

        acroform = reader.trailer["/Root"]["/AcroForm"]
        xfa = acroform.get("/XFA")

        if not xfa:
            raise ValueError("No XFA data found in PDF")

        # Extract XFA packets
        xfa_packets = {}

        if isinstance(xfa, list):
            # XFA array contains pairs of packet names and their data
            for i in range(0, len(xfa), 2):
                if i + 1 < len(xfa):
                    pkt_name = xfa[i]
                    if isinstance(pkt_name, str):
                        packet_data = xfa[i + 1].get_object()
                        if hasattr(packet_data, 'get_data'):
                            xfa_packets[pkt_name] = packet_data.get_data()

        if packet_name in xfa_packets:
            if verbose:
                print(f"Extracting data from '{packet_name}' packet")
            xml_data = xfa_packets[packet_name]
            return parse_xfa_xml(xml_data, verbose)
        else:
            raise ValueError(f"XFA packet '{packet_name}' not found in PDF")

    except Exception as e:
        raise ValueError(f"Error extracting XFA packet: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description='Extract text from XFA-based (dynamic) PDF forms')
    parser.add_argument('pdf_path', nargs='?', default="032625-1rg0603.pdf", help='Path to the PDF file')
    parser.add_argument('--analyze', '-a', action='store_true', help='Analyze PDF structure')
    parser.add_argument('--verbose', '-v', action='store_true', help='Print detailed information')
    parser.add_argument('--json', '-j', action='store_true', help='Output in JSON format')
    parser.add_argument('--packet', '-p', help='Extract a specific XFA packet (e.g., template, datasets, form)')
    parser.add_argument('--all-packets', action='store_true', help='Extract data from all available XFA packets')

    args = parser.parse_args()

    if args.analyze:
        analyze_pdf_structure(args.pdf_path)
        return

    output_format = 'json' if args.json else 'text'

    try:
        if args.packet:
            # Extract a specific packet
            form_fields = extract_xfa_packet(args.pdf_path, args.packet, args.verbose)
            print(f"\nFields from '{args.packet}' packet:")
            print_form_fields(form_fields, output_format)
        elif args.all_packets:
            # Try to extract all available packets
            reader = PdfReader(args.pdf_path)
            acroform = reader.trailer["/Root"]["/AcroForm"]
            xfa = acroform.get("/XFA")

            if isinstance(xfa, list):
                packet_names = [xfa[i] for i in range(0, len(xfa), 2)
                               if i < len(xfa) and isinstance(xfa[i], str)]

                for packet_name in packet_names:
                    if packet_name in ['xdp:xdp', '</xdp:xdp>']:
                        continue  # Skip XML wrapper elements

                    try:
                        form_fields = extract_xfa_packet(args.pdf_path, packet_name, args.verbose)
                        print(f"\nFields from '{packet_name}' packet:")
                        print_form_fields(form_fields, output_format)
                    except Exception as e:
                        print(f"Error extracting '{packet_name}' packet: {str(e)}")
        else:
            # Default behavior - extract form fields
            form_fields = extract_xfa_data(args.pdf_path, args.verbose)
            print_form_fields(form_fields, output_format)
    except (FileNotFoundError, ValueError) as e:
        print(f"Error: {e}")
        if args.verbose:
            print("\nTrying to analyze PDF structure for more information:")
            analyze_pdf_structure(args.pdf_path)


if __name__ == "__main__":
    main()

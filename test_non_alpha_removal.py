#!/usr/bin/env python3
"""
Test script to demonstrate the non-alphabetic character removal feature
in the language detection script.
"""

from language_detector import LanguageDetector

def create_test_file_with_mixed_content():
    """Create a test file with text containing numbers, punctuation, and special characters."""
    test_content = """Hello, how are you today? I'm fine! 123
Bonjour, comment allez-vous? Ça va bien! 456
¡Hola! ¿Cómo estás? ¡Muy bien! 789
Guten Tag! Wie geht es Ihnen? Sehr gut! @#$
Ciao! Come stai? Molto bene! €£¥
Привет! Как дела? Всё хорошо! ₽₴₸
こんにちは！元気ですか？元気です！ ¥￥
안녕하세요! 어떻게 지내세요? 잘 지내요! ₩
你好！你好吗？我很好！ ¥￥
مرحبا! كيف حالك؟ أنا بخير! ﷼
Email: <EMAIL> Phone: ******-567-8900
Website: https://www.example.com Price: $99.99
Date: 2024-01-15 Time: 14:30:00 Code: ABC123XYZ
Numbers only: 1234567890
Punctuation only: !@#$%^&*()_+-=[]{}|;:,.<>?
Mixed: Hello123World!@# How456Are789You???"""
    
    with open('test_mixed_content.txt', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("Created test file: test_mixed_content.txt")
    return 'test_mixed_content.txt'

def test_individual_lines():
    """Test individual lines to show the cleaning effect."""
    detector = LanguageDetector()
    
    test_lines = [
        "Hello, how are you today? I'm fine! 123",
        "Bonjour, comment allez-vous? Ça va bien! 456",
        "¡Hola! ¿Cómo estás? ¡Muy bien! 789",
        "Email: <EMAIL> Phone: ******-567-8900",
        "Website: https://www.example.com Price: $99.99",
        "Numbers only: 1234567890",
        "Punctuation only: !@#$%^&*()_+-=[]{}|;:,.<>?",
        "Mixed: Hello123World!@# How456Are789You???"
    ]
    
    print("\n" + "="*80)
    print("INDIVIDUAL LINE TESTING - SHOWING NON-ALPHA REMOVAL EFFECT")
    print("="*80)
    
    for i, line in enumerate(test_lines, 1):
        # Show what the cleaned text would look like
        cleaned_text = ''.join(char if char.isalpha() or char.isspace() else ' ' for char in line)
        cleaned_text = ' '.join(cleaned_text.split())  # Remove extra spaces
        
        lang_code, lang_name, confidence = detector.detect_language(line)
        
        print(f"\nLine {i}:")
        print(f"  Original: {line}")
        print(f"  Cleaned:  {cleaned_text}")
        print(f"  Result:   {lang_name} (confidence: {confidence:.2f})")
        print("-" * 80)

def test_file_analysis():
    """Test the full file analysis with the modified detector."""
    print("\n" + "="*80)
    print("FULL FILE ANALYSIS WITH NON-ALPHA REMOVAL")
    print("="*80)
    
    # Create test file
    test_file = create_test_file_with_mixed_content()
    
    # Initialize detector
    detector = LanguageDetector()
    
    # Analyze the file
    results = detector.analyze_file(test_file)
    
    # Print results
    detector.print_results(results, max_text_length=60)

def compare_before_after():
    """Show a comparison of what text looks like before and after cleaning."""
    print("\n" + "="*80)
    print("BEFORE/AFTER COMPARISON")
    print("="*80)
    
    examples = [
        "Hello123World!@# How456Are789You???",
        "Bonjour, ça va bien! €50 coûte cher...",
        "¡Hola! ¿Cómo estás? Tel: +34-123-456-789",
        "Email: <EMAIL> & website: https://example.org",
        "Price: $99.99 (discount: 20%) = $79.99 final",
        "Date: 2024-01-15, Time: 14:30:00, Code: XYZ789"
    ]
    
    for example in examples:
        # Apply the same cleaning logic as in the detector
        cleaned = ''.join(char if char.isalpha() or char.isspace() else ' ' for char in example)
        cleaned = ' '.join(cleaned.split())  # Remove extra spaces
        
        print(f"Original: {example}")
        print(f"Cleaned:  {cleaned}")
        print("-" * 80)

if __name__ == "__main__":
    print("Language Detection with Non-Alphabetic Character Removal")
    print("="*80)
    
    # Run tests
    compare_before_after()
    test_individual_lines()
    test_file_analysis()
    
    print("\n" + "="*80)
    print("Testing completed!")
    print("="*80)
    print("\nKey benefits of removing non-alphabetic characters:")
    print("1. Improved accuracy by focusing only on linguistic content")
    print("2. Better handling of mixed content (text + numbers/symbols)")
    print("3. More consistent results across different text formats")
    print("4. Reduced noise from punctuation, URLs, emails, etc.")

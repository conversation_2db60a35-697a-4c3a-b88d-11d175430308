import pandas as pd
import os
import sys

try:
	# Path to the map folder
	folder_path = r'c:\D_Partition\Courses\Python_env\map'
	print(f"Reading Excel files from: {folder_path}")

	# List to store all dataframes
	dfs = []
	file_count = 0

	# Loop through all Excel files in the folder
	for file in os.listdir(folder_path):
		if file.endswith('.xlsx'):
			try:
				file_path = os.path.join(folder_path, file)
				print(f"Processing file: {file}")
				df = pd.read_excel(file_path)
				print(f"Successfully read {file} with {len(df)} rows")
				df['Source_File'] = file
				dfs.append(df)
				file_count += 1
			except Exception as e:
				print(f"Error processing {file}: {str(e)}")

	print(f"\nProcessed {file_count} files successfully")
	
	if dfs:
		combined_df = pd.concat(dfs, ignore_index=True)
		print(f"\nTotal number of rows: {len(combined_df)}")
		print("\nDataFrame columns:")
		print(combined_df.columns.tolist())
		print("\nFirst few rows of the combined data:")
		print(combined_df.head())
	else:
		print("No dataframes were created. Check if files were read correctly.")

except Exception as e:
	print(f"An error occurred: {str(e)}")
	sys.exit(1)

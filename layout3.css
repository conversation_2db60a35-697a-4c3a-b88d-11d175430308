:root {
	--primary: #0284c7;
	--primary-light: #38bdf8;
	--bg: #f0f9ff;
	--sidebar-bg: #ffffff;
	--card-bg: #ffffff;
	--text: #0c4a6e;
	--text-light: #0369a1;
}

body {
	background-color: var(--bg);
	color: var(--text);
}

/* Sidebar Styles */
.sidebar {
	width: 240px;
	background: var(--sidebar-bg);
	padding: 1.5rem;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
	border-radius: 0 30px 30px 0;
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 2px solid var(--primary-light);
}

.logo i {
	font-size: 24px;
	color: var(--primary);
	animation: wave 2s ease-in-out infinite;
}

.logo span {
	font-size: 20px;
	font-weight: 600;
	color: var(--primary);
}

.nav-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	color: var(--text-light);
	text-decoration: none;
	border-radius: 12px;
	margin-bottom: 8px;
	transition: all 0.3s ease;
}

.nav-item:hover, .nav-item.active {
	background: var(--primary);
	color: white;
	transform: translateX(8px);
}

.nav-item i {
	font-size: 18px;
}

.sidebar-toggle {
	background: var(--primary);
	color: white;
}

/* Main Content Styles */
.main-content {
	margin-left: 240px;
	position: relative;
	padding: 2rem;
}

.main-content.expanded {
	margin-left: 80px;
}

/* Columns Layout */
.columns {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 2rem;
	margin-top: 1rem;
}

.column h2 {
	margin-bottom: 1.5rem;
	color: var(--primary);
	font-weight: 600;
}

/* Card Styles */
.card {
	background: var(--card-bg);
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
	border: 1px solid #e5e7eb;
	border-radius: 20px;
	padding: 1.5rem;
	position: relative;
	overflow: hidden;
}

.card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(90deg, var(--primary-light), var(--primary));
	opacity: 0;
	transition: opacity 0.3s ease;
}

.card:hover::before {
	opacity: 1;
}

.card h3 {
	color: var(--text);
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.card p {
	color: var(--text-light);
}

/* Decorative Shapes */
.main-content::before {
	content: '';
	position: fixed;
	top: 0;
	right: 0;
	width: 400px;
	height: 400px;
	background: radial-gradient(circle at center, var(--primary-light) 0%, transparent 70%);
	opacity: 0.1;
	border-radius: 50%;
	z-index: -1;
	animation: pulse 8s ease-in-out infinite;
}

.main-content::after {
	content: '';
	position: fixed;
	bottom: 0;
	left: 300px;
	width: 100%;
	height: 200px;
	background: var(--primary);
	opacity: 0.05;
	clip-path: polygon(0% 100%, 100% 100%, 100% 40%, 85% 50%, 70% 40%, 55% 50%, 40% 40%, 25% 50%, 10% 40%, 0 50%);
	z-index: -1;
	animation: wave 10s linear infinite;
}

/* Animations */
@keyframes wave {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-10px); }
}

@keyframes pulse {
	0%, 100% { transform: scale(1); opacity: 0.1; }
	50% { transform: scale(1.1); opacity: 0.15; }
}

/* Responsive Design */
@media (max-width: 1024px) {
	.columns {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.columns {
		grid-template-columns: 1fr;
	}
}
import pdfplumber
import os
import concurrent.futures
from datetime import datetime
from tqdm import tqdm
import multiprocessing

# Configuration - MODIFY THESE VALUES
INPUT_FILE = "pdf_list.txt"  # Text file with one PDF path per line
OUTPUT_FILE = "searchability_results.txt"  # Output file for results
MIN_AVG_CHARS_PER_PAGE = 300  # Minimum average characters per page for searchable PDFs
MIN_CHARS_PER_PAGE = 20  # Minimum characters on any single page
MAX_WORKERS = 7  # Number of parallel processes
BATCH_SIZE = 500  # Number of PDFs to process in each batch

def main(pdf_path):
    """
    Main function to check if a PDF is searchable.
    This function is designed to be used with multiprocessing.
    
    Args:
        pdf_path: Path to the PDF file
        
    Returns:
        str: Tab-separated result string with path, status, and reason
    """
    if not os.path.exists(pdf_path):
        return f"{pdf_path}\tNOT SEARCHABLE\tFile not found"
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            total_text = ""
            low_text_pages = []
            
            # Extract text from each page and track length
            for i, page in enumerate(pdf.pages):
                page_text = page.extract_text() or ""
                total_text += page_text
                
                # Check if page has less than minimum characters
                if len(page_text) < MIN_CHARS_PER_PAGE:
                    low_text_pages.append(i + 1)  # Store 1-based page number
            
            # Calculate average characters per page
            total_pages = len(pdf.pages)
            if total_pages == 0:
                return f"{pdf_path}\tNOT SEARCHABLE\tEmpty PDF (0 pages)"
                
            avg_chars_per_page = len(total_text) / total_pages
            
            # Determine if PDF is searchable
            if avg_chars_per_page < MIN_AVG_CHARS_PER_PAGE and low_text_pages:
                reason = f"Low average text ({avg_chars_per_page:.1f} chars/page < {MIN_AVG_CHARS_PER_PAGE}) AND pages with < {MIN_CHARS_PER_PAGE} chars: {', '.join(map(str, low_text_pages))}"
                return f"{pdf_path}\tNOT SEARCHABLE\t{reason}"
            elif avg_chars_per_page < MIN_AVG_CHARS_PER_PAGE:
                reason = f"Low average text ({avg_chars_per_page:.1f} chars/page < {MIN_AVG_CHARS_PER_PAGE})"
                return f"{pdf_path}\tNOT SEARCHABLE\t{reason}"
            elif low_text_pages:
                reason = f"Pages with < {MIN_CHARS_PER_PAGE} characters: {', '.join(map(str, low_text_pages))}"
                return f"{pdf_path}\tNOT SEARCHABLE\t{reason}"
            else:
                return f"{pdf_path}\tSEARCHABLE\tSearchable ({avg_chars_per_page:.1f} chars/page)"
                
    except Exception as e:
        return f"{pdf_path}\tNOT SEARCHABLE\tError processing PDF: {str(e)}"

if __name__ == "__main__":
    # Freeze support is needed for Windows
    multiprocessing.freeze_support()
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(OUTPUT_FILE)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Read the list of PDFs
    try:
        with open(INPUT_FILE, 'r') as f:
            pdf_paths = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"Error reading input file: {str(e)}")
        exit(1)
    
    total_pdfs = len(pdf_paths)
    if total_pdfs == 0:
        print("No PDFs found in the input file.")
        exit(1)
    
    print(f"Starting analysis of {total_pdfs} PDFs...")
    
    # Write header to output file
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        f.write(f"# PDF Searchability Analysis - {timestamp}\n")
        f.write(f"# Criteria: Avg chars/page > {MIN_AVG_CHARS_PER_PAGE} and all pages > {MIN_CHARS_PER_PAGE} chars\n")
        f.write("PDF_Path\tStatus\tReason\n")
    
    # Process PDFs using multiprocessing with progress bar exactly as requested
    one_time_count = BATCH_SIZE
    total_rows = len(pdf_paths)
    
    with tqdm(total=total_rows, desc="PROCESSING", unit="pdf", ncols=100) as progress_bar:
        with concurrent.futures.ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            for i in range(0, len(pdf_paths), one_time_count):
                batch_pdfs = pdf_paths[i:i + one_time_count]
                results = executor.map(main, batch_pdfs)
                for result in results:
                    with open(OUTPUT_FILE, 'a', encoding='utf-8') as of:
                        of.write(result)
                        of.write('\n')
                    progress_bar.update(1)
        progress_bar.set_description("DONE")
    
    # Count results
    searchable_count = 0
    with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            if line.startswith('#') or line.startswith('PDF_Path'):
                continue
            if '\tSEARCHABLE\t' in line:
                searchable_count += 1
    
    # Print summary
    print(f"\nAnalysis complete. Results written to {OUTPUT_FILE}")
    print(f"Total PDFs: {total_pdfs}")
    print(f"Searchable PDFs: {searchable_count} ({searchable_count/total_pdfs*100:.1f}%)")
    print(f"Non-searchable PDFs: {total_pdfs - searchable_count} ({(total_pdfs - searchable_count)/total_pdfs*100:.1f}%)")

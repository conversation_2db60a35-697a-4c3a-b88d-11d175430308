line	title	page	line font size	title font size
User Manual	No Title Found	1	36.0	
DA1453x Pro-Development Kit	No Title Found	1	27.959999084472656	
Hardware Description	No Title Found	1	27.959999084472656	
UM-B-169	No Title Found	1	20.040000915527344	
Abstract	No Title Found	1	14.039999961853027	
Renesas DA1453x Pro-development kit supports the family of DA1453x SoC. This user manual	Abstract	1	9.960000038146973	14.039999961853027
describes the system functions of the hardware as well as the guidelines of how to enable or disable	Abstract	1	9.960000038146973	14.039999961853027
features of the DA1453x Pro-development kit.	Abstract	1	9.960000038146973	14.039999961853027
UM-B-169	No Title Found	2	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	2	12.0	
Description	No Title Found	2	12.0	
User Manual	No Title Found	2	9.960000038146973	
Revision 1.0	No Title Found	2	9.960000038146973	
Feb 9, 2024	No Title Found	2	9.960000038146973	
CFR0012	No Title Found	2	7.559999942779541	
2 of 61	No Title Found	2	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	2	7.559999942779541	
Contents	No Title Found	2	14.039999961853027	
Abstract ................................................................................................................................................ 1	Contents	2	9.960000038146973	14.039999961853027
Contents ............................................................................................................................................... 2	Contents	2	9.960000038146973	14.039999961853027
Figures .................................................................................................................................................. 3	Contents	2	9.960000038146973	14.039999961853027
Tables ................................................................................................................................................... 4	Contents	2	9.960000038146973	14.039999961853027
1	Contents	2	9.960000038146973	14.039999961853027
Terms and Definitions ................................................................................................................... 5	Contents	2	9.960000038146973	14.039999961853027
2	No Title Found	2	9.960000038146973	
References ..................................................................................................................................... 5	No Title Found	2	9.960000038146973	
3	No Title Found	2	9.960000038146973	
Introduction.................................................................................................................................... 6	No Title Found	2	9.960000038146973	
3.1	No Title Found	2	9.960000038146973	
Features of DA1453x Pro-DB ............................................................................................... 6	No Title Found	2	9.960000038146973	
3.2	No Title Found	2	9.960000038146973	
Features of DA1453x Pro-MB ............................................................................................... 7	No Title Found	2	9.960000038146973	
3.3	No Title Found	2	9.960000038146973	
DA1453x Pro-Devkit hardware block diagram ...................................................................... 7	No Title Found	2	9.960000038146973	
4	No Title Found	2	9.960000038146973	
Getting to Know DA1453x Pro-Devkit ......................................................................................... 8	No Title Found	2	9.960000038146973	
4.1	No Title Found	2	9.960000038146973	
DA1453x Pro-Devkit hardware components ......................................................................... 8	No Title Found	2	9.960000038146973	
4.2	No Title Found	2	9.960000038146973	
Jumper settings ..................................................................................................................... 9	No Title Found	2	9.960000038146973	
4.3	No Title Found	2	9.960000038146973	
DA1453x Pro-Devkit default setup ...................................................................................... 11	No Title Found	2	9.960000038146973	
5	No Title Found	2	9.960000038146973	
DA1453x Pro-DB .......................................................................................................................... 13	No Title Found	2	9.960000038146973	
5.1	No Title Found	2	9.960000038146973	
Power section of Pro-DB ..................................................................................................... 15	No Title Found	2	9.960000038146973	
5.2	No Title Found	2	9.960000038146973	
RF section ........................................................................................................................... 16	No Title Found	2	9.960000038146973	
5.3	No Title Found	2	9.960000038146973	
R-Multiplexer ....................................................................................................................... 16	No Title Found	2	9.960000038146973	
5.4	No Title Found	2	9.960000038146973	
SPI Data flash on Pro-DB ................................................................................................... 17	No Title Found	2	9.960000038146973	
6	No Title Found	2	9.960000038146973	
DA1453x Pro-MB ......................................................................................................................... 19	No Title Found	2	9.960000038146973	
6.1	No Title Found	2	9.960000038146973	
Power section ...................................................................................................................... 22	No Title Found	2	9.960000038146973	
6.1.1	No Title Found	2	9.960000038146973	
DA1453x Pro-DB power circuit ............................................................................ 22	No Title Found	2	9.960000038146973	
6.1.2	No Title Found	2	9.960000038146973	
U26 (POR) ........................................................................................................... 23	No Title Found	2	9.960000038146973	
6.1.3	No Title Found	2	9.960000038146973	
PMM2................................................................................................................... 23	No Title Found	2	9.960000038146973	
6.1.4	No Title Found	2	9.960000038146973	
Mode selection (Buck, Boost, By-Pass) .............................................................. 25	No Title Found	2	9.960000038146973	
6.2	No Title Found	2	9.960000038146973	
USB HUB ............................................................................................................................ 26	No Title Found	2	9.960000038146973	
6.3	No Title Found	2	9.960000038146973	
USB to UART ...................................................................................................................... 27	No Title Found	2	9.960000038146973	
6.4	No Title Found	2	9.960000038146973	
USB to JTAG ....................................................................................................................... 29	No Title Found	2	9.960000038146973	
6.5	No Title Found	2	9.960000038146973	
Reset function ..................................................................................................................... 29	No Title Found	2	9.960000038146973	
6.6	No Title Found	2	9.960000038146973	
Voltage translator ................................................................................................................ 31	No Title Found	2	9.960000038146973	
6.7	No Title Found	2	9.960000038146973	
SPI data flash ...................................................................................................................... 31	No Title Found	2	9.960000038146973	
6.8	No Title Found	2	9.960000038146973	
Configuration headers J1 and J10 ...................................................................................... 33	No Title Found	2	9.960000038146973	
6.9	No Title Found	2	9.960000038146973	
UART configuration ............................................................................................................. 34	No Title Found	2	9.960000038146973	
6.9.1	No Title Found	2	9.960000038146973	
Single-wire UART ................................................................................................ 35	No Title Found	2	9.960000038146973	
6.9.2	No Title Found	2	9.960000038146973	
Two-wires UART .................................................................................................. 36	No Title Found	2	9.960000038146973	
6.9.3	No Title Found	2	9.960000038146973	
Four-wires UART/UART with flow control ........................................................... 37	No Title Found	2	9.960000038146973	
6.10 JTAG configuration ............................................................................................................. 39	No Title Found	2	9.960000038146973	
6.11 Breakout header J2 ............................................................................................................. 40	No Title Found	2	9.960000038146973	
6.12 Push buttons ....................................................................................................................... 41	No Title Found	2	9.960000038146973	
6.13 User LED ............................................................................................................................. 42	No Title Found	2	9.960000038146973	
6.14 MikroBus interface .............................................................................................................. 42	No Title Found	2	9.960000038146973	
6.15 PMOD interface ................................................................................................................... 43	No Title Found	2	9.960000038146973	
6.16 Measurements and software triggers .................................................................................. 44	No Title Found	2	9.960000038146973	
UM-B-169	No Title Found	3	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	3	12.0	
Description	No Title Found	3	12.0	
User Manual	No Title Found	3	9.960000038146973	
Revision 1.0	No Title Found	3	9.960000038146973	
Feb 9, 2024	No Title Found	3	9.960000038146973	
CFR0012	No Title Found	3	7.559999942779541	
3 of 61	No Title Found	3	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	3	7.559999942779541	
7	No Title Found	3	9.960000038146973	
The Power Measurement Module 2 (PMM2), (500-29-x) .......................................................... 48	No Title Found	3	9.960000038146973	
7.1	No Title Found	3	9.960000038146973	
Accuracy of current measurement for VBAT system (VLDO) ............................................. 50	No Title Found	3	9.960000038146973	
Appendix A Schematics .................................................................................................................... 52	No Title Found	3	9.960000038146973	
A.1	No Title Found	3	9.960000038146973	
DA1453x Pro motherboard (610-01-B), schematic. ............................................................ 52	No Title Found	3	9.960000038146973	
A.2	No Title Found	3	9.960000038146973	
DA14535 Pro daughterboard (610-02-A), schematic ......................................................... 56	No Title Found	3	9.960000038146973	
A.3	No Title Found	3	9.960000038146973	
DA14533 Pro daughterboard (610-05-A), schematic ......................................................... 57	No Title Found	3	9.960000038146973	
A.4	No Title Found	3	9.960000038146973	
PMM2, Power measurement module (500-29-E), schematic ............................................. 58	No Title Found	3	9.960000038146973	
Revision History ................................................................................................................................ 60	No Title Found	3	9.960000038146973	
Figures	No Title Found	3	14.039999961853027	
Figure 1. The DA1453x Pro-Devkit ....................................................................................................... 6	Figures	3	9.960000038146973	14.039999961853027
Figure 2. Block diagram of the Pro DA1453x Dev-Kit Platform ............................................................. 7	Figures	3	9.960000038146973	14.039999961853027
Figure 3. DA1453x complete system of Pro-MB, Pro-DB and PMM2 ................................................... 8	Figures	3	9.960000038146973	14.039999961853027
Figure 4. Default jumper settings for DA1453x Pro-Devkit ................................................................... 9	Figures	3	9.960000038146973	14.039999961853027
Figure 5. System block diagram of DA1453x Pro-DB ......................................................................... 13	Figures	3	9.960000038146973	14.039999961853027
Figure 6. DA14531 and DA14535 Component’s placement ............................................................... 13	Figures	3	9.960000038146973	14.039999961853027
Figure 7. DA14533 Component’s placement ...................................................................................... 14	Figures	3	9.960000038146973	14.039999961853027
Figure 8. Current measurement connector, J2.................................................................................... 15	No Title Found	3	9.960000038146973	
Figure 9. RF section of DA1453x Pro-DB ........................................................................................... 16	No Title Found	3	9.960000038146973	
Figure 10. Modifications required for performing RF measurements .................................................. 16	No Title Found	3	9.960000038146973	
Figure 11. R-Multiplexer default setup ................................................................................................ 17	No Title Found	3	9.960000038146973	
Figure 12. Optional SPI-Flash on the DA14533, DA14535 Pro-DB .................................................... 18	No Title Found	3	9.960000038146973	
Figure 13. DA1453x Pro-MB block diagram ........................................................................................ 19	No Title Found	3	9.960000038146973	
Figure 14. Pro-MB (610-01-A) ............................................................................................................. 20	No Title Found	3	9.960000038146973	
Figure 15. DA1453x Pro-MB power tree block diagram ...................................................................... 22	No Title Found	3	9.960000038146973	
Figure 16. DA1453x Pro-DB power circuit .......................................................................................... 23	No Title Found	3	9.960000038146973	
Figure 17. J5 header configuration ...................................................................................................... 23	No Title Found	3	9.960000038146973	
Figure 18. PMM2 slot .......................................................................................................................... 24	No Title Found	3	9.960000038146973	
Figure 19. How to mount the PMM2 on the DA1453x Pro-MB ........................................................... 24	No Title Found	3	9.960000038146973	
Figure 20. J9 header configuration ...................................................................................................... 25	No Title Found	3	9.960000038146973	
Figure 21. J4 header mode selection .................................................................................................. 25	No Title Found	3	9.960000038146973	
Figure 22. J4 header configuration ...................................................................................................... 26	No Title Found	3	9.960000038146973	
Figure 23. USB HUB circuitry .............................................................................................................. 26	No Title Found	3	9.960000038146973	
Figure 24. USB HUB power supply ..................................................................................................... 27	No Title Found	3	9.960000038146973	
Figure 25. USB to UART circuitry ........................................................................................................ 28	No Title Found	3	9.960000038146973	
Figure 26. USB to UART power supply ............................................................................................... 28	No Title Found	3	9.960000038146973	
Figure 27. USB to JTAG (U25) ............................................................................................................ 29	No Title Found	3	9.960000038146973	
Figure 28. Reset circuit block diagram ................................................................................................ 30	No Title Found	3	9.960000038146973	
Figure 29. Reset circuit on DA1453x Pro-MB ..................................................................................... 30	No Title Found	3	9.960000038146973	
Figure 30. Position of the SW1 and J23 header on the DA1453x Pro-MB ......................................... 31	No Title Found	3	9.960000038146973	
Figure 31. Voltage translator circuitry .................................................................................................. 31	No Title Found	3	9.960000038146973	
Figure 32. SPI data flash schematic .................................................................................................... 32	No Title Found	3	9.960000038146973	
Figure 33. Jumper configuration at J1 header on Pro-MB .................................................................. 32	No Title Found	3	9.960000038146973	
Figure 34. SPI data flash and configuration jumpers position ............................................................. 33	No Title Found	3	9.960000038146973	
Figure 35. J1 pin assignment (including J10) ...................................................................................... 33	No Title Found	3	9.960000038146973	
Figure 36. J1 default jumper configuration .......................................................................................... 34	No Title Found	3	9.960000038146973	
Figure 37. Single-wire UART jumper configuration at J1 header on Pro-MB ..................................... 35	No Title Found	3	9.960000038146973	
Figure 38. Single-wire UART configuration jumpers position.............................................................. 35	No Title Found	3	9.960000038146973	
Figure 39. Two-wires UART jumper configuration at J1 header on Pro-MB ....................................... 36	No Title Found	3	9.960000038146973	
Figure 40. Two-wires UART configuration jumpers position ............................................................... 36	No Title Found	3	9.960000038146973	
Figure 41. Enable two-wires UART on Pro-DB ................................................................................... 36	No Title Found	3	9.960000038146973	
Figure 42. Enabling two-wires UART with jumper wires ..................................................................... 37	No Title Found	3	9.960000038146973	
Figure 43. Full-wire UART jumper configuration at J1 header ............................................................ 37	No Title Found	3	9.960000038146973	
UM-B-169	No Title Found	4	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	4	12.0	
Description	No Title Found	4	12.0	
User Manual	No Title Found	4	9.960000038146973	
Revision 1.0	No Title Found	4	9.960000038146973	
Feb 9, 2024	No Title Found	4	9.960000038146973	
CFR0012	No Title Found	4	7.559999942779541	
4 of 61	No Title Found	4	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	4	7.559999942779541	
Figure 44. Four UART configuration jumpers position ........................................................................ 38	No Title Found	4	9.960000038146973	
Figure 45. Enable two-wires UART on Pro-DB ................................................................................... 38	No Title Found	4	9.960000038146973	
Figure 46. Enabling two-wires UART with jumper wires ..................................................................... 39	No Title Found	4	9.960000038146973	
Figure 47. JTAG jumper configuration at J1 header ........................................................................... 39	No Title Found	4	9.960000038146973	
Figure 48. JTAG configuration jumpers position ................................................................................. 40	No Title Found	4	9.960000038146973	
Figure 49. J2 schematic ...................................................................................................................... 40	No Title Found	4	9.960000038146973	
Figure 50. J2 position on DA1453x Pro-MB ........................................................................................ 40	No Title Found	4	9.960000038146973	
Figure 51. Push buttons SW2 and SW3 .............................................................................................. 41	No Title Found	4	9.960000038146973	
Figure 52. Push buttons position on the DA1453x Pro-MB ................................................................. 41	No Title Found	4	9.960000038146973	
Figure 53. J19 header position on the DA1453x Pro-MB .................................................................... 41	No Title Found	4	9.960000038146973	
Figure 54. User LED (D5) .................................................................................................................... 42	No Title Found	4	9.960000038146973	
Figure 55. User LED position .............................................................................................................. 42	No Title Found	4	9.960000038146973	
Figure 56. MikroBus slot ...................................................................................................................... 42	No Title Found	4	9.960000038146973	
Figure 57. MikroBus position on the DA1453x Pro-MB ....................................................................... 43	No Title Found	4	9.960000038146973	
Figure 58. PMOD slot .......................................................................................................................... 43	No Title Found	4	9.960000038146973	
Figure 59. PMOD position on the DA1453x Pro-MB ........................................................................... 43	No Title Found	4	9.960000038146973	
Figure 60. Power measurement module (PMM2) block diagram ........................................................ 44	No Title Found	4	9.960000038146973	
Figure 61. Current measurement socket (M2) .................................................................................... 45	No Title Found	4	9.960000038146973	
Figure 62. Selection jumper block (J24) and buffer MOSFETS for I/O levels compatibility ................ 46	No Title Found	4	9.960000038146973	
Figure 63. DA1453x waveforms, captured from Power profiler of SmartSnippet Toolbox ................. 47	No Title Found	4	9.960000038146973	
Figure 64. PMM2 current measurement circuit PMM2 ........................................................................ 48	No Title Found	4	9.960000038146973	
Figure 65. PMM2 on board peripherals (power supply, memory and so on) ...................................... 49	No Title Found	4	9.960000038146973	
Figure 66. Current measurement unit PCBA (TOP) ............................................................................ 50	No Title Found	4	9.960000038146973	
Figure 67. Normal mode (1 μA to 640 mA at 3.3 V) data after offset calibration ................................ 51	No Title Found	4	9.960000038146973	
Figure 68. Hibernation mode (100 nA to 60 μA at 3.3 V) data after offset calibration ........................ 51	No Title Found	4	9.960000038146973	
Figure 69. DA1453x Pro-MB (610-01-B), GPIO connectivity, voltage translators, and data flash ...... 52	No Title Found	4	9.960000038146973	
Figure 70. DA1453x Pro-MB (610-01-B), PMM2 interface .................................................................. 53	No Title Found	4	9.960000038146973	
Figure 71. DA1453x Pro-MB (610-01-B), the MCU with segger implementation ................................ 54	No Title Found	4	9.960000038146973	
Figure 72. DA1453x Pro-MB (610-01-B), USB hub, UART and power section .................................. 55	No Title Found	4	9.960000038146973	
Figure 73. DA14535 Pro-DB (610-02-A) ............................................................................................. 56	No Title Found	4	9.960000038146973	
Figure 74. DA14533 Pro-DB (610-05-A) ............................................................................................. 57	No Title Found	4	9.960000038146973	
Figure 75. PMM2 (500-29-E), main circuit .......................................................................................... 58	No Title Found	4	9.960000038146973	
Figure 76. PMM2 (500-29-E), mating connector, power supply and EEPROM .................................. 59	No Title Found	4	9.960000038146973	
Tables	No Title Found	4	14.039999961853027	
Table 1. Pro-Dev-Kit part numbers and description .............................................................................. 9	Tables	4	9.960000038146973	14.039999961853027
Table 2. Headers and jumper settings of DA1453x Pro-Daughterboard .............................................. 9	Tables	4	9.960000038146973	14.039999961853027
Table 3. Headers and jumper settings of DA1453x Pro-Motherboard ................................................ 10	Tables	4	9.960000038146973	14.039999961853027
Table 4. DA1453x Pro-DB and Pro-MB signals assignment (default) ................................................ 12	Tables	4	9.960000038146973	14.039999961853027
Table 5. Configuration settings for SPI data flash in DA1453x Pro-MB .............................................. 32	Tables	4	9.960000038146973	14.039999961853027
Table 6. UART signals assignment in DA1453x Dev-Kit .................................................................... 34	Tables	4	9.960000038146973	14.039999961853027
Table 7. UART signals assignment in DA1453x Dev-Kit .................................................................... 39	Tables	4	9.960000038146973	14.039999961853027
Table 8. Monitored power sources ...................................................................................................... 45	No Title Found	4	9.960000038146973	
Table 9. Accuracy of the current measurement circuit ........................................................................ 51	No Title Found	4	9.960000038146973	
UM-B-169	No Title Found	5	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	5	12.0	
Description	No Title Found	5	12.0	
User Manual	No Title Found	5	9.960000038146973	
Revision 1.0	No Title Found	5	9.960000038146973	
Feb 9, 2024	No Title Found	5	9.960000038146973	
CFR0012	No Title Found	5	7.559999942779541	
5 of 61	No Title Found	5	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	5	7.559999942779541	
1	No Title Found	5	14.039999961853027	
Terms and Definitions	No Title Found	5	14.039999961853027	
UART	Terms and Definitions	5	9.960000038146973	14.039999961853027
Universal Asynchronous Receiver Transmitter	Terms and Definitions	5	9.960000038146973	14.039999961853027
GPIO	Terms and Definitions	5	9.960000038146973	14.039999961853027
General Purpose Input Output (pin)	Terms and Definitions	5	9.960000038146973	14.039999961853027
JTAG	Terms and Definitions	5	9.960000038146973	14.039999961853027
Joint Test Action Group	Terms and Definitions	5	9.960000038146973	14.039999961853027
RF	Terms and Definitions	5	9.960000038146973	14.039999961853027
Radio Frequency	Terms and Definitions	5	9.960000038146973	14.039999961853027
IC	Terms and Definitions	5	9.960000038146973	14.039999961853027
Integrated Circuit	Terms and Definitions	5	9.960000038146973	14.039999961853027
NP	Terms and Definitions	5	9.960000038146973	14.039999961853027
Not Populated	Terms and Definitions	5	9.960000038146973	14.039999961853027
PCB	No Title Found	5	9.960000038146973	
Printed Circuit Board	No Title Found	5	9.960000038146973	
PCBA	No Title Found	5	9.960000038146973	
Printed Circuit Board Assembled	No Title Found	5	9.960000038146973	
SoC	No Title Found	5	9.960000038146973	
System on Chip	No Title Found	5	9.960000038146973	
SWD	No Title Found	5	9.960000038146973	
Serial Wire Debug	No Title Found	5	9.960000038146973	
DK	No Title Found	5	9.960000038146973	
Development Kit	No Title Found	5	9.960000038146973	
Pro-DB	No Title Found	5	9.960000038146973	
Pro Daughterboard	No Title Found	5	9.960000038146973	
Pro-MB	No Title Found	5	9.960000038146973	
Pro Motherboard	No Title Found	5	9.960000038146973	
OTP	No Title Found	5	9.960000038146973	
One-Time Programmable Memory	No Title Found	5	9.960000038146973	
POR	No Title Found	5	9.960000038146973	
Power-On Reset	No Title Found	5	9.960000038146973	
2	No Title Found	5	14.039999961853027	
References	No Title Found	5	14.039999961853027	
[1]	References	5	9.960000038146973	14.039999961853027
DA14531	References	5	9.960000038146973	14.039999961853027
Datasheet	References	5	9.960000038146973	14.039999961853027
, Renesas.	References	5	9.960000038146973	14.039999961853027
[2]	References	5	9.960000038146973	14.039999961853027
DA14535	References	5	9.960000038146973	14.039999961853027
Datasheet	References	5	9.960000038146973	14.039999961853027
, Renesas.	References	5	9.960000038146973	14.039999961853027
[3]	References	5	9.960000038146973	14.039999961853027
UM-B-083,	References	5	9.960000038146973	14.039999961853027
SmartSnippets Toolbox User Manual	References	5	9.960000038146973	14.039999961853027
, User Manual, Renesas.	References	5	9.960000038146973	14.039999961853027
[4]	References	5	9.960000038146973	14.039999961853027
AN-B-075,	References	5	9.960000038146973	14.039999961853027
DA14531 Hardware Guidelines	References	5	9.960000038146973	14.039999961853027
, Application Note, Renesas.	References	5	9.960000038146973	14.039999961853027
[5]	References	5	9.960000038146973	14.039999961853027
AN-B-098, DA14535 Hardware Guidelines, Application Note, Renesas	References	5	9.960000038146973	14.039999961853027
[6]	References	5	9.960000038146973	14.039999961853027
UM-B-114,	References	5	9.960000038146973	14.039999961853027
DA14531 Devkit Pro Hardware	References	5	9.960000038146973	14.039999961853027
, User Manual, Renesas.	References	5	9.960000038146973	14.039999961853027
Note 1	No Title Found	5	9.0	
References are for the latest published version, unless otherwise indicated.	No Title Found	5	9.0	
UM-B-169	No Title Found	6	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	6	12.0	
Description	No Title Found	6	12.0	
User Manual	No Title Found	6	9.960000038146973	
Revision 1.0	No Title Found	6	9.960000038146973	
Feb 9, 2024	No Title Found	6	9.960000038146973	
CFR0012	No Title Found	6	7.559999942779541	
6 of 61	No Title Found	6	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	6	7.559999942779541	
3	No Title Found	6	14.039999961853027	
Introduction	No Title Found	6	14.039999961853027	
This document describes the hardware of DA1453x Pro-Development Kit (Pro-Devkit). The DA1453x	Introduction	6	9.960000038146973	14.039999961853027
Pro-Devkit is available as a set of a Pro motherboard, a Pro daughterboard with four variants	Introduction	6	9.960000038146973	14.039999961853027
(according to IC type and/or package), and the power measurement module PMM2. The complete	Introduction	6	9.960000038146973	14.039999961853027
system is shown in	Introduction	6	9.960000038146973	14.039999961853027
Figure 1.	Introduction	6	9.960000038146973	14.039999961853027
The DA1453x Pro-Devkit, when combined with the Software Development Kit (SDK) and Smart	Introduction	6	9.960000038146973	14.039999961853027
Snippets tools, provides an easy to use and complete platform for software/hardware development.	Introduction	6	9.960000038146973	14.039999961853027
Figure 1. The DA1453x Pro-Devkit	No Title Found	6	9.960000038146973	
The following sections describe the system setup, the different available configuration options, as	No Title Found	6	9.960000038146973	
well as the tools provided to debug, develop, and evaluate the system performance.	No Title Found	6	9.960000038146973	
3.1	No Title Found	6	12.0	
Features of DA1453x Pro-DB	No Title Found	6	12.0	
•	No Title Found	6	11.039999961853027	
Embedded printed antenna.	No Title Found	6	9.960000038146973	
•	No Title Found	6	11.039999961853027	
RF port output (connector not included)	No Title Found	6	9.960000038146973	
UM-B-169	No Title Found	7	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	7	12.0	
Description	No Title Found	7	12.0	
User Manual	No Title Found	7	9.960000038146973	
Revision 1.0	No Title Found	7	9.960000038146973	
Feb 9, 2024	No Title Found	7	9.960000038146973	
CFR0012	No Title Found	7	7.559999942779541	
7 of 61	No Title Found	7	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	7	7.559999942779541	
•	No Title Found	7	11.039999961853027	
Current measurement port to connect external measurement equipment.	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Onboard place holder of a 2-Mbit SPI data Flash for DA14531, DA14533 and DA14535	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Onboard JTAG connector	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Support for coin cell battery (battery holder not included).	No Title Found	7	9.960000038146973	
3.2	No Title Found	7	12.0	
Features of DA1453x Pro-MB	No Title Found	7	12.0	
•	No Title Found	7	11.039999961853027	
A slot to connect a DA1453x Pro-DB which hosts one of the:	No Title Found	7	9.960000038146973	
○	No Title Found	7	11.039999961853027	
DA14531-FCGQFN24	No Title Found	7	9.960000038146973	
○	No Title Found	7	11.039999961853027	
DA14531-WLCSP17	No Title Found	7	9.960000038146973	
○	No Title Found	7	11.039999961853027	
DA14533-	No Title Found	7	9.960000038146973	
FCQFN22	No Title Found	7	9.0	
○	No Title Found	7	11.039999961853027	
DA14535-FCGQFN24	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
A slot for attaching a power measurement module (PMM2)	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Single USB port to provide power and data interfacing to a PC (USB1)	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Onboard JTAG debugger	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Virtual 4-wire UART port	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Onboard 2-Mbit SPI data Flash AT25DF021A-MAHN-T	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Multiple voltage options (1.25 V, 1.8 V, 3.0 V, and 3.3V) to supply the DA1453x DBs	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
User LEDs and push buttons (to be used with a DA14531/5 FCGQFN24 PRO-DB)	No Title Found	7	9.960000038146973	
•	No Title Found	7	11.039999961853027	
Voltage translation for the JTAG and UART signals eliminates current leakage during operation.	No Title Found	7	9.960000038146973	
3.3	No Title Found	7	12.0	
DA1453x Pro-Devkit hardware block diagram	No Title Found	7	12.0	
Current	No Title Found	7	8.348379135131836	
Sense	No Title Found	7	8.348379135131836	
(PMM2)	No Title Found	7	8.322371482849121	
BAT	No Title Found	7	5.20148229598999	
SPI	No Title Found	7	8.322371482849121	
U3	No Title Found	7	6.24177885055542	
SPI Flash	No Title Found	7	5.227489948272705	
SPI	No Title Found	7	8.322371482849121	
32k	No Title Found	7	5.227489948272705	
LPF	No Title Found	7	4.1611857414245605	
Printed	No Title Found	7	5.20148229598999	
ANT1	No Title Found	7	5.227489948272705	
Y2	No Title Found	7	5.227489948272705	
Y1	No Title Found	7	5.20148229598999	
PRO-DB	No Title Found	7	15.656461715698242	
PCI-e Connector (J3)	No Title Found	7	12.48355770111084	
32M	No Title Found	7	5.227489948272705	
SMA	No Title Found	7	5.227489948272705	
J3	No Title Found	7	5.20148229598999	
R - Multiplexer	No Title Found	7	8.322371482849121	
2.2μΗ	No Title Found	7	4.1611857414245605	
U1	No Title Found	7	10.428972244262695	
DA1453x	No Title Found	7	8.322371482849121	
BLE SoC	No Title Found	7	8.348379135131836	
USB	No Title Found	7	5.227489948272705	
U14	No Title Found	7	6.24177885055542	
U13	No Title Found	7	6.24177885055542	
U11	No Title Found	7	7.282074928283691	
USB HUB	No Title Found	7	7.282074928283691	
U5	No Title Found	7	6.24177885055542	
J5	No Title Found	7	7.282074928283691	
Configure U15 output	No Title Found	7	5.227489948272705	
from 1.25V to 3.3V	No Title Found	7	5.20148229598999	
VLDO	No Title Found	7	5.20148229598999	
J9	No Title Found	7	7.308082580566406	
PMM2	No Title Found	7	6.24177885055542	
Selection	No Title Found	7	6.267786502838135	
VLDO_EN	No Title Found	7	4.187193393707275	
J4	No Title Found	7	7.308082580566406	
BUCK, BOOST	No Title Found	7	6.24177885055542	
or BYPASS Selection	No Title Found	7	6.267786502838135	
SW1	No Title Found	7	5.721630573272705	
U2	No Title Found	7	7.308082580566406	
SPI	No Title Found	7	7.308082580566406	
FLASH	No Title Found	7	7.282074928283691	
U25	No Title Found	7	7.308082580566406	
MCU	No Title Found	7	7.282074928283691	
(JTAG)	No Title Found	7	7.282074928283691	
RST	No Title Found	7	5.74763822555542	
U21	No Title Found	7	5.20148229598999	
VLDO_EN	No Title Found	7	4.70734167098999	
U_RST	No Title Found	7	4.681334018707275	
U_RST	No Title Found	7	4.187193393707275	
J1	No Title Found	7	8.322371482849121	
USB1	No Title Found	7	7.308082580566406	
U12	No Title Found	7	7.282074928283691	
FTDI	No Title Found	7	7.308082580566406	
(UART)	No Title Found	7	7.282074928283691	
PRO-MB	No Title Found	7	15.656461715698242	
MikroBus /	No Title Found	7	7.282074928283691	
PMOD	No Title Found	7	7.308082580566406	
J2	No Title Found	7	8.348379135131836	
U26	No Title Found	7	5.721630573272705	
(POR)	No Title Found	7	5.721630573272705	
3V3	No Title Found	7	5.20148229598999	
3V3	No Title Found	7	5.20148229598999	
3V3	No Title Found	7	5.20148229598999	
3V3_HUB	No Title Found	7	5.20148229598999	
3V3_HUB	No Title Found	7	5.20148229598999	
Level	No Title Found	7	8.322371482849121	
Translator	No Title Found	7	8.348379135131836	
JTAG	No Title Found	7	5.227489948272705	
USB	No Title Found	7	5.227489948272705	
V_FL	No Title Found	7	5.20148229598999	
SPI	No Title Found	7	5.227489948272705	
V3 (V	No Title Found	7	5.721630573272705	
HIGH	No Title Found	7	3.6410374641418457	
)	No Title Found	7	5.721630573272705	
V3 (V	No Title Found	7	5.721630573272705	
HIGH	No Title Found	7	3.6410374641418457	
)	No Title Found	7	5.721630573272705	
V1 (V	No Title Found	7	5.721630573272705	
LOW	No Title Found	7	3.6670453548431396	
)	No Title Found	7	5.721630573272705	
V_LDO	No Title Found	7	5.20148229598999	
GPIOs	No Title Found	7	8.348379135131836	
UART	No Title Found	7	8.348379135131836	
JTAG	No Title Found	7	8.348379135131836	
P0x	No Title Found	7	8.348379135131836	
RST	No Title Found	7	5.227489948272705	
USB	No Title Found	7	5.227489948272705	
Optional for	No Title Found	7	6.267786502838135	
DA14533/5	No Title Found	7	6.24177885055542	
V3 (V	No Title Found	7	5.721630573272705	
HIGH	No Title Found	7	3.6410374641418457	
)	No Title Found	7	5.721630573272705	
V1 (V	No Title Found	7	5.721630573272705	
LOW	No Title Found	7	3.6410374641418457	
)	No Title Found	7	5.721630573272705	
Figure 2. Block diagram of the Pro DA1453x Dev-Kit Platform	No Title Found	7	9.960000038146973	
UM-B-169	No Title Found	8	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	8	12.0	
Description	No Title Found	8	12.0	
User Manual	No Title Found	8	9.960000038146973	
Revision 1.0	No Title Found	8	9.960000038146973	
Feb 9, 2024	No Title Found	8	9.960000038146973	
CFR0012	No Title Found	8	7.559999942779541	
8 of 61	No Title Found	8	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	8	7.559999942779541	
4	No Title Found	8	14.039999961853027	
Getting to Know DA1453x Pro-Devkit	No Title Found	8	14.039999961853027	
4.1	No Title Found	8	12.0	
DA1453x Pro-Devkit hardware components	No Title Found	8	12.0	
The DA1453x Pro-Development kit consists of the following parts:	No Title Found	8	9.960000038146973	
Pro-MB	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
PCBA reference number 610-01-B	No Title Found	8	9.960000038146973	
Pro-DB	No Title Found	8	9.960000038146973	
with following variants	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
FCGQFN24 Pro-DB: SoC DA14531-FCGQFN24. PCBA reference number 376-04-F	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
WLCSP17 Pro-DB: SoC DA14531-WLCSP17. PCBA reference number 376-05-E	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
FCQFN22 Pro-DB: SoC DA14533-FCQFN22. PCBA reference number 610-05-A	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
FCGQFN24 Pro-DB: SoC DA14535-FCGQFN24. PCBA reference number 610-02-A	No Title Found	8	9.960000038146973	
PMM2	No Title Found	8	9.960000038146973	
●	No Title Found	8	11.039999961853027	
Power measurement module, PMM2. PCBA reference number 500-29-E	No Title Found	8	9.960000038146973	
Figure 3. DA1453x complete system of Pro-MB, Pro-DB and PMM2	No Title Found	8	9.960000038146973	
UM-B-169	No Title Found	9	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	9	12.0	
Description	No Title Found	9	12.0	
User Manual	No Title Found	9	9.960000038146973	
Revision 1.0	No Title Found	9	9.960000038146973	
Feb 9, 2024	No Title Found	9	9.960000038146973	
CFR0012	No Title Found	9	7.559999942779541	
9 of 61	No Title Found	9	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	9	7.559999942779541	
Table 1. Pro-Dev-Kit part numbers and description	No Title Found	9	9.960000038146973	
Part number	No Title Found	9	9.0	
Description	No Title Found	9	9.0	
DA14531-00OGDB-P	No Title Found	9	9.0	
DA14531 WLCSP17 Pro-DB.	No Title Found	9	9.0	
Bluetooth Low Energy DA14531 WLCSP17 Pro-daughterboard	No Title Found	9	9.0	
DA14531-00FXDB-P	No Title Found	9	9.0	
DA14531 FCGQFN24 Pro-DB	No Title Found	9	9.0	
Bluetooth Low Energy DA14531 FCGQFN24 Pro-daughterboard	No Title Found	9	9.0	
DA14533 Pro-DB	No Title Found	9	9.0	
DA14533, FCQFN22 Pro-DB	No Title Found	9	9.0	
Bluetooth Low Energy DA14533 FCQFN22 Pro-daughterboard	No Title Found	9	9.0	
DA14535-00FXDB-P	No Title Found	9	9.0	
DA14535, FCGQFN24 Pro-DB	No Title Found	9	9.0	
Bluetooth Low Energy DA14535 FCGQFN24 Pro-daughterboard	No Title Found	9	9.0	
DA1453x Pro-MB	No Title Found	9	9.0	
DA1453x Bluetooth Low Energy Development Pro-MB	No Title Found	9	9.0	
DA14535-00FXDEVKT-P	No Title Found	9	9.0	
DA14535 DK-Pro	No Title Found	9	9.0	
Bluetooth Low Energy Development Kit Pro for DA1453x, including Pro-MB	No Title Found	9	9.0	
and Pro-DB.	No Title Found	9	9.0	
4.2	No Title Found	9	12.0	
Jumper settings	No Title Found	9	12.0	
Table 2. Headers and jumper settings of DA1453x Pro-Daughterboard	Jumper settings	9	9.960000038146973	12.0
HDR	Jumper settings	9	9.0	12.0
Function of headers	Jumper settings	9	9.0	12.0
Jumper Options	Jumper settings	9	9.0	12.0
Default Jumper	Jumper settings	9	9.0	12.0
setting	Jumper settings	9	9.0	12.0
J1	Jumper settings	9	9.0	12.0
Supply DA1453x SoC	Jumper settings	9	9.0	12.0
Supply VBAT_Hi	Jumper settings	9	9.0	12.0
J2:1-2	Jumper settings	9	9.0	12.0
Mounted 1-2	Jumper settings	9	9.0	12.0
Supply VBAT_Lo	Jumper settings	9	9.0	12.0
J2:4-5	Jumper settings	9	9.0	12.0
Mounted 4-5	Jumper settings	9	9.0	12.0
J5	No Title Found	9	8.480043411254883	
J9	No Title Found	9	8.480043411254883	
J4	No Title Found	9	8.480043411254883	
J23	No Title Found	9	8.480043411254883	
J8	No Title Found	9	8.480043411254883	
J19	No Title Found	9	8.480043411254883	
J1	No Title Found	9	8.480043411254883	
J24	No Title Found	9	8.480043411254883	
J10	No Title Found	9	8.480043411254883	
Figure 4. Default jumper settings for DA1453x Pro-Devkit	No Title Found	9	9.960000038146973	
UM-B-169	No Title Found	10	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	10	12.0	
Description	No Title Found	10	12.0	
User Manual	No Title Found	10	9.960000038146973	
Revision 1.0	No Title Found	10	9.960000038146973	
Feb 9, 2024	No Title Found	10	9.960000038146973	
CFR0012	No Title Found	10	7.559999942779541	
10 of 61	No Title Found	10	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	10	7.559999942779541	
Table 3. Headers and jumper settings of DA1453x Pro-Motherboard	No Title Found	10	9.960000038146973	
HDR	No Title Found	10	9.0	
Function of headers	No Title Found	10	9.0	
Jumper options	No Title Found	10	9.0	
Default jumper	No Title Found	10	9.0	
setting	No Title Found	10	9.0	
J1 and	No Title Found	10	9.0	
J10	No Title Found	10	9.0	
DA1453x Pro-Devkit	No Title Found	10	9.0	
System setup	No Title Found	10	9.0	
Enable SPI Data Flash	No Title Found	10	9.0	
on Pro-Motherboard	No Title Found	10	9.0	
Mount pos. 1-2	No Title Found	10	8.520000457763672	
Mount pos. 3-4	No Title Found	10	8.520000457763672	
Mount pos. 5-6	No Title Found	10	8.520000457763672	
Mount pos. 7-8	No Title Found	10	8.520000457763672	
Mount pos. 9-10	No Title Found	10	8.520000457763672	
SPI Flash Enabled	No Title Found	10	9.0	
Mounted 1-2	No Title Found	10	8.520000457763672	
Mounted 3-4	No Title Found	10	8.520000457763672	
Mounted 5-6	No Title Found	10	8.520000457763672	
Mounted 7-8	No Title Found	10	8.520000457763672	
Mounted 9-10	No Title Found	10	8.520000457763672	
Enable SWD	No Title Found	10	9.0	
Mount pos. 21-22	No Title Found	10	8.520000457763672	
Mount pos. 23-24	No Title Found	10	8.520000457763672	
SWD Enabled	No Title Found	10	8.520000457763672	
Mounted 21-22	No Title Found	10	8.520000457763672	
Mounted 23-24	No Title Found	10	8.520000457763672	
Enable Reset	No Title Found	10	9.0	
Mount pos. 25-26	No Title Found	10	8.520000457763672	
Reset Enabled	No Title Found	10	9.0	
Mounted 25-26	No Title Found	10	9.0	
Enable 2-UART	No Title Found	10	9.0	
Mount pos. 15-16	No Title Found	10	8.520000457763672	
Mount pos. 17-18	No Title Found	10	8.520000457763672	
Not mounted	No Title Found	10	9.0	
Enable 4-UART	No Title Found	10	9.0	
Mount pos. 11-12	No Title Found	10	8.520000457763672	
Mount pos. 13-14	No Title Found	10	8.520000457763672	
Mount pos. 15-16	No Title Found	10	8.520000457763672	
Mount pos. 17-18	No Title Found	10	8.520000457763672	
Not mounted	No Title Found	10	9.0	
Enable Single UART	No Title Found	10	9.0	
Mount pos. J1.16-	No Title Found	10	8.520000457763672	
J10	No Title Found	10	9.0	
.1	No Title Found	10	8.520000457763672	
Mount pos. J1.18-	No Title Found	10	8.520000457763672	
J10	No Title Found	10	9.0	
.2	No Title Found	10	8.520000457763672	
Single UART Enabled	No Title Found	10	9.0	
Mounted	No Title Found	10	9.0	
J1.16 -	No Title Found	10	8.520000457763672	
J10	No Title Found	10	9.0	
.1	No Title Found	10	8.520000457763672	
Mounted	No Title Found	10	9.0	
J2.18 -	No Title Found	10	8.520000457763672	
J10	No Title Found	10	9.0	
.2	No Title Found	10	8.520000457763672	
J4	No Title Found	10	9.0	
Selection of Mode	No Title Found	10	9.0	
DA1453x SoC power	No Title Found	10	9.0	
configuration	No Title Found	10	9.0	
BOOST	No Title Found	10	9.0	
Mount pos. 1-2	No Title Found	10	9.0	
BUCK	No Title Found	10	9.0	
Mounted 3-4	No Title Found	10	9.0	
BUCK	No Title Found	10	9.0	
Mount pos. 3-4	No Title Found	10	9.0	
BYPASS	No Title Found	10	9.0	
Mount pos. 1-2	No Title Found	10	9.0	
Mount pos. 3-4	No Title Found	10	9.0	
J5	No Title Found	10	9.0	
Voltage levels of V	No Title Found	10	9.0	
LDO	No Title Found	10	6.0	
V	No Title Found	10	9.0	
LDO	No Title Found	10	6.0	
generates V1 and	No Title Found	10	9.0	
V3	No Title Found	10	9.0	
1.2 V	No Title Found	10	9.0	
Mount no jumper	No Title Found	10	9.0	
3.3 V	No Title Found	10	9.0	
Mounted 1-3	No Title Found	10	9.0	
Mounted 2-4	No Title Found	10	9.0	
1.8V	No Title Found	10	9.0	
Mount Pos. 1-3	No Title Found	10	9.0	
3.0 V	No Title Found	10	9.0	
Mount pos. 2-4	No Title Found	10	9.0	
3.3 V	No Title Found	10	9.0	
Mount pos.1-3	No Title Found	10	9.0	
Mount pos. 2-4	No Title Found	10	9.0	
J6	No Title Found	10	9.0	
Force Power Enable	No Title Found	10	9.0	
for	No Title Found	10	9.0	
supplying Pro-Devkit	No Title Found	10	9.0	
with a power source	No Title Found	10	9.0	
without USB data	No Title Found	10	9.0	
connection	No Title Found	10	9.0	
Force Power_Enable       Mount pos. 1-2	No Title Found	10	9.0	
Not mounted 1-2	No Title Found	10	9.0	
J8	No Title Found	10	9.0	
Reset sourced from	No Title Found	10	9.0	
R7FA4M2AD3CFL	No Title Found	10	9.0	
(implemented from	No Title Found	10	9.0	
Segger software)	No Title Found	10	9.0	
Enable Reset	No Title Found	10	9.0	
Mount pos. 1-2	No Title Found	10	9.0	
Mounted 1-2	No Title Found	10	9.0	
LED, D5 connection to	No Title Found	10	9.0	
P0_9	No Title Found	10	9.0	
Enable LED connection	No Title Found	10	9.0	
Mount pos. 3-4	No Title Found	10	9.0	
Mounted 3-4	No Title Found	10	9.0	
UM-B-169	No Title Found	11	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	11	12.0	
Description	No Title Found	11	12.0	
User Manual	No Title Found	11	9.960000038146973	
Revision 1.0	No Title Found	11	9.960000038146973	
Feb 9, 2024	No Title Found	11	9.960000038146973	
CFR0012	No Title Found	11	7.559999942779541	
11 of 61	No Title Found	11	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	11	7.559999942779541	
HDR	No Title Found	11	9.0	
Function of headers	No Title Found	11	9.0	
Jumper options	No Title Found	11	9.0	
Default jumper	No Title Found	11	9.0	
setting	No Title Found	11	9.0	
J9	No Title Found	11	9.0	
Power Measurement	No Title Found	11	9.0	
Module PMM2	No Title Found	11	9.0	
PMM2 Bypass	No Title Found	11	9.0	
Mount pos. 2-3	No Title Found	11	9.0	
PMM2 mounted	No Title Found	11	9.0	
Mounted 1-2	No Title Found	11	9.0	
Mounted 3-4	No Title Found	11	9.0	
PMM2 not mounted	No Title Found	11	9.0	
Mounted 2-3	No Title Found	11	9.0	
PMM2 Enable	No Title Found	11	9.0	
Mount pos.1-2	No Title Found	11	9.0	
Mount pos. 3-4	No Title Found	11	9.0	
J19	No Title Found	11	9.0	
Enable Push buttons	No Title Found	11	9.0	
SW2 and SW3	No Title Found	11	9.0	
Enable SW2	No Title Found	11	9.0	
Mount pos.1-2	No Title Found	11	9.0	
Mounted 1-2	No Title Found	11	9.0	
Enable SW3	No Title Found	11	9.0	
Mount pos.2-3	No Title Found	11	9.0	
Mounted 3-4	No Title Found	11	9.0	
J23	No Title Found	11	9.0	
Reset source options	No Title Found	11	9.0	
Activate Reset driven	No Title Found	11	9.0	
from Debugger	No Title Found	11	9.0	
(U_RSTn)	No Title Found	11	9.0	
Mount pos.1-2	No Title Found	11	9.0	
Not Mounted 1-2	No Title Found	11	9.0	
Activate POR	No Title Found	11	9.0	
functionality when	No Title Found	11	9.0	
pressing SW1 push	No Title Found	11	9.0	
button	No Title Found	11	9.0	
Mount pos. 3-4	No Title Found	11	9.0	
Not Mounted 3-4	No Title Found	11	9.0	
J24	No Title Found	11	9.0	
Software Trigger	No Title Found	11	9.0	
activation	No Title Found	11	9.0	
TRIG_0 mapped to	No Title Found	11	8.520000457763672	
P0_11	No Title Found	11	8.520000457763672	
TRIG_1 mapped to P0_0	No Title Found	11	8.520000457763672	
TRIG_2 mapped to P0_1	No Title Found	11	8.520000457763672	
TRIG_3 mapped to P0_5	No Title Found	11	8.520000457763672	
TRIG_4 mapped to P0_6	No Title Found	11	8.520000457763672	
TRIG_5 mapped to P0_7	No Title Found	11	8.520000457763672	
TRIG_6 mapped to P0_8	No Title Found	11	8.520000457763672	
TRIG_7 mapped to P0_9	No Title Found	11	8.520000457763672	
Mount pos.1-2	No Title Found	11	8.520000457763672	
Mount pos.3-4	No Title Found	11	8.520000457763672	
Mount pos.5-6	No Title Found	11	8.520000457763672	
Mount pos.7-8	No Title Found	11	8.520000457763672	
Mount pos. 9-10	No Title Found	11	8.520000457763672	
Mount pos.11-12	No Title Found	11	8.520000457763672	
Mount pos.13-14	No Title Found	11	8.520000457763672	
Mount pos.15-16	No Title Found	11	8.520000457763672	
Mounted 1-2	No Title Found	11	9.0	
4.3	No Title Found	11	12.0	
DA1453x Pro-Devkit default setup	No Title Found	11	12.0	
The default configuration of DA1453x Pro-Devkit is enabled by applying the appropriate jumpers to	No Title Found	11	9.960000038146973	
the DA1453x Pro-MB:	No Title Found	11	9.960000038146973	
•	No Title Found	11	9.960000038146973	
Reset is enabled.	No Title Found	11	9.960000038146973	
•	No Title Found	11	9.960000038146973	
JTAG is enabled.	No Title Found	11	9.960000038146973	
•	No Title Found	11	9.960000038146973	
Single wire UART is enabled.	No Title Found	11	9.960000038146973	
•	No Title Found	11	9.960000038146973	
SPI data flash (populated on Pro-MB) Dual UART or full UART are not enabled.	No Title Found	11	9.960000038146973	
•	No Title Found	11	9.960000038146973	
Crystal 32.768 kHz is not mounted on DA1453x Pro-DB.	No Title Found	11	9.960000038146973	
UM-B-169	No Title Found	12	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	12	12.0	
Description	No Title Found	12	12.0	
User Manual	No Title Found	12	9.960000038146973	
Revision 1.0	No Title Found	12	9.960000038146973	
Feb 9, 2024	No Title Found	12	9.960000038146973	
CFR0012	No Title Found	12	7.559999942779541	
12 of 61	No Title Found	12	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	12	7.559999942779541	
Table 4. DA1453x Pro-DB and Pro-MB signals assignment (default)	No Title Found	12	9.960000038146973	
DA14531-	No Title Found	12	9.0	
FCGQFN24	No Title Found	12	9.0	
376-04-x	No Title Found	12	9.0	
DA14531-	No Title Found	12	9.0	
WLCSP1	No Title Found	12	9.0	
7	No Title Found	12	9.0	
376-05-x	No Title Found	12	9.0	
DA14535-	No Title Found	12	9.0	
FCGQFN24	No Title Found	12	9.0	
610-02-x	No Title Found	12	9.0	
DA14533	No Title Found	12	9.0	
-	No Title Found	12	9.0	
FCQFN2	No Title Found	12	9.0	
2	No Title Found	12	9.0	
610-05-x	No Title Found	12	9.0	
Function	No Title Found	12	9.0	
Mikrobu	No Title Found	12	9.0	
s PMOD	No Title Found	12	9.0	
Trigger	No Title Found	12	9.0	
signals	No Title Found	12	9.0	
Position on	No Title Found	12	9.0	
Pro-MB	No Title Found	12	9.0	
connector(s)	No Title Found	12	9.0	
P0_0	No Title Found	12	9.0	
P0_0	No Title Found	12	9.0	
P0_0	No Title Found	12	9.0	
P0_0	No Title Found	12	9.0	
RST/MOSI	No Title Found	12	9.0	
X_MOSI,	No Title Found	12	9.0	
TRIG_1	No Title Found	12	9.0	
J1:4, J1:12,	No Title Found	12	9.0	
J1:26, J2:3,	No Title Found	12	9.0	
J24:4	No Title Found	12	9.0	
P0_1	No Title Found	12	9.0	
P0_1	No Title Found	12	9.0	
P0_1	No Title Found	12	9.0	
P0_1	No Title Found	12	9.0	
FCS	No Title Found	12	9.0	
X_AN	No Title Found	12	9.0	
TRIG_2	No Title Found	12	9.0	
J1:8, J2:4,	No Title Found	12	9.0	
J24:6	No Title Found	12	9.0	
P0_2	No Title Found	12	9.0	
P0_2	No Title Found	12	9.0	
P0_2	No Title Found	12	9.0	
P0_2	No Title Found	12	9.0	
SW_CLK	No Title Found	12	9.0	
X_SCL,	No Title Found	12	9.0	
J1:22, J2:5	No Title Found	12	9.0	
P0_3	No Title Found	12	9.0	
P0_3	No Title Found	12	9.0	
P0_3	No Title Found	12	9.0	
P0_3	No Title Found	12	9.0	
MISO	No Title Found	12	9.0	
(	No Title Found	12	9.0	
Note 1	No Title Found	12	9.0	
)	No Title Found	12	9.0	
X_MISO	No Title Found	12	9.0	
J1:10, J2:6	No Title Found	12	9.0	
P0_4	No Title Found	12	9.0	
P0_4	No Title Found	12	9.0	
P0_4	No Title Found	12	9.0	
P0_4	No Title Found	12	9.0	
SCK	No Title Found	12	9.0	
X_CLK	No Title Found	12	9.0	
J1:6, J2:7	No Title Found	12	9.0	
P0_5	No Title Found	12	9.0	
P0_5	No Title Found	12	9.0	
P0_5	No Title Found	12	9.0	
P0_5	No Title Found	12	9.0	
RxTx	No Title Found	12	9.0	
(	No Title Found	12	9.0	
Note 2	No Title Found	12	9.0	
)	No Title Found	12	9.0	
X_TX	No Title Found	12	9.0	
TRIG_3	No Title Found	12	9.0	
J1:20, J2:8,	No Title Found	12	9.0	
J24:8	No Title Found	12	9.0	
P0_6	No Title Found	12	9.0	
P0_6	No Title Found	12	9.0	
P0_6	No Title Found	12	9.0	
X_RX	No Title Found	12	9.0	
TRIG_4	No Title Found	12	9.0	
J2:9, J24:10	No Title Found	12	9.0	
P0_7	No Title Found	12	9.0	
P0_7	No Title Found	12	9.0	
P0_7	No Title Found	12	9.0	
P0_7	No Title Found	12	9.0	
X_CSn	No Title Found	12	9.0	
TRIG_5	No Title Found	12	9.0	
J2:10, J24:12	No Title Found	12	9.0	
P0_8	No Title Found	12	9.0	
P0_8	No Title Found	12	9.0	
P0_8	No Title Found	12	9.0	
P0_8	No Title Found	12	9.0	
X_SDA	No Title Found	12	9.0	
TRIG_6	No Title Found	12	9.0	
J2:11, J24:14	No Title Found	12	9.0	
P0_9	No Title Found	12	9.0	
P0_9	No Title Found	12	9.0	
P0_9	No Title Found	12	9.0	
X_PWM	No Title Found	12	9.0	
TRIG_7	No Title Found	12	9.0	
J2:12, J24:16	No Title Found	12	9.0	
P0_10	No Title Found	12	9.0	
P0_10	No Title Found	12	9.0	
P0_10	No Title Found	12	9.0	
SW_DIO/	No Title Found	12	9.0	
SW3-	No Title Found	12	9.0	
Button	No Title Found	12	9.0	
X_RSTn	No Title Found	12	9.0	
J1:24, J2:13,	No Title Found	12	9.0	
J19:3,	No Title Found	12	9.0	
P0_11	No Title Found	12	9.0	
P0_11	No Title Found	12	9.0	
SW2-	No Title Found	12	9.0	
Button	No Title Found	12	9.0	
X_INT	No Title Found	12	9.0	
TRIG_0	No Title Found	12	9.0	
J2:14, J19:1,	No Title Found	12	9.0	
J24:2	No Title Found	12	9.0	
Note 1	No Title Found	12	9.0	
For DA14531-WLCSP17 (376-05-x), P0_3 is assigned as RxTx.	No Title Found	12	9.0	
Note 2	No Title Found	12	9.0	
For DA14531-WLCSP17(376-05-x), P0_5 is assigned as SW_DIO.	No Title Found	12	9.0	
UM-B-169	No Title Found	13	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	13	12.0	
Description	No Title Found	13	12.0	
User Manual	No Title Found	13	9.960000038146973	
Revision 1.0	No Title Found	13	9.960000038146973	
Feb 9, 2024	No Title Found	13	9.960000038146973	
CFR0012	No Title Found	13	7.559999942779541	
13 of 61	No Title Found	13	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	13	7.559999942779541	
5	No Title Found	13	14.039999961853027	
DA1453x Pro-DB	No Title Found	13	14.039999961853027	
The system on DA1453x Pro-DB consists of the DA1453x SoC, XTALs, power section, and radio	No Title Found	13	9.960000038146973	
section. The system block diagram is presented in	No Title Found	13	9.960000038146973	
Figure 5	No Title Found	13	9.960000038146973	
and the actual component’s location in	No Title Found	13	9.960000038146973	
Figure 6	No Title Found	13	9.960000038146973	
for DA14531 and DA14535 and	No Title Found	13	9.960000038146973	
Figure 7	No Title Found	13	9.960000038146973	
for the DA14533.	No Title Found	13	9.960000038146973	
U1	No Title Found	13	13.275291442871094	
DA1453x	No Title Found	13	13.275291442871094	
J1	No Title Found	13	10.310906410217285	
J4	No Title Found	13	10.32931900024414	
Y1	No Title Found	13	8.856331825256348	
32 MHz	No Title Found	13	8.856331825256348	
Y2	No Title Found	13	8.856331825256348	
32.768 kHz	No Title Found	13	8.856331825256348	
No mounted	No Title Found	13	8.856331825256348	
Memory	No Title Found	13	7.383345127105713	
U3 -No	No Title Found	13	7.383345127105713	
Mounted	No Title Found	13	7.383345127105713	
Coin cell	No Title Found	13	7.364933013916016	
3.0 V	No Title Found	13	7.383345127105713	
BAT1	No Title Found	13	6.628439903259277	
Optional for	No Title Found	13	6.646852016448975	
DA14533/5	No Title Found	13	6.646852016448975	
Printed	No Title Found	13	8.856331825256348	
Antenna	No Title Found	13	8.856331825256348	
L1	No Title Found	13	7.383345127105713	
SMA	No Title Found	13	8.837919235229492	
J3	No Title Found	13	8.837919235229492	
VBAT_Lo	No Title Found	13	7.364933013916016	
VBAT_Hi	No Title Found	13	7.383345127105713	
C2	No Title Found	13	7.383345127105713	
C1	No Title Found	13	7.383345127105713	
J1	No Title Found	13	7.383345127105713	
R-	No Title Found	13	7.383345127105713	
MUX	No Title Found	13	7.383345127105713	
RST,	No Title Found	13	7.383345127105713	
GPIOs	No Title Found	13	7.383345127105713	
M	No Title Found	13	8.856331825256348	
LPF	No Title Found	13	8.856331825256348	
VBAT	No Title Found	13	6.628439903259277	
VL+	No Title Found	13	6.646852016448975	
SPI	No Title Found	13	6.628439903259277	
SWD	No Title Found	13	6.646852016448975	
Figure 5. System block diagram of DA1453x Pro-DB	No Title Found	13	9.960000038146973	
Figure 6. DA14531 and DA14535 Component’s placement	No Title Found	13	9.960000038146973	
UM-B-169	No Title Found	14	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	14	12.0	
Description	No Title Found	14	12.0	
User Manual	No Title Found	14	9.960000038146973	
Revision 1.0	No Title Found	14	9.960000038146973	
Feb 9, 2024	No Title Found	14	9.960000038146973	
CFR0012	No Title Found	14	7.559999942779541	
14 of 61	No Title Found	14	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	14	7.559999942779541	
Figure 7. DA14533 Component’s placement	No Title Found	14	9.960000038146973	
Components:	No Title Found	14	9.960000038146973	
1. Antenna selection network	No Title Found	14	9.960000038146973	
2. DA1453x SoC (U1)	No Title Found	14	9.960000038146973	
3. 32.768 kHz XTAL (Y2 - Not mounted)	No Title Found	14	9.960000038146973	
4. R-Multiplexer	No Title Found	14	9.960000038146973	
5. Debug Connector	No Title Found	14	9.960000038146973	
6. SPI Flash (Not mounted for DA14531 & DA14535)	No Title Found	14	9.960000038146973	
7. Interface Connector with DA1453x Pro-MB	No Title Found	14	9.960000038146973	
8. Current Measurement Points	No Title Found	14	9.960000038146973	
9. 32 MHz XTAL (Y1)	No Title Found	14	9.960000038146973	
10. SMA Connector (Not mounted)	No Title Found	14	9.960000038146973	
11. Embedded Printed Antenna	No Title Found	14	9.960000038146973	
•	No Title Found	14	9.960000038146973	
BLE SoC (U1)	No Title Found	14	9.960000038146973	
: DA1453x is an ultra-low power SoC integrating a 2.4 GHz transceiver and an	No Title Found	14	9.960000038146973	
ARM CortexM0+TM microcontroller with 64 kB of RAM and 12 kB of One-Time Programmable	No Title Found	14	9.960000038146973	
memory (OTP).	No Title Found	14	9.960000038146973	
•	No Title Found	14	9.960000038146973	
32 MHz XTAL (Y1)	No Title Found	14	9.960000038146973	
: The main clock of the system is generated from a 32 MHz XTAL which is	No Title Found	14	9.960000038146973	
connected to th	No Title Found	14	9.960000038146973	
e internal clock oscillator. The selected crystal for DA14535 is the	No Title Found	14	9.960000038146973	
XRCGB32M000F1SBAR0	No Title Found	14	9.960000038146973	
of Murata, whereas for DA14533 the XRCGE32M000FBA2FR0 of	No Title Found	14	9.960000038146973	
Murata is used.	No Title Found	14	9.960000038146973	
•	No Title Found	14	9.960000038146973	
32.768 kHz XTAL (Y2, NP)	No Title Found	14	9.960000038146973	
: A crystal of 32.768 kHz can be placed on the pins P0_3 and P0_4 of	No Title Found	14	9.960000038146973	
DA1453x. A crystal that can be used is the ECS-.327-7-12QS-TRfrom ECS. In most applications	No Title Found	14	9.960000038146973	
the DA1453x can run with good accuracy with its internal RC oscillator (RCX) and therefore the	No Title Found	14	9.960000038146973	
XTAL32k is not needed. For applications with more demanding accuracy/drift characteristics,	No Title Found	14	9.960000038146973	
such as timekeeping, using the XTAL32k is considered a suitable solution.	No Title Found	14	9.960000038146973	
By default, XTAL32k is not assembled on Pro-DB. Internal RC clock is used. P0_3 and P0_4	No Title Found	14	9.960000038146973	
pins are assigned to other SPI data flash (mounted on Pro-MB).	No Title Found	14	9.960000038146973	
UM-B-169	No Title Found	15	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	15	12.0	
Description	No Title Found	15	12.0	
User Manual	No Title Found	15	9.960000038146973	
Revision 1.0	No Title Found	15	9.960000038146973	
Feb 9, 2024	No Title Found	15	9.960000038146973	
CFR0012	No Title Found	15	7.559999942779541	
15 of 61	No Title Found	15	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	15	7.559999942779541	
●	No Title Found	15	11.039999961853027	
RF section:	No Title Found	15	9.960000038146973	
see section	No Title Found	15	9.960000038146973	
5.2.	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
R-Multiplexer:	No Title Found	15	9.960000038146973	
a group of 0 Ω configuration resistors can be placed/removed as needed to	No Title Found	15	9.960000038146973	
hardwire various peripherals to the DA1453x pins as desired. See section	No Title Found	15	9.960000038146973	
5.3	No Title Found	15	9.960000038146973	
.	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
SPI Flash:	No Title Found	15	9.960000038146973	
see section	No Title Found	15	9.960000038146973	
5.4	No Title Found	15	9.960000038146973	
.	No Title Found	15	9.960000038146973	
5.1	No Title Found	15	12.0	
Power section of Pro-DB	No Title Found	15	12.0	
The DA1453x Pro-DB can operate in the Buck mode (supplied VBAT_HIGH with 1.8 V to 3.6 V) or in	Power section of Pro-DB	15	9.960000038146973	12.0
the Boost mode (supplied VBAT_LOW with 1.2 V to 1.65 V). The available voltage levels provided by	Power section of Pro-DB	15	9.960000038146973	12.0
DA1453x Pro-MB are:	Power section of Pro-DB	15	9.960000038146973	12.0
●	Power section of Pro-DB	15	11.039999961853027	12.0
1.25 V	Power section of Pro-DB	15	9.960000038146973	12.0
●	Power section of Pro-DB	15	11.039999961853027	12.0
1.8 V	Power section of Pro-DB	15	9.960000038146973	12.0
●	Power section of Pro-DB	15	11.039999961853027	12.0
3 V	Power section of Pro-DB	15	9.960000038146973	12.0
●	No Title Found	15	11.039999961853027	
3.3 V	No Title Found	15	9.960000038146973	
A jumper must be applied between pins 1 and 2 on header J2. This jumper shorts the VH+ and VH-	No Title Found	15	9.960000038146973	
for the VBAT_Hi pin of the DA1453x SoC.	No Title Found	15	9.960000038146973	
For the Boost mode, the jumper must be applied between pins 3 and 4 on header J2. This jumper	No Title Found	15	9.960000038146973	
short VL+ and VL- for the VBAT_Lo pin of the DA1453x SoC.	No Title Found	15	9.960000038146973	
Both jumpers should be mounted.	No Title Found	15	9.960000038146973	
For any other voltage level, the DA1453x Pro-DB must be supplied from external power supply.	No Title Found	15	9.960000038146973	
Right angle J2 header can be used to connect external ammeter and measure the power	No Title Found	15	9.960000038146973	
consumption of the DA1453x Pro-DB, or to externally power supply the Pro-DB when in stand-alone	No Title Found	15	9.960000038146973	
(not mounted on a DA1453x Pro-MB).	No Title Found	15	9.960000038146973	
Figure 8. Current measurement connector, J2	No Title Found	15	9.960000038146973	
Current measurement for:	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
Buck mode: connect ammeter at J2 pin 1-2	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
Boost mode: connect ammeter at J2 pin 4-5.	No Title Found	15	9.960000038146973	
Power supplied externally (for example, Benchtop power supply) for:	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
Buck mode: connect GND to J2 pin 3 and power supply voltage from 1.8 V to 3.6 V at pin 1.	No Title Found	15	9.960000038146973	
●	No Title Found	15	11.039999961853027	
Boost mode: connect GND to J2 pin 3 and power supply voltage from 1.25 V to 1.65 V at pin 5.	No Title Found	15	9.960000038146973	
UM-B-169	No Title Found	16	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	16	12.0	
Description	No Title Found	16	12.0	
User Manual	No Title Found	16	9.960000038146973	
Revision 1.0	No Title Found	16	9.960000038146973	
Feb 9, 2024	No Title Found	16	9.960000038146973	
CFR0012	No Title Found	16	7.559999942779541	
16 of 61	No Title Found	16	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	16	7.559999942779541	
5.2	No Title Found	16	12.0	
RF section	No Title Found	16	12.0	
A printed F-antenna (ANT1) is used as the radiating element for the DA1453x Pro-DB.	RF section	16	9.960000038146973	12.0
The DA1453x RFIO pin is connected to the printed antenna through an RF strip-line and a matching	RF section	16	9.960000038146973	12.0
circuit.	RF section	16	9.960000038146973	12.0
To perform conducted RF measurements, proceed with the following hardware modifications (	RF section	16	9.960000038146973	12.0
Figure	RF section	16	9.960000038146973	12.0
10	RF section	16	9.960000038146973	12.0
):	RF section	16	9.960000038146973	12.0
1. Remove Z9	RF section	16	9.960000038146973	12.0
2. Assemble Z7 = 10 pF	No Title Found	16	9.960000038146973	
3. Assemble J3, SMA Female Socket 50 Ω Board Edge (	No Title Found	16	9.960000038146973	
142-0761-861)	No Title Found	16	9.960000038146973	
of Cinch Connectivity	No Title Found	16	9.960000038146973	
Solutions Johnson).	No Title Found	16	9.960000038146973	
Figure 9. RF section of DA1453x Pro-DB	No Title Found	16	9.960000038146973	
Figure 10. Modifications required for performing RF measurements	No Title Found	16	9.960000038146973	
5.3	No Title Found	16	12.0	
R-Multiplexer	No Title Found	16	12.0	
Different combinations of GPIOs assignment can be selected through the R-multiplexer. The default	R-Multiplexer	16	9.960000038146973	12.0
setup is shown in	R-Multiplexer	16	9.960000038146973	12.0
Figure 11.	R-Multiplexer	16	9.960000038146973	12.0
Depending the setup of the R-Multiplexer on the DA1453x Pro-DB,	R-Multiplexer	16	9.960000038146973	12.0
modifications on software may also require.	R-Multiplexer	16	9.960000038146973	12.0
UM-B-169	No Title Found	17	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	17	12.0	
Description	No Title Found	17	12.0	
User Manual	No Title Found	17	9.960000038146973	
Revision 1.0	No Title Found	17	9.960000038146973	
Feb 9, 2024	No Title Found	17	9.960000038146973	
CFR0012	No Title Found	17	7.559999942779541	
17 of 61	No Title Found	17	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	17	7.559999942779541	
Figure 11. R-Multiplexer default setup	No Title Found	17	9.960000038146973	
5.4	No Title Found	17	12.0	
SPI Data flash on Pro-DB	No Title Found	17	12.0	
The DA14533 Pro-DB (610-05-x) and the DA14535 Pro-DB (610-02-x) have the option to support	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
SPI flash (U3 or U3x) as shown in	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
Figure 12	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
. The proposed part is the AT25DF021A-MAHN-T for the	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
DA14535 Pro-DB and the Winbond W25X20CV for the DA14533 Pro-DB (610-05-x). By default, the	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
SPI Flash is not mounted on the Pro-DB.	SPI Data flash on Pro-DB	17	9.960000038146973	12.0
UM-B-169	No Title Found	18	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	18	12.0	
Description	No Title Found	18	12.0	
User Manual	No Title Found	18	9.960000038146973	
Revision 1.0	No Title Found	18	9.960000038146973	
Feb 9, 2024	No Title Found	18	9.960000038146973	
CFR0012	No Title Found	18	7.559999942779541	
18 of 61	No Title Found	18	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	18	7.559999942779541	
Figure 12. Optional SPI-Flash on the DA14533, DA14535 Pro-DB	No Title Found	18	9.960000038146973	
NOTE	No Title Found	18	9.0	
If memory is mounted on Pro-DB, you must ensure that resistor R19 to R22 (see	No Title Found	18	9.0	
Figure 11	No Title Found	18	9.0	
) are mounted and	No Title Found	18	9.0	
the jumpers on J1 header on Pro-MB (Jumpers at positions 1-2, 3-4, 5-6, 7-8 and 9-10) are removed.	No Title Found	18	9.0	
UM-B-169	No Title Found	19	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	19	12.0	
Description	No Title Found	19	12.0	
User Manual	No Title Found	19	9.960000038146973	
Revision 1.0	No Title Found	19	9.960000038146973	
Feb 9, 2024	No Title Found	19	9.960000038146973	
CFR0012	No Title Found	19	7.559999942779541	
19 of 61	No Title Found	19	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	19	7.559999942779541	
6	No Title Found	19	14.039999961853027	
DA1453x Pro-MB	No Title Found	19	14.039999961853027	
The block diagram and the actual component locations of the Pro-MB are shown in	No Title Found	19	9.960000038146973	
Figure 13	No Title Found	19	9.960000038146973	
and	No Title Found	19	9.960000038146973	
Figure 14	No Title Found	19	9.960000038146973	
, respectively.	No Title Found	19	9.960000038146973	
Current	No Title Found	19	10.989736557006836	
Sense	No Title Found	19	10.989736557006836	
(PMM2)	No Title Found	19	10.989736557006836	
PCI-e Connector (J3)	No Title Found	19	16.51894760131836	
USB	No Title Found	19	6.902928352355957	
U14	No Title Found	19	8.242302894592285	
U13	No Title Found	19	8.27664566040039	
U11	No Title Found	19	9.650362968444824	
USB HUB	No Title Found	19	9.650362968444824	
U5	No Title Found	19	8.27664566040039	
J5	No Title Found	19	9.616019248962402	
Configure U15 output	No Title Found	19	6.902928352355957	
from 1.25V to 3.3V	No Title Found	19	6.868585109710693	
VLDO	No Title Found	19	6.902928352355957	
J9	No Title Found	19	9.616019248962402	
PMM2	No Title Found	19	8.242302894592285	
Selection	No Title Found	19	8.27664566040039	
VLDO_EN	No Title Found	19	5.494868278503418	
J4	No Title Found	19	9.650362968444824	
BUCK, BOOST	No Title Found	19	8.242302894592285	
or BYPASS Selection	No Title Found	19	8.27664566040039	
SW1	No Title Found	19	7.589787006378174	
U2 - SPI	No Title Found	19	9.616019248962402	
FLASH	No Title Found	19	9.650362968444824	
U25	No Title Found	19	9.650362968444824	
MCU	No Title Found	19	9.616019248962402	
(JTAG)	No Title Found	19	9.616019248962402	
RST	No Title Found	19	7.589787006378174	
U21	No Title Found	19	6.868585109710693	
VLDO_EN	No Title Found	19	6.181726932525635	
U_RST	No Title Found	19	6.216070175170898	
U_RST	No Title Found	19	5.494868278503418	
J1	No Title Found	19	11.024078369140625	
USB1	No Title Found	19	9.616019248962402	
U12	No Title Found	19	9.650362968444824	
FTDI	No Title Found	19	9.650362968444824	
(UART)	No Title Found	19	9.650362968444824	
MikroBus /	No Title Found	19	9.616019248962402	
PMOD	No Title Found	19	9.616019248962402	
J2	No Title Found	19	10.989736557006836	
U26	No Title Found	19	7.55544376373291	
(POR)	No Title Found	19	7.55544376373291	
3V3	No Title Found	19	6.868585109710693	
3V3	No Title Found	19	6.902928352355957	
3V3	No Title Found	19	6.902928352355957	
3V3_HUB	No Title Found	19	6.868585109710693	
3V3_HUB	No Title Found	19	6.868585109710693	
Level	No Title Found	19	9.616019248962402	
Translator	No Title Found	19	9.650362968444824	
JTAG	No Title Found	19	6.868585109710693	
USB	No Title Found	19	6.868585109710693	
V_FL	No Title Found	19	6.868585109710693	
SPI	No Title Found	19	6.902928352355957	
V3 (V	No Title Found	19	7.55544376373291	
HIGH	No Title Found	19	4.808009624481201	
)	No Title Found	19	7.55544376373291	
V3 (V	No Title Found	19	7.589787006378174	
HIGH	No Title Found	19	4.808009624481201	
)	No Title Found	19	7.589787006378174	
V1 (V	No Title Found	19	7.55544376373291	
LOW	No Title Found	19	4.808009624481201	
)	No Title Found	19	7.55544376373291	
V_LDO	No Title Found	19	6.902928352355957	
GPIOs	No Title Found	19	11.024078369140625	
USB	No Title Found	19	6.902928352355957	
USB	No Title Found	19	6.868585109710693	
PC	No Title Found	19	12.397796630859375	
Figure 13. DA1453x Pro-MB block diagram	No Title Found	19	9.960000038146973	
UM-B-169	No Title Found	20	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	20	12.0	
Description	No Title Found	20	12.0	
User Manual	No Title Found	20	9.960000038146973	
Revision 1.0	No Title Found	20	9.960000038146973	
Feb 9, 2024	No Title Found	20	9.960000038146973	
CFR0012	No Title Found	20	7.559999942779541	
20 of 61	No Title Found	20	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	20	7.559999942779541	
Figure 14. Pro-MB (610-01-A)	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Configuration header (J1)	No Title Found	20	9.960000038146973	
: wiring of the available peripherals to the DA1453x pins is done with	No Title Found	20	9.960000038146973	
the help of jumpers on J1 (for default configurations) or with jumper wires from J1 to J2 when	No Title Found	20	9.960000038146973	
custom pin assignments of the peripherals to DA1453x pins are desired.	No Title Found	20	9.960000038146973	
○	No Title Found	20	11.039999961853027	
RxTx single wire UART (J10)	No Title Found	20	9.960000038146973	
: generates RxTx single wire UART by shorting UTX and URX	No Title Found	20	9.960000038146973	
through a 1 KΩ resistor. Jumpers must be applied from J10.1 to J1.15 and from J10.2 to	No Title Found	20	9.960000038146973	
J15.17.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Voltage translators (U3, U22, U23):	No Title Found	20	9.960000038146973	
transfer the data between DA1453x and FTDI (U12)/JTAG	No Title Found	20	9.960000038146973	
(U25) in proper voltage level.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
USB to UART (U12)	No Title Found	20	9.960000038146973	
: provides communication between DA1453x UART port and PC. It also	No Title Found	20	9.960000038146973	
transfers the current measurement samples to PC.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
DCDC configuration (J4)	No Title Found	20	9.960000038146973	
: selects either Boost, Buck, or Bypass mode for DA1453x. See	No Title Found	20	9.960000038146973	
Section	No Title Found	20	9.960000038146973	
6.1.4.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Pro-DB mating header (J3)	No Title Found	20	9.960000038146973	
: an interface mating connector for the DA1453x Pro-DB.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Software trigger options (J24):	No Title Found	20	9.960000038146973	
configures the pin assignment for trigger through SmartSnippets	No Title Found	20	9.960000038146973	
Toolbox software application (when PPM2 is mounted).	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Push button selection header (J19)	No Title Found	20	9.960000038146973	
: configures the assignment of onboard push buttons to	No Title Found	20	9.960000038146973	
certain DA1453x pins.	No Title Found	20	9.960000038146973	
●	No Title Found	20	11.039999961853027	
Reset push button (SW1)	No Title Found	20	9.960000038146973	
: push button used to reset DA1453x.	No Title Found	20	9.960000038146973	
○	No Title Found	20	11.039999961853027	
P0	No Title Found	20	9.960000038146973	
_0 must be configured as RST pin to generate reset from push button SW1.	No Title Found	20	9.960000038146973	
UM-B-169	No Title Found	21	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	21	12.0	
Description	No Title Found	21	12.0	
User Manual	No Title Found	21	9.960000038146973	
Revision 1.0	No Title Found	21	9.960000038146973	
Feb 9, 2024	No Title Found	21	9.960000038146973	
CFR0012	No Title Found	21	7.559999942779541	
21 of 61	No Title Found	21	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	21	7.559999942779541	
●	No Title Found	21	11.039999961853027	
User LED and C-Trig (J8)	No Title Found	21	9.960000038146973	
: enables use of the onboard LED (D5) and the software cursor.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
GPIO monitoring header (J2)	No Title Found	21	9.960000038146973	
: exposes all the pins of the Pro-DB to allow wiring for custom	No Title Found	21	9.960000038146973	
configurations as well as monitoring of the hardware signals.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
GND test point (TP4):	No Title Found	21	9.960000038146973	
can be used as GND point for external instruments connection such as	No Title Found	21	9.960000038146973	
oscilloscope.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
Reset options (J23):	No Title Found	21	9.960000038146973	
additional reset options (POR, software reset).	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
MikroBus interface connector (J17 and J18)	No Title Found	21	9.960000038146973	
: sockets for plugging in MikroBus interface add-on	No Title Found	21	9.960000038146973	
boards. It is multiplexed with PMOD (to be used with DA1453x).	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
PMOD (J20)	No Title Found	21	9.960000038146973	
: socket for plugging in PMOD interface add-on boards. It is multiplexed with MikroBUS	No Title Found	21	9.960000038146973	
(to be used with DA1453x).	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
PMM2 interface connector (J7)	No Title Found	21	9.960000038146973	
: PMM2 interface male connector. Using this current	No Title Found	21	9.960000038146973	
measurement circuit monitors the current consumption of Pro-DB through SmartSnippets Toolbox	No Title Found	21	9.960000038146973	
software application.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
Push buttons (SW2 and SW3)	No Title Found	21	9.960000038146973	
: general use push buttons.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
USB connector (USB1)	No Title Found	21	9.960000038146973	
: mini-USB connector for power supply and data interfacing to the PC.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
USB hub (U11)	No Title Found	21	9.960000038146973	
: connects JTAG and UART interfaces to the PC.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
VLDO selection (J5)	No Title Found	21	9.960000038146973	
: selects the output voltage generated for the Pro-DB board (1.25 V, 1.8 V,	No Title Found	21	9.960000038146973	
3.0 V, or 3.3 V).	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
Adjustable LDO (U5):	No Title Found	21	9.960000038146973	
provides power to DA1453x Pro-DB. Adjustable.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
PMM2 selection (J26)	No Title Found	21	9.960000038146973	
: enables the current sense circuit (default PMM2 is not mounted). Also, it	No Title Found	21	9.960000038146973	
allows the simultaneous connection of an external current measurement instrument to measure	No Title Found	21	9.960000038146973	
and profile the current consumption of D1453x.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
USB to JTAG (U4)	No Title Found	21	9.960000038146973	
: provides communication between DA1453x JTAG and PC.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
SPI Flash (U2):	No Title Found	21	9.960000038146973	
on-board SPI Flash (AT25DF021A-MAHN-T)	No Title Found	21	9.960000038146973	
connected with DA1453x through J1	No Title Found	21	9.960000038146973	
configuration header.	No Title Found	21	9.960000038146973	
●	No Title Found	21	11.039999961853027	
GND Test Point (J21):	No Title Found	21	9.960000038146973	
can be used as GND point for external instruments connection such as	No Title Found	21	9.960000038146973	
oscilloscope.	No Title Found	21	9.960000038146973	
UM-B-169	No Title Found	22	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	22	12.0	
Description	No Title Found	22	12.0	
User Manual	No Title Found	22	9.960000038146973	
Revision 1.0	No Title Found	22	9.960000038146973	
Feb 9, 2024	No Title Found	22	9.960000038146973	
CFR0012	No Title Found	22	7.559999942779541	
22 of 61	No Title Found	22	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	22	7.559999942779541	
6.1	No Title Found	22	12.0	
Power section	No Title Found	22	12.0	
The power tree of the DA1453x Pro-MB is shown in	Power section	22	9.960000038146973	12.0
Figure 15	Power section	22	9.960000038146973	12.0
.	Power section	22	9.960000038146973	12.0
Figure 15. DA1453x Pro-MB power tree block diagram	No Title Found	22	9.960000038146973	
The power tree of the DA1453x Pro-MB, as shown in	No Title Found	22	9.960000038146973	
Figure 15	No Title Found	22	9.960000038146973	
, consist of two routes, one is used to	No Title Found	22	9.960000038146973	
power supply he components of the DA1453x Pro-MB and the other is used to power supply the	No Title Found	22	9.960000038146973	
DA1453x Pro-DB and its components.	No Title Found	22	9.960000038146973	
6.1.1	No Title Found	22	11.039999961853027	
DA1453x Pro-DB power circuit	No Title Found	22	11.039999961853027	
The DA1453x Pro-DB is powered from the adjustable LDO (U5). The output voltage of the U5 can be	No Title Found	22	9.960000038146973	
configured from the jumpers on the J5 header and provide the following voltage levels:	No Title Found	22	9.960000038146973	
●	No Title Found	22	11.039999961853027	
1.25 V	No Title Found	22	9.960000038146973	
●	No Title Found	22	11.039999961853027	
1.8 V	No Title Found	22	9.960000038146973	
●	No Title Found	22	11.039999961853027	
3.0 V	No Title Found	22	9.960000038146973	
●	No Title Found	22	11.039999961853027	
3.3 V	No Title Found	22	9.960000038146973	
NOTE	No Title Found	22	9.0	
Consider the DA1453x Pro-DB mode (Buck, Boost, or By-pass) before selecting the U5 output voltage. The	No Title Found	22	9.0	
1.25 V used for the Boost mode, while from 1.8 V (and above) is used for Buck and By-Pass modes. The	No Title Found	22	9.0	
mode selection is described in Section	No Title Found	22	9.0	
6.1.4	No Title Found	22	9.0	
.	No Title Found	22	9.0	
UM-B-169	No Title Found	23	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	23	12.0	
Description	No Title Found	23	12.0	
User Manual	No Title Found	23	9.960000038146973	
Revision 1.0	No Title Found	23	9.960000038146973	
Feb 9, 2024	No Title Found	23	9.960000038146973	
CFR0012	No Title Found	23	7.559999942779541	
23 of 61	No Title Found	23	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	23	7.559999942779541	
Pro	No Title Found	23	9.960000038146973	
Figure 16. DA1453x Pro-DB power circuit	No Title Found	23	9.960000038146973	
The configuration for the jumpers of the J5 header is presented on the top mask of the DA1453x Pro-	No Title Found	23	9.960000038146973	
MB as shown in the	No Title Found	23	9.960000038146973	
Figure 17	No Title Found	23	9.960000038146973	
.	No Title Found	23	9.960000038146973	
Figure 17. J5 header configuration	No Title Found	23	9.960000038146973	
6.1.2	No Title Found	23	11.039999961853027	
U26 (POR)	No Title Found	23	11.039999961853027	
To generate the Power ON Reset function for the DA1453x Pro-DB the load switch U26 is used as	No Title Found	23	9.960000038146973	
shown in	No Title Found	23	9.960000038146973	
Figure 16	No Title Found	23	9.960000038146973	
. By default, the load switch is always ON and can be disabled when B2 pin is	No Title Found	23	9.960000038146973	
connected to GND. More details for the POR functionality, see Section	No Title Found	23	9.960000038146973	
6.5	No Title Found	23	9.960000038146973	
.	No Title Found	23	9.960000038146973	
6.1.3	No Title Found	23	11.039999961853027	
PMM2	No Title Found	23	11.039999961853027	
PMM2 is an external module which can be attached on the DA1453x Pro-MB. The usage of this	No Title Found	23	9.960000038146973	
module is to measure the current consumption of the DA1453x Pro-DB as well as to monitor signals	No Title Found	23	9.960000038146973	
and various power rails. The PMM2 is connected to J7, M.2 Socket and stabilized with a screw at T6	No Title Found	23	9.960000038146973	
point, as shown in	No Title Found	23	9.960000038146973	
Figure 18	No Title Found	23	9.960000038146973	
and	No Title Found	23	9.960000038146973	
Figure 19	No Title Found	23	9.960000038146973	
.	No Title Found	23	9.960000038146973	
UM-B-169	No Title Found	24	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	24	12.0	
Description	No Title Found	24	12.0	
User Manual	No Title Found	24	9.960000038146973	
Revision 1.0	No Title Found	24	9.960000038146973	
Feb 9, 2024	No Title Found	24	9.960000038146973	
CFR0012	No Title Found	24	7.559999942779541	
24 of 61	No Title Found	24	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	24	7.559999942779541	
Figure 18. PMM2 slot	No Title Found	24	9.960000038146973	
NOTE	No Title Found	24	9.0	
SmartSnippets Toolbox software application is required for the monitoring of the voltages and the signals	No Title Found	24	9.0	
triggering.	No Title Found	24	9.0	
Figure 19. How to mount the PMM2 on the DA1453x Pro-MB	No Title Found	24	9.960000038146973	
Even if the PPM2 is mounted on the DA1453x Pro-MB, you can either include or exclude it from the	No Title Found	24	9.960000038146973	
system, by configuring the jumpers on J9 header. On the top mask of the DA1453x Pro-DB is	No Title Found	24	9.960000038146973	
described the jumper configuration for the J9 header, as shown in	No Title Found	24	9.960000038146973	
Figure 20	No Title Found	24	9.960000038146973	
.	No Title Found	24	9.960000038146973	
UM-B-169	No Title Found	25	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	25	12.0	
Description	No Title Found	25	12.0	
User Manual	No Title Found	25	9.960000038146973	
Revision 1.0	No Title Found	25	9.960000038146973	
Feb 9, 2024	No Title Found	25	9.960000038146973	
CFR0012	No Title Found	25	7.559999942779541	
25 of 61	No Title Found	25	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	25	7.559999942779541	
Figure 20. J9 header configuration	No Title Found	25	9.960000038146973	
6.1.4	No Title Found	25	11.039999961853027	
Mode selection (Buck, Boost, By-Pass)	No Title Found	25	11.039999961853027	
By applying proper jumper at J4 header, you can select among buck, boost, or bypass mode of	No Title Found	25	9.960000038146973	
operation of the DA1453x DC-DC converter. The supply pins are:	No Title Found	25	9.960000038146973	
●	No Title Found	25	11.039999961853027	
Jumper pin 1-2	No Title Found	25	9.960000038146973	
→	No Title Found	25	9.960000038146973	
Boost mode, V1 is connector to VBAT_LOW	No Title Found	25	9.960000038146973	
●	No Title Found	25	11.039999961853027	
Jumper pin 3-4	No Title Found	25	9.960000038146973	
→	No Title Found	25	9.960000038146973	
Buck mode, V3 is connector to VBAT_HIGH	No Title Found	25	9.960000038146973	
●	No Title Found	25	11.039999961853027	
Jumper pin 1-2 and pin 3-4	No Title Found	25	9.960000038146973	
→	No Title Found	25	9.960000038146973	
Bypass mode, V3 is connector to VBAT_HIGH and V1 is	No Title Found	25	9.960000038146973	
connector to VBAT_LOW (DA1453x DC-DC converter is disabled).	No Title Found	25	9.960000038146973	
NOTE	No Title Found	25	9.0	
The Buck mode is the default selected mode.	No Title Found	25	9.0	
Figure 21. J4 header mode selection	No Title Found	25	9.960000038146973	
UM-B-169	No Title Found	26	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	26	12.0	
Description	No Title Found	26	12.0	
User Manual	No Title Found	26	9.960000038146973	
Revision 1.0	No Title Found	26	9.960000038146973	
Feb 9, 2024	No Title Found	26	9.960000038146973	
CFR0012	No Title Found	26	7.559999942779541	
26 of 61	No Title Found	26	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	26	7.559999942779541	
On the top mask of the DA1453x Pro-MB is described the jumper configuration for the J4 header, as	No Title Found	26	9.960000038146973	
show in	No Title Found	26	9.960000038146973	
Figure 22	No Title Found	26	9.960000038146973	
. No modifications required on DA1453x Pro-DB.	No Title Found	26	9.960000038146973	
Figure 22. J4 header configuration	No Title Found	26	9.960000038146973	
6.2	No Title Found	26	12.0	
USB HUB	No Title Found	26	12.0	
The USB HUB of DA1453x Pro-MB is implemented by U11(	USB HUB	26	9.960000038146973	12.0
Figure 23	USB HUB	26	9.960000038146973	12.0
), USB2512B. This chip is	USB HUB	26	9.960000038146973	12.0
supplied with 3.3 V from U13 (	USB HUB	26	9.960000038146973	12.0
Figure 24	USB HUB	26	9.960000038146973	12.0
).	USB HUB	26	9.960000038146973	12.0
The signal PWR_ENABLE is generated from U11 and it is an active high signal. It enables the power	USB HUB	26	9.960000038146973	12.0
components (LDOs and DCDC converter) for UART, JTAG, and the current sensing circuit. The	USB HUB	26	9.960000038146973	12.0
system powers up only after the USB HUB is enumerated properly.	USB HUB	26	9.960000038146973	12.0
USB HUB operation is indicated through the green LED D4 on DA1453x Pro-MB. A 24 MHz crystal	USB HUB	26	9.960000038146973	12.0
(Y3) is required for chip operation.	No Title Found	26	9.960000038146973	
Figure 23. USB HUB circuitry	No Title Found	26	9.960000038146973	
UM-B-169	No Title Found	27	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	27	12.0	
Description	No Title Found	27	12.0	
User Manual	No Title Found	27	9.960000038146973	
Revision 1.0	No Title Found	27	9.960000038146973	
Feb 9, 2024	No Title Found	27	9.960000038146973	
CFR0012	No Title Found	27	7.559999942779541	
27 of 61	No Title Found	27	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	27	7.559999942779541	
Figure 24. USB HUB power supply	No Title Found	27	9.960000038146973	
6.3	No Title Found	27	12.0	
USB to UART	No Title Found	27	12.0	
The USB to UART function is implemented by U12, FT2232HL (	USB to UART	27	9.960000038146973	12.0
Figure 25	USB to UART	27	9.960000038146973	12.0
). A 12 MHz crystal (Y4) is	USB to UART	27	9.960000038146973	12.0
required for the chip operation. This chip is supplied with 3.3 V from U14 (	USB to UART	27	9.960000038146973	12.0
Figure 26	USB to UART	27	9.960000038146973	12.0
). U14 can be	USB to UART	27	9.960000038146973	12.0
enabled manually from J6 header. Not in use by default.	USB to UART	27	9.960000038146973	12.0
U12 functions are the following:	USB to UART	27	9.960000038146973	12.0
●	USB to UART	27	11.039999961853027	12.0
Connecting a PC to the UART port of DA1453x SoC	USB to UART	27	9.960000038146973	12.0
●	USB to UART	27	11.039999961853027	12.0
Connecting a PC to the power measurement module PMM2:	USB to UART	27	9.960000038146973	12.0
○	No Title Found	27	11.039999961853027	
SPI connection with ADC (U8)	No Title Found	27	9.960000038146973	
○	No Title Found	27	11.039999961853027	
Up to 8 triggers including Software cursor triggering (C_TRIG)	No Title Found	27	9.960000038146973	
●	No Title Found	27	11.039999961853027	
Reset capability of the DA1453x SoC through the T_RESET signal (not enabled).	No Title Found	27	9.960000038146973	
UART signals are connected to the DA1453x SoC through voltage translators as described in	No Title Found	27	9.960000038146973	
Section	No Title Found	27	9.960000038146973	
6.5	No Title Found	27	9.960000038146973	
, these can be accessed by the breakout header J2 as described in Section	No Title Found	27	9.960000038146973	
6.11	No Title Found	27	9.960000038146973	
.	No Title Found	27	9.960000038146973	
UM-B-169	No Title Found	28	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	28	12.0	
Description	No Title Found	28	12.0	
User Manual	No Title Found	28	9.960000038146973	
Revision 1.0	No Title Found	28	9.960000038146973	
Feb 9, 2024	No Title Found	28	9.960000038146973	
CFR0012	No Title Found	28	7.559999942779541	
28 of 61	No Title Found	28	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	28	7.559999942779541	
Figure 25. USB to UART circuitry	No Title Found	28	9.960000038146973	
Figure 26. USB to UART power supply	No Title Found	28	9.960000038146973	
UM-B-169	No Title Found	29	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	29	12.0	
Description	No Title Found	29	12.0	
User Manual	No Title Found	29	9.960000038146973	
Revision 1.0	No Title Found	29	9.960000038146973	
Feb 9, 2024	No Title Found	29	9.960000038146973	
CFR0012	No Title Found	29	7.559999942779541	
29 of 61	No Title Found	29	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	29	7.559999942779541	
6.4	No Title Found	29	12.0	
USB to JTAG	No Title Found	29	12.0	
The USB to JTAG function is implemented by U25, R7FA4M2AD3CFL (	USB to JTAG	29	9.960000038146973	12.0
Figure 27	USB to JTAG	29	9.960000038146973	12.0
). On the ROM of	USB to JTAG	29	9.960000038146973	12.0
U4, software from SEGGER is loaded. Its operation is indicated through the green LED D2 on the	USB to JTAG	29	9.960000038146973	12.0
DA1453x Pro-MB. This chip is supplied with 3.3 V from U14 (	USB to JTAG	29	9.960000038146973	12.0
Figure 26	USB to JTAG	29	9.960000038146973	12.0
). U14 can be enabled	USB to JTAG	29	9.960000038146973	12.0
manually from J6 header. Not in use by default.	USB to JTAG	29	9.960000038146973	12.0
U25 functions are the following:	USB to JTAG	29	9.960000038146973	12.0
•	USB to JTAG	29	9.960000038146973	12.0
Connecting a PC to the JTAG signals SWCLK, SWDIO of DA1453x SoC	USB to JTAG	29	9.960000038146973	12.0
•	No Title Found	29	9.960000038146973	
Reset capability of the DA1453x SoC through the T_RESET signal.	No Title Found	29	9.960000038146973	
JTAG signals are connected to the DA1453x SoC through voltage translators as described in Section	No Title Found	29	9.960000038146973	
6.6	No Title Found	29	9.960000038146973	
, these can be accessed by the breakout header J2 as described in Section	No Title Found	29	9.960000038146973	
6.11.	No Title Found	29	9.960000038146973	
Figure 27. USB to JTAG (U25)	No Title Found	29	9.960000038146973	
6.5	No Title Found	29	12.0	
Reset function	No Title Found	29	12.0	
At power-on and before booting in the chip, reset is active high, and it is assigned to on P0_0. After	Reset function	29	9.960000038146973	12.0
booting, reset assignment and operation is handled by software.	Reset function	29	9.960000038146973	12.0
DA1453x Pro-DB reset signal provided by the Pro-MB is connected to P0_0 through a 1 kΩ resistor,	Reset function	29	9.960000038146973	12.0
reset can be enabled:	Reset function	29	9.960000038146973	12.0
●	Reset function	29	11.039999961853027	12.0
By pressing the push button SW1	Reset function	29	9.960000038146973	12.0
●	Reset function	29	11.039999961853027	12.0
From JTAG interface (T_RST)	Reset function	29	9.960000038146973	12.0
●	No Title Found	29	11.039999961853027	
By the U_RSTn signal from UART – not enabled (a jumper must be placed at J23 pin 1-2 and –	No Title Found	29	9.960000038146973	
software required for enabling this feature.	No Title Found	29	9.960000038146973	
NOTE	No Title Found	29	9.0	
To enable reset on an application, P0_0 must be enabled in both hardware and software.	No Title Found	29	9.0	
To generate POR, a jumper must be placed at J23 pin 3-4 and press the SW1 push button.	No Title Found	29	9.0	
UM-B-169	No Title Found	30	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	30	12.0	
Description	No Title Found	30	12.0	
User Manual	No Title Found	30	9.960000038146973	
Revision 1.0	No Title Found	30	9.960000038146973	
Feb 9, 2024	No Title Found	30	9.960000038146973	
CFR0012	No Title Found	30	7.559999942779541	
30 of 61	No Title Found	30	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	30	7.559999942779541	
The block diagram of the reset circuit is presented in	No Title Found	30	9.960000038146973	
Figure 28	No Title Found	30	9.960000038146973	
, the circuit schematic in	No Title Found	30	9.960000038146973	
Figure 29	No Title Found	30	9.960000038146973	
and the actual position of the SW1 and J23 Header in	No Title Found	30	9.960000038146973	
Figure 30	No Title Found	30	9.960000038146973	
.	No Title Found	30	9.960000038146973	
U24	No Title Found	30	10.908750534057617	
U25	No Title Found	30	14.714128494262695	
J8	No Title Found	30	8.7161283493042	
T_RESET	No Title Found	30	7.085251331329346	
T_RST	No Title Found	30	7.085251331329346	
DA1453x Reset Line	No Title Found	30	8.7161283493042	
V_LDO	No Title Found	30	7.085251331329346	
SW1	No Title Found	30	7.085251331329346	
1.25V to 3.3V	No Title Found	30	7.085251331329346	
U21_1	No Title Found	30	10.890629768371582	
U21_2	No Title Found	30	10.908750534057617	
A1	No Title Found	30	7.085251331329346	
J23	No Title Found	30	8.7161283493042	
A2	No Title Found	30	7.085251331329346	
U26	No Title Found	30	7.085251331329346	
VLDO_EN	No Title Found	30	5.998000621795654	
POR	No Title Found	30	5.998000621795654	
U12	No Title Found	30	14.714128494262695	
U_RST	No Title Found	30	7.085251331329346	
Figure 28. Reset circuit block diagram	No Title Found	30	9.960000038146973	
Figure 29. Reset circuit on DA1453x Pro-MB	No Title Found	30	9.960000038146973	
UM-B-169	No Title Found	31	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	31	12.0	
Description	No Title Found	31	12.0	
User Manual	No Title Found	31	9.960000038146973	
Revision 1.0	No Title Found	31	9.960000038146973	
Feb 9, 2024	No Title Found	31	9.960000038146973	
CFR0012	No Title Found	31	7.559999942779541	
31 of 61	No Title Found	31	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	31	7.559999942779541	
Figure 30. Position of the SW1 and J23 header on the DA1453x Pro-MB	No Title Found	31	9.960000038146973	
6.6	No Title Found	31	12.0	
Voltage translator	No Title Found	31	12.0	
Voltage translation is applied to the UART and JTAG signals. The voltage translation is from 3.3 V to	Voltage translator	31	9.960000038146973	12.0
V	Voltage translator	31	9.960000038146973	12.0
DDIO	Voltage translator	31	6.480000019073486	12.0
and vice versa.	Voltage translator	31	9.960000038146973	12.0
The V	Voltage translator	31	9.960000038146973	12.0
DDIO	Voltage translator	31	6.480000019073486	12.0
is generated from U1A, where V3 (V	Voltage translator	31	9.960000038146973	12.0
BAT_HIGH	Voltage translator	31	6.480000019073486	12.0
) is used as a reference. Consequently, there	Voltage translator	31	9.960000038146973	12.0
is no additional power consumption on the power circuitry of DA1453x Pro-MB due to voltage	Voltage translator	31	9.960000038146973	12.0
translation.	Voltage translator	31	9.960000038146973	12.0
Figure 31. Voltage translator circuitry	No Title Found	31	9.960000038146973	
6.7	No Title Found	31	12.0	
SPI data flash	No Title Found	31	12.0	
SPI data flash is enabled by default. The SPI data flash is the AT25DF021A-MAHN-T from Renesas	SPI data flash	31	9.960000038146973	12.0
(2 Mbit) and is located on the DA1453x Pro-MB.	SPI data flash	31	9.960000038146973	12.0
Ext-SPI Slave mode is used to connect the DA1453x to SPI data flash. The SPI data flash can be	SPI data flash	31	9.960000038146973	12.0
isolated or connected on the system if the appropriate jumpers are removed or mounted respectively	SPI data flash	31	9.960000038146973	12.0
from J1 configuration header. The configuration for the SPI data flash is presented in	SPI data flash	31	9.960000038146973	12.0
Table 5	SPI data flash	31	9.960000038146973	12.0
. The	SPI data flash	31	9.960000038146973	12.0
circuit schematic is shown in	SPI data flash	31	9.960000038146973	12.0
Figure 32	SPI data flash	31	9.960000038146973	12.0
, The jumper configuration of the J2 header is highlighted in	SPI data flash	31	9.960000038146973	12.0
Figure 33	SPI data flash	31	9.960000038146973	12.0
and	SPI data flash	31	9.960000038146973	12.0
Figure 34,	SPI data flash	31	9.960000038146973	12.0
and the SPI data flash position on the DA1453x Pro-MB is shown in	SPI data flash	31	9.960000038146973	12.0
Figure 34	No Title Found	31	9.960000038146973	
.	No Title Found	31	9.960000038146973	
UM-B-169	No Title Found	32	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	32	12.0	
Description	No Title Found	32	12.0	
User Manual	No Title Found	32	9.960000038146973	
Revision 1.0	No Title Found	32	9.960000038146973	
Feb 9, 2024	No Title Found	32	9.960000038146973	
CFR0012	No Title Found	32	7.559999942779541	
32 of 61	No Title Found	32	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	32	7.559999942779541	
Table 5. Configuration settings for SPI data flash in DA1453x Pro-MB	No Title Found	32	9.960000038146973	
SPI data flash pin	No Title Found	32	9.0	
Enabled by jumpers	No Title Found	32	9.0	
DA1453x Pro-DB pins	No Title Found	32	9.0	
Comments	No Title Found	32	9.0	
MOSI	No Title Found	32	9.0	
J1 pin 3-4	No Title Found	32	9.0	
P0_0	No Title Found	32	9.0	
FCS	No Title Found	32	9.0	
J1 pin 7-8	No Title Found	32	9.0	
P0_1	No Title Found	32	9.0	
MISO	No Title Found	32	9.0	
J1 pin 9-10	No Title Found	32	9.0	
P0_3	No Title Found	32	9.0	
SCK	No Title Found	32	9.0	
J1 pin 5-6	No Title Found	32	9.0	
P0_4	No Title Found	32	9.0	
V_FL	No Title Found	32	9.0	
J1 pin 1-2	No Title Found	32	9.0	
V3	No Title Found	32	9.0	
Flash voltage. It is supplied	No Title Found	32	9.0	
with the voltage rail of the	No Title Found	32	9.0	
V	No Title Found	32	9.0	
BAT_HIGH	No Title Found	32	6.0	
Note 1	No Title Found	32	9.0	
If the SPI data flash on the DA1453x Pro-DB is mounted and is used as primary flash, the SPI data	No Title Found	32	9.0	
flash from the DA1453x Pro-MB must be disconnected from the system. All the above jumpers must	No Title Found	32	9.0	
be removed.	No Title Found	32	9.0	
Figure 32. SPI data flash schematic	No Title Found	32	9.960000038146973	
Figure 33. Jumper configuration at J1 header on Pro-MB	No Title Found	32	9.960000038146973	
UM-B-169	No Title Found	33	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	33	12.0	
Description	No Title Found	33	12.0	
User Manual	No Title Found	33	9.960000038146973	
Revision 1.0	No Title Found	33	9.960000038146973	
Feb 9, 2024	No Title Found	33	9.960000038146973	
CFR0012	No Title Found	33	7.559999942779541	
33 of 61	No Title Found	33	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	33	7.559999942779541	
Figure 34. SPI data flash and configuration jumpers position	No Title Found	33	9.960000038146973	
6.8	No Title Found	33	12.0	
Configuration headers J1 and J10	No Title Found	33	12.0	
Header J1 and J10 configuration define the communication interface between the DA1453x Pro-MB	No Title Found	33	9.960000038146973	
and DA1453x Pro-DB. You can choose if the SPI data flash is to be included in the system, the type	No Title Found	33	9.960000038146973	
of the UART connection (single wire UART or 2 wire UART or flow control), JTAG and Reset access.	No Title Found	33	9.960000038146973	
This header can also be used for monitoring and for debugging/measuring.	No Title Found	33	9.960000038146973	
The default pin assignment is presented in	No Title Found	33	9.960000038146973	
Figure 35	No Title Found	33	9.960000038146973	
and the default jumper configuration in	No Title Found	33	9.960000038146973	
Figure 36	No Title Found	33	9.960000038146973	
.	No Title Found	33	9.960000038146973	
Figure 35. J1 pin assignment (including J10)	No Title Found	33	9.960000038146973	
UM-B-169	No Title Found	34	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	34	12.0	
Description	No Title Found	34	12.0	
User Manual	No Title Found	34	9.960000038146973	
Revision 1.0	No Title Found	34	9.960000038146973	
Feb 9, 2024	No Title Found	34	9.960000038146973	
CFR0012	No Title Found	34	7.559999942779541	
34 of 61	No Title Found	34	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	34	7.559999942779541	
Figure 36. J1 default jumper configuration	No Title Found	34	9.960000038146973	
The default configuration, (	No Title Found	34	9.960000038146973	
Figure 36	No Title Found	34	9.960000038146973	
) is:	No Title Found	34	9.960000038146973	
●	No Title Found	34	11.039999961853027	
SPI data flash	No Title Found	34	9.960000038146973	
●	No Title Found	34	11.039999961853027	
Single wire UART	No Title Found	34	9.960000038146973	
●	No Title Found	34	11.039999961853027	
JTAG access	No Title Found	34	9.960000038146973	
●	No Title Found	34	11.039999961853027	
Reset access.	No Title Found	34	9.960000038146973	
NOTE	No Title Found	34	9.0	
Сonsider configuring the software accordingly to support the above hardware features.	No Title Found	34	9.0	
If a DA1453x Pro-DB with a Flash is plugged on the Pro-MB, the following jumpers must be removed:	No Title Found	34	9.0	
●	No Title Found	34	9.960000038146973	
J1 1:2	No Title Found	34	9.0	
●	No Title Found	34	9.960000038146973	
J1 3:4	No Title Found	34	9.0	
●	No Title Found	34	9.960000038146973	
J1 5:6	No Title Found	34	9.0	
●	No Title Found	34	9.960000038146973	
J1 7:8	No Title Found	34	9.0	
●	No Title Found	34	9.960000038146973	
J1 9:10	No Title Found	34	9.0	
Pro SPI flash signals on the Pro-MB must not be used and should be kept isolated to avoid conflict	No Title Found	34	9.960000038146973	
with correlated signals on Pro-DB.	No Title Found	34	9.960000038146973	
6.9	No Title Found	34	12.0	
UART configuration	No Title Found	34	12.0	
Any GPIO pin can be set as UART in the DA1453x SoC. In the DA1453x Dek-Kit specific pins are	UART configuration	34	9.960000038146973	12.0
used for UART signals. The dev-kit supports three different modes of UART:	UART configuration	34	9.960000038146973	12.0
●	UART configuration	34	11.039999961853027	12.0
Single-wire UART (default)	UART configuration	34	9.960000038146973	12.0
●	UART configuration	34	11.039999961853027	12.0
Two-wire UART	UART configuration	34	9.960000038146973	12.0
●	UART configuration	34	11.039999961853027	12.0
Flow control UART.	UART configuration	34	9.960000038146973	12.0
The UART signal assignment is shown in	UART configuration	34	9.960000038146973	12.0
Table 6.	No Title Found	34	9.960000038146973	
Table 6. UART signals assignment in DA1453x Dev-Kit	No Title Found	34	9.960000038146973	
Pro-MB signal	No Title Found	34	9.0	
Function	No Title Found	34	9.0	
Pro-DB signal	No Title Found	34	9.0	
Comments	No Title Found	34	9.0	
UTX	No Title Found	34	9.0	
Two-wire UART or Full	No Title Found	34	9.0	
UART Transmit – boot	No Title Found	34	9.0	
UTX	No Title Found	34	9.0	
P0_1	No Title Found	34	9.0	
Used for SPI data Flash	No Title Found	34	9.0	
UM-B-169	No Title Found	35	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	35	12.0	
Description	No Title Found	35	12.0	
User Manual	No Title Found	35	9.960000038146973	
Revision 1.0	No Title Found	35	9.960000038146973	
Feb 9, 2024	No Title Found	35	9.960000038146973	
CFR0012	No Title Found	35	7.559999942779541	
35 of 61	No Title Found	35	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	35	7.559999942779541	
Pro-MB signal	No Title Found	35	9.0	
Function	No Title Found	35	9.0	
Pro-DB signal	No Title Found	35	9.0	
Comments	No Title Found	35	9.0	
URX	No Title Found	35	9.0	
Two-wire UART or Full	No Title Found	35	9.0	
UART Receive – boot	No Title Found	35	9.0	
URX	No Title Found	35	9.0	
P0_0	No Title Found	35	9.0	
Used for SPI data Flash	No Title Found	35	9.0	
UCTS	No Title Found	35	9.0	
Two-wire UART or Full	No Title Found	35	9.0	
UART Clear To Send	No Title Found	35	9.0	
P0_3	No Title Found	35	9.0	
Used for SPI data Flash	No Title Found	35	9.0	
URTS	No Title Found	35	9.0	
Two-wire UART or Full	No Title Found	35	9.0	
UART Request To Send	No Title Found	35	9.0	
P0_4	No Title Found	35	9.0	
Used for SPI data Flash	No Title Found	35	9.0	
RxTx	No Title Found	35	9.0	
Single-Wire UART	No Title Found	35	9.0	
Receive and Transmit –	No Title Found	35	9.0	
boot-TxRx	No Title Found	35	9.0	
P0_5	No Title Found	35	9.0	
SPI data flash and two-wire UART or full UART use same DA1453x GPIOs. This is valid for the data	No Title Found	35	9.960000038146973	
flash memory located either on Pro-MB or on Pro-DB. Consequently, when a two-wires or four-wires	No Title Found	35	9.960000038146973	
UART is used, SPI data flash can’t be used and vice versa.	No Title Found	35	9.960000038146973	
6.9.1	No Title Found	35	11.039999961853027	
Single-wire UART	No Title Found	35	11.039999961853027	
Single-wire UART is the default configuration on the DA1453x DevKit and it is assigned to P0_5 for	No Title Found	35	9.960000038146973	
both RX and TX as shown in	No Title Found	35	9.960000038146973	
Figure 37	No Title Found	35	9.960000038146973	
. The jumper configuration is highlighted in	No Title Found	35	9.960000038146973	
Figure 38	No Title Found	35	9.960000038146973	
.	No Title Found	35	9.960000038146973	
Figure 37. Single-wire UART jumper configuration at J1 header on Pro-MB	No Title Found	35	9.960000038146973	
Figure 38. Single-wire UART configuration jumpers position	No Title Found	35	9.960000038146973	
UM-B-169	No Title Found	36	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	36	12.0	
Description	No Title Found	36	12.0	
User Manual	No Title Found	36	9.960000038146973	
Revision 1.0	No Title Found	36	9.960000038146973	
Feb 9, 2024	No Title Found	36	9.960000038146973	
CFR0012	No Title Found	36	7.559999942779541	
36 of 61	No Title Found	36	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	36	7.559999942779541	
6.9.2	No Title Found	36	11.039999961853027	
Two-wires UART	No Title Found	36	11.039999961853027	
Two-wire UART pins are multiplexed with FCS (chip select) and MOSI of the SPI bus. The jumpers	No Title Found	36	9.960000038146973	
of the SPI flash must be removed. The two-wire UART jumper configuration is highlighted in	No Title Found	36	9.960000038146973	
Figure	No Title Found	36	9.960000038146973	
39	No Title Found	36	9.960000038146973	
and	No Title Found	36	9.960000038146973	
Figure 40	No Title Found	36	9.960000038146973	
.	No Title Found	36	9.960000038146973	
Figure 39. Two-wires UART jumper configuration at J1 header on Pro-MB	No Title Found	36	9.960000038146973	
Figure 40. Two-wires UART configuration jumpers position	No Title Found	36	9.960000038146973	
By placing the jumpers, UTX and URX are driven from signals P0_0 and P0_1 respectively. These	No Title Found	36	9.960000038146973	
signals are not connected to the correspondent pins of DA1453x SoC. You must modify the Pro-DBs	No Title Found	36	9.960000038146973	
by mounting resistors R23 and R24 (both equal to 0Ω).	No Title Found	36	9.960000038146973	
Figure 41. Enable two-wires UART on Pro-DB	No Title Found	36	9.960000038146973	
Alternatively, you can use a jumper wire for connecting the appropriate signals, without modifying	No Title Found	36	9.960000038146973	
Pro-DBs:	No Title Found	36	9.960000038146973	
UM-B-169	No Title Found	37	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	37	12.0	
Description	No Title Found	37	12.0	
User Manual	No Title Found	37	9.960000038146973	
Revision 1.0	No Title Found	37	9.960000038146973	
Feb 9, 2024	No Title Found	37	9.960000038146973	
CFR0012	No Title Found	37	7.559999942779541	
37 of 61	No Title Found	37	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	37	7.559999942779541	
●	No Title Found	37	11.039999961853027	
J1:4 to J1:17 for UTX	No Title Found	37	9.960000038146973	
●	No Title Found	37	11.039999961853027	
J1:8 to J1:15 for URX	No Title Found	37	9.960000038146973	
Figure 42. Enabling two-wires UART with jumper wires	No Title Found	37	9.960000038146973	
6.9.3	No Title Found	37	11.039999961853027	
Four-wires UART/UART with flow control	No Title Found	37	11.039999961853027	
Four-wires UART pins UTX, URX, URTS, and UCTS are multiplexed with MOSI, FCS, SCK, and	No Title Found	37	9.960000038146973	
MISO of the SPI bus, respectively. The jumpers of the SPI flash must be removed. The Full-wire	No Title Found	37	9.960000038146973	
UART jumper configuration is highlighted in	No Title Found	37	9.960000038146973	
Figure 43	No Title Found	37	9.960000038146973	
and	No Title Found	37	9.960000038146973	
Figure 44	No Title Found	37	9.960000038146973	
.	No Title Found	37	9.960000038146973	
Figure 43. Full-wire UART jumper configuration at J1 header	No Title Found	37	9.960000038146973	
UM-B-169	No Title Found	38	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	38	12.0	
Description	No Title Found	38	12.0	
User Manual	No Title Found	38	9.960000038146973	
Revision 1.0	No Title Found	38	9.960000038146973	
Feb 9, 2024	No Title Found	38	9.960000038146973	
CFR0012	No Title Found	38	7.559999942779541	
38 of 61	No Title Found	38	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	38	7.559999942779541	
Figure 44. Four UART configuration jumpers position	No Title Found	38	9.960000038146973	
By placing the jumpers, UART signals are driven from signals P0_0 to P0_4. These signals are not	No Title Found	38	9.960000038146973	
connected to the correspondent pins of DA1453x SoC. You must modify the Pro-DBs by mounting	No Title Found	38	9.960000038146973	
resistors R23 to R26 (both equal to 0Ω) and to remove resistors R19 to R22.	No Title Found	38	9.960000038146973	
Mount with 0Ω	No Title Found	38	10.777225494384766	
Remove Resistors	No Title Found	38	10.777225494384766	
Figure 45. Enable two-wires UART on Pro-DB	No Title Found	38	9.960000038146973	
Alternatively, you can use a jumper wire for connecting the appropriate signals, without modifying	No Title Found	38	9.960000038146973	
Pro-DBs:	No Title Found	38	9.960000038146973	
●	No Title Found	38	11.039999961853027	
J1:4 to J1:17 for UTX	No Title Found	38	9.960000038146973	
●	No Title Found	38	11.039999961853027	
J1:8 to J1:15 for URX	No Title Found	38	9.960000038146973	
●	No Title Found	38	11.039999961853027	
J1:10 to J1:11 for UCTS	No Title Found	38	9.960000038146973	
●	No Title Found	38	11.039999961853027	
J1:6 to J1:13 for URTS.	No Title Found	38	9.960000038146973	
UM-B-169	No Title Found	39	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	39	12.0	
Description	No Title Found	39	12.0	
User Manual	No Title Found	39	9.960000038146973	
Revision 1.0	No Title Found	39	9.960000038146973	
Feb 9, 2024	No Title Found	39	9.960000038146973	
CFR0012	No Title Found	39	7.559999942779541	
39 of 61	No Title Found	39	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	39	7.559999942779541	
Figure 46. Enabling two-wires UART with jumper wires	No Title Found	39	9.960000038146973	
6.10 JTAG configuration	No Title Found	39	12.0	
JTAG uses the Serial Wire Debug (SWD) protocol and consists of the SWDIO and SWCLK lines.	6.10 JTAG configuration	39	9.960000038146973	12.0
The JTAG signal assignment is shown in	6.10 JTAG configuration	39	9.960000038146973	12.0
Table 7	6.10 JTAG configuration	39	9.960000038146973	12.0
.	6.10 JTAG configuration	39	9.960000038146973	12.0
Table 7. UART signals assignment in DA1453x Dev-Kit	6.10 JTAG configuration	39	9.960000038146973	12.0
Pro-MB signal	6.10 JTAG configuration	39	9.0	12.0
Function	6.10 JTAG configuration	39	9.0	12.0
Pro-DB signal	6.10 JTAG configuration	39	9.0	12.0
Comments	6.10 JTAG configuration	39	9.0	12.0
SWCLK	6.10 JTAG configuration	39	9.0	12.0
Serial Wire Clock	6.10 JTAG configuration	39	9.0	12.0
P0_2	6.10 JTAG configuration	39	9.0	12.0
SWDIO	No Title Found	39	9.0	
Serial Wire Data Input Output	No Title Found	39	9.0	
P_10	No Title Found	39	9.0	
On the WLCSP	No Title Found	39	9.0	
package the SWDIO	No Title Found	39	9.0	
assigned to P0_5.	No Title Found	39	9.0	
Note 1	No Title Found	39	9.0	
JTAG and UART cannot be used in parallel for the WLCSP versions of the DA1453x SoC without	No Title Found	39	9.0	
software modification.	No Title Found	39	9.0	
The JTAG jumper configuration is highlighted in	No Title Found	39	9.960000038146973	
Figure 47	No Title Found	39	9.960000038146973	
and	No Title Found	39	9.960000038146973	
Figure 48	No Title Found	39	9.960000038146973	
.	No Title Found	39	9.960000038146973	
Figure 47. JTAG jumper configuration at J1 header	No Title Found	39	9.960000038146973	
UM-B-169	No Title Found	40	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	40	12.0	
Description	No Title Found	40	12.0	
User Manual	No Title Found	40	9.960000038146973	
Revision 1.0	No Title Found	40	9.960000038146973	
Feb 9, 2024	No Title Found	40	9.960000038146973	
CFR0012	No Title Found	40	7.559999942779541	
40 of 61	No Title Found	40	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	40	7.559999942779541	
Figure 48. JTAG configuration jumpers position	No Title Found	40	9.960000038146973	
6.11 Breakout header J2	No Title Found	40	12.0	
All P0_X GPIOs of the DA1453x Pro-DBs are diverted also to the DA1453x Pro-MB for monitoring on	No Title Found	40	9.960000038146973	
the breakout header J2 of the Pro-MB. The schematic of the J2 is shown in	No Title Found	40	9.960000038146973	
Figure 49	No Title Found	40	9.960000038146973	
and the	No Title Found	40	9.960000038146973	
position of the J2 header on DA1453x in	No Title Found	40	9.960000038146973	
Figure 50	No Title Found	40	9.960000038146973	
.	No Title Found	40	9.960000038146973	
Figure 49. J2 schematic	No Title Found	40	9.960000038146973	
Figure 50. J2 position on DA1453x Pro-MB	No Title Found	40	9.960000038146973	
UM-B-169	No Title Found	41	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	41	12.0	
Description	No Title Found	41	12.0	
User Manual	No Title Found	41	9.960000038146973	
Revision 1.0	No Title Found	41	9.960000038146973	
Feb 9, 2024	No Title Found	41	9.960000038146973	
CFR0012	No Title Found	41	7.559999942779541	
41 of 61	No Title Found	41	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	41	7.559999942779541	
6.12 Push buttons	No Title Found	41	12.0	
There are two available push buttons on the DA1453x Pro-MB, the SW2 and SW3. The SW2 is	6.12 Push buttons	41	9.960000038146973	12.0
assigned to P0_11 and the SW3 to P0_10. The WLCSP versions of the DA1453x SoC do not	6.12 Push buttons	41	9.960000038146973	12.0
support any push button whereas  DA14533 supports only the SW3.	6.12 Push buttons	41	9.960000038146973	12.0
By default, the jumpers on J19 are mounted. The circuit schematic is shown in	6.12 Push buttons	41	9.960000038146973	12.0
Figure 51	6.12 Push buttons	41	9.960000038146973	12.0
the position	6.12 Push buttons	41	9.960000038146973	12.0
of the push buttons in	6.12 Push buttons	41	9.960000038146973	12.0
Figure 52	6.12 Push buttons	41	9.960000038146973	12.0
, and the position of the J19 header in	6.12 Push buttons	41	9.960000038146973	12.0
Figure 53	6.12 Push buttons	41	9.960000038146973	12.0
.	6.12 Push buttons	41	9.960000038146973	12.0
Figure 51. Push buttons SW2 and SW3	No Title Found	41	9.960000038146973	
Figure 52. Push buttons position on the DA1453x Pro-MB	No Title Found	41	9.960000038146973	
Figure 53. J19 header position on the DA1453x Pro-MB	No Title Found	41	9.960000038146973	
UM-B-169	No Title Found	42	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	42	12.0	
Description	No Title Found	42	12.0	
User Manual	No Title Found	42	9.960000038146973	
Revision 1.0	No Title Found	42	9.960000038146973	
Feb 9, 2024	No Title Found	42	9.960000038146973	
CFR0012	No Title Found	42	7.559999942779541	
42 of 61	No Title Found	42	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	42	7.559999942779541	
6.13 User LED	No Title Found	42	12.0	
The user LED (D5) is supported only from DA14531 and DA14535 FCQFN packages. The user LED	6.13 User LED	42	9.960000038146973	12.0
is assigned to P0_9. The circuit schematic is shown in	6.13 User LED	42	9.960000038146973	12.0
Figure 54	6.13 User LED	42	9.960000038146973	12.0
and the position of the user LED on	6.13 User LED	42	9.960000038146973	12.0
the DA1453x Pro-MB in	6.13 User LED	42	9.960000038146973	12.0
Figure 55	6.13 User LED	42	9.960000038146973	12.0
. The jumper on the J8 header (pin 3-4) is mounted by default.	6.13 User LED	42	9.960000038146973	12.0
Figure 54. User LED (D5)	No Title Found	42	9.960000038146973	
Figure 55. User LED position	No Title Found	42	9.960000038146973	
6.14 MikroBus interface	No Title Found	42	12.0	
DA1453x Pro-MB can support one MikroBus module. The MikroBus slot is compatible with MikroBus	6.14 MikroBus interface	42	9.960000038146973	12.0
standard click boards for additional expandability requirements. Two female headers (8 position	6.14 MikroBus interface	42	9.960000038146973	12.0
0.100", through hole, socket type) are mounted (by default) on J17 and J18. The schematic circuit is	6.14 MikroBus interface	42	9.960000038146973	12.0
shown in	6.14 MikroBus interface	42	9.960000038146973	12.0
Figure 56	6.14 MikroBus interface	42	9.960000038146973	12.0
and the position of the MikoBus slot in	6.14 MikroBus interface	42	9.960000038146973	12.0
Figure 57	6.14 MikroBus interface	42	9.960000038146973	12.0
.	6.14 MikroBus interface	42	9.960000038146973	12.0
Figure 56. MikroBus slot	No Title Found	42	9.960000038146973	
UM-B-169	No Title Found	43	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	43	12.0	
Description	No Title Found	43	12.0	
User Manual	No Title Found	43	9.960000038146973	
Revision 1.0	No Title Found	43	9.960000038146973	
Feb 9, 2024	No Title Found	43	9.960000038146973	
CFR0012	No Title Found	43	7.559999942779541	
43 of 61	No Title Found	43	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	43	7.559999942779541	
Figure 57. MikroBus position on the DA1453x Pro-MB	No Title Found	43	9.960000038146973	
6.15 PMOD interface	No Title Found	43	12.0	
DA1453x Pro-MB can support one PMOD module. The female header (J20) is mounted (by default).	6.15 PMOD interface	43	9.960000038146973	12.0
The schematic circuit is shown in	6.15 PMOD interface	43	9.960000038146973	12.0
Figure 58	6.15 PMOD interface	43	9.960000038146973	12.0
and the position of the MikoBus slot in	6.15 PMOD interface	43	9.960000038146973	12.0
Figure 59	6.15 PMOD interface	43	9.960000038146973	12.0
.	6.15 PMOD interface	43	9.960000038146973	12.0
Figure 58. PMOD slot	No Title Found	43	9.960000038146973	
Figure 59. PMOD position on the DA1453x Pro-MB	No Title Found	43	9.960000038146973	
GPIOs and Pin Assignment and Functions of DA1453x DK Pro.	No Title Found	43	9.960000038146973	
All P0_X GPIOs of the DA1453x Pro-DBs are diverted also to the DA1453x Pro-MB for monitoring.	No Title Found	43	9.960000038146973	
UM-B-169	No Title Found	44	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	44	12.0	
Description	No Title Found	44	12.0	
User Manual	No Title Found	44	9.960000038146973	
Revision 1.0	No Title Found	44	9.960000038146973	
Feb 9, 2024	No Title Found	44	9.960000038146973	
CFR0012	No Title Found	44	7.559999942779541	
44 of 61	No Title Found	44	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	44	7.559999942779541	
On the breakout headerJ2 of the Pro-MB, you can monitor the P0_X GPIOs mainly used for the	No Title Found	44	9.960000038146973	
MikroBUS, PMOD interface.	No Title Found	44	9.960000038146973	
6.16 Measurements and software triggers	No Title Found	44	12.0	
On DA1453x Development kit, you can monitor the critical current and voltages of the system. This is	6.16 Measurements and software triggers	44	9.960000038146973	12.0
implemented with a power measurement circuitry which consists of the power measurement module	6.16 Measurements and software triggers	44	9.960000038146973	12.0
(PMM2) and the monitoring circuit, which resides on the DA1453x Pro-Motherboard. With the aid of	6.16 Measurements and software triggers	44	9.960000038146973	12.0
the Power profiler of Renesas Smart Snippets Toolbox, a good visualization of the system current	6.16 Measurements and software triggers	44	9.960000038146973	12.0
drawn and various voltages is provided. Measurements are quite accurate, but for precise	6.16 Measurements and software triggers	44	9.960000038146973	12.0
measurements, use an external calibrated instrument.	6.16 Measurements and software triggers	44	9.960000038146973	12.0
PMM2 features are:	6.16 Measurements and software triggers	44	9.960000038146973	12.0
●	No Title Found	44	11.039999961853027	
DA1453x current measurement (1 uA-100 mA at 128 kHz)	No Title Found	44	9.960000038146973	
●	No Title Found	44	11.039999961853027	
Measurement of two additional system currents (voltage rails V3x and V5x for Mikrobus/PMOD)	No Title Found	44	9.960000038146973	
●	No Title Found	44	11.039999961853027	
2x DA1453x system voltage measurement	No Title Found	44	9.960000038146973	
●	No Title Found	44	11.039999961853027	
8 x DA1453x system voltage monitoring	No Title Found	44	9.960000038146973	
●	No Title Found	44	11.039999961853027	
Monitoring and measuring up to 8 Digital signals with capability to be configured as software	No Title Found	44	9.960000038146973	
triggers.	No Title Found	44	9.960000038146973	
The block diagram of PMM2 is shown in	No Title Found	44	9.960000038146973	
Figure 60	No Title Found	44	9.960000038146973	
. The analog frontend and ADC converter are	No Title Found	44	9.960000038146973	
implemented on the PMM2 module, whereas the SPI to USB bridge (FT2232H, U12) and digital	No Title Found	44	9.960000038146973	
signals reside on the motherboard.	No Title Found	44	9.960000038146973	
V1	No Title Found	44	7.0201616287231445	
V2	No Title Found	44	7.00764799118042	
V3	No Title Found	44	7.0201616287231445	
V4	No Title Found	44	7.00764799118042	
V5	No Title Found	44	7.0201616287231445	
V6	No Title Found	44	7.0201616287231445	
V7	No Title Found	44	7.00764799118042	
J7, M.2 socket	No Title Found	44	8.008740425109863	
DA1453x	No Title Found	44	8.008740425109863	
Pro-DB	No Title Found	44	8.008740425109863	
VLDO	No Title Found	44	8.02125358581543	
Vs-	No Title Found	44	7.0201616287231445	
Vs+	No Title Found	44	7.00764799118042	
DA1453x  PRO-MB	No Title Found	44	9.022346496582031	
V5X	No Title Found	44	8.02125358581543	
V3X	No Title Found	44	8.02125358581543	
V5X Current	No Title Found	44	7.00764799118042	
V3X Current	No Title Found	44	7.00764799118042	
V8	No Title Found	44	7.00764799118042	
Figure 60. Power measurement module (PMM2) block diagram	No Title Found	44	9.960000038146973	
An EEPROM is also provided on the module to store production data and allow autodetection from	No Title Found	44	9.960000038146973	
the host software.	No Title Found	44	9.960000038146973	
The power measurement module (PMM2) is connected to Pro-Motherboard through J7, M.2 socket.	No Title Found	44	9.960000038146973	
PMM2 is supplied from 3.3 V and VCM through power switch U24.	No Title Found	44	9.960000038146973	
The DA1453x does not employ shunt voltage drop compensation. The voltage drop on the PMM2 is	No Title Found	44	9.960000038146973	
V	No Title Found	44	9.960000038146973	
PMM2_dropout	No Title Found	44	6.480000019073486	
=2.4*I	No Title Found	44	9.960000038146973	
VBAT	No Title Found	44	6.480000019073486	
. Use cases for the DA1453x rarely draw more than 40-50 mA of current in	No Title Found	44	9.960000038146973	
which case the max voltage drop of about 100 mV due to the PMM2 should not cause problems.	No Title Found	44	9.960000038146973	
UM-B-169	No Title Found	45	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	45	12.0	
Description	No Title Found	45	12.0	
User Manual	No Title Found	45	9.960000038146973	
Revision 1.0	No Title Found	45	9.960000038146973	
Feb 9, 2024	No Title Found	45	9.960000038146973	
CFR0012	No Title Found	45	7.559999942779541	
45 of 61	No Title Found	45	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	45	7.559999942779541	
V1	No Title Found	45	7.496982574462891	
V2	No Title Found	45	7.478287220001221	
C1p	No Title Found	45	7.478287220001221	
C1n	No Title Found	45	7.496982574462891	
C2p	No Title Found	45	7.478287220001221	
C2n	No Title Found	45	7.478287220001221	
V3	No Title Found	45	7.478287220001221	
V4	No Title Found	45	7.478287220001221	
V6	No Title Found	45	7.496982574462891	
V7	No Title Found	45	7.496982574462891	
V5	No Title Found	45	7.478287220001221	
V8	No Title Found	45	7.478287220001221	
Figure 61. Current measurement socket (M2)	No Title Found	45	9.960000038146973	
Table 8. Monitored power sources	No Title Found	45	9.960000038146973	
PMM2 pins	No Title Found	45	9.0	
(Note 1)	No Title Found	45	8.039999961853027	
DA1453x signals	No Title Found	45	9.0	
Comments	No Title Found	45	9.0	
V1	No Title Found	45	9.0	
Reset	No Title Found	45	9.0	
Reset line, applied on pin J7.20	No Title Found	45	9.0	
V2	No Title Found	45	9.0	
V5X	No Title Found	45	9.0	
5 V rail powering MikroBUS. It is connected to USB1 through a 0.1Ω	No Title Found	45	9.0	
resistor and filter L1.	No Title Found	45	9.0	
V3	No Title Found	45	9.0	
V1	No Title Found	45	9.0	
This is voltage rail is connected to the VBAT_Low of the DA1453x SoC.	No Title Found	45	9.0	
When, VLOW.	No Title Found	45	9.0	
It is applied on pin J7.44.	No Title Found	45	9.0	
Sourced from Pro-Daughterboard when DA13453x is configured in	No Title Found	45	9.0	
BUCK or BYPASS mode.	No Title Found	45	9.0	
Sourced from Pro-Motherboard when DA13453x is configured in	No Title Found	45	9.0	
BOOST mode.	No Title Found	45	9.0	
V4	No Title Found	45	9.0	
VF	No Title Found	45	9.0	
It is a sample of the voltage that is supplied to DA1453x SoC. It is	No Title Found	45	9.0	
applied on pin J7.46.	No Title Found	45	9.0	
Sourced from Pro-Motherboard.	No Title Found	45	9.0	
V5	No Title Found	45	9.0	
VFL	No Title Found	45	9.0	
The supply voltage of Flash that is located on the Pro-Motherboard. It is	No Title Found	45	9.0	
applied on pin J7.48.	No Title Found	45	9.0	
V6	No Title Found	45	9.0	
TRIG_0	No Title Found	45	9.0	
Pin P0_11 or another signal connected on J24.1. Applied on pin J7.3.	No Title Found	45	9.0	
V7	No Title Found	45	9.0	
TRIG_3	No Title Found	45	9.0	
Pin P0_5 or another signal connected on J24.7. Applied on pin J7.5.	No Title Found	45	9.0	
V8	No Title Found	45	9.0	
AIN	No Title Found	45	9.0	
Any signal connected on J2.2 with a jump wire. Applied on J7.59.	No Title Found	45	9.0	
UM-B-169	No Title Found	46	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	46	12.0	
Description	No Title Found	46	12.0	
User Manual	No Title Found	46	9.960000038146973	
Revision 1.0	No Title Found	46	9.960000038146973	
Feb 9, 2024	No Title Found	46	9.960000038146973	
CFR0012	No Title Found	46	7.559999942779541	
46 of 61	No Title Found	46	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	46	7.559999942779541	
PMM2 pins	No Title Found	46	9.0	
(Note 1)	No Title Found	46	8.039999961853027	
DA1453x signals	No Title Found	46	9.0	
Comments	No Title Found	46	9.0	
VLDOp,	No Title Found	46	9.0	
VLDOn	No Title Found	46	9.0	
Vs+, Vs-	No Title Found	46	9.0	
DA1453x VBAT_x current measurement.	No Title Found	46	9.0	
Sourced from Pro-Motherboard.	No Title Found	46	9.0	
Current enters PMM2 from Vs+ (pin J7.10) and exits from Vs- (pin J7.8).	No Title Found	46	9.0	
C1p, C1n	No Title Found	46	9.0	
V3P, V3X	No Title Found	46	9.0	
3.3 V voltage rail. V3X current measurement.	No Title Found	46	9.0	
The current drawn from PMOD.	No Title Found	46	9.0	
C2p, C2n	No Title Found	46	9.0	
V5P, V5X	No Title Found	46	9.0	
5 V voltage rail. V5X current measurement.	No Title Found	46	9.0	
The current drawn from MikroBUS or PMOD.	No Title Found	46	9.0	
Note 1	No Title Found	46	9.0	
See PMM2 daughterboard pinout.	No Title Found	46	9.0	
There are eight TRIG options defined (TRIG_0 to TRIG_7).	No Title Found	46	9.960000038146973	
As shown in	No Title Found	46	9.960000038146973	
Figure 62,	No Title Found	46	9.960000038146973	
suitable jumper block (J7) allows you to directly select any of the available	No Title Found	46	9.960000038146973	
signals. Any other GPIO can be used as a trigger source by connecting a TRIG pin on J7 with a	No Title Found	46	9.960000038146973	
jumper wire to the desired position at breakout header (J2).	No Title Found	46	9.960000038146973	
Figure 62. Selection jumper block (J24) and buffer MOSFETS for I/O levels compatibility	No Title Found	46	9.960000038146973	
The dual Mosfets Q1 to Q4 buffer the signals to provide compatibility with 1.2 to 5 V I/O levels.	No Title Found	46	9.960000038146973	
Renesas Smart Snippets Toolbox is required for capturing the waveforms of DA1453x system. By	No Title Found	46	9.960000038146973	
using Power profiler, you can monitor simultaneously, on separated windows, the DA1453x VBAT	No Title Found	46	9.960000038146973	
current, two analog waveforms (voltages or currents) and up to eight digital signals,	No Title Found	46	9.960000038146973	
Figure 63	No Title Found	46	9.960000038146973	
.	No Title Found	46	9.960000038146973	
UM-B-169	No Title Found	47	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	47	12.0	
Description	No Title Found	47	12.0	
User Manual	No Title Found	47	9.960000038146973	
Revision 1.0	No Title Found	47	9.960000038146973	
Feb 9, 2024	No Title Found	47	9.960000038146973	
CFR0012	No Title Found	47	7.559999942779541	
47 of 61	No Title Found	47	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	47	7.559999942779541	
Figure 63. DA1453x waveforms, captured from Power profiler of SmartSnippet Toolbox	No Title Found	47	9.960000038146973	
This functionality, coupled with the digital trigger signals allows profiling the power footprint of	No Title Found	47	9.960000038146973	
software operations and provides you a better insight of how the system works. Digital signals can be	No Title Found	47	9.960000038146973	
used either as monitoring signals or as triggers to start/stop capture of data so the user can isolate	No Title Found	47	9.960000038146973	
specific events. The trigger functionality is implemented in SDK.	No Title Found	47	9.960000038146973	
UM-B-169	No Title Found	48	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	48	12.0	
Description	No Title Found	48	12.0	
User Manual	No Title Found	48	9.960000038146973	
Revision 1.0	No Title Found	48	9.960000038146973	
Feb 9, 2024	No Title Found	48	9.960000038146973	
CFR0012	No Title Found	48	7.559999942779541	
48 of 61	No Title Found	48	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	48	7.559999942779541	
7	No Title Found	48	14.039999961853027	
The Power Measurement Module 2 (PMM2), (500-29-x)	No Title Found	48	14.039999961853027	
The power measurement module (PMM2) is an external add-on board that is interfaced (connected)	No Title Found	48	9.960000038146973	
on the Pro-Motherboard via connector J7.	No Title Found	48	9.960000038146973	
The current measurement unit has the following features:	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Full scale range 640 mA at 3 V (for currents > 50-100 mA dropout compensation is	No Title Found	48	9.960000038146973	
recommended – not implemented in the DA1453x Pro-Devkit)	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Measure accurately down to 1 μA	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Dedicated hibernation mode to measure down to 100 nA	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Current sense resistors	No Title Found	48	9.960000038146973	
○	No Title Found	48	11.039999961853027	
2.4 Ω in series to VLDO (located on PMM2)	No Title Found	48	9.960000038146973	
○	No Title Found	48	11.039999961853027	
0.1 Ω in series for measuring current on C1p/C1n (located externally to PMM2)	No Title Found	48	9.960000038146973	
○	No Title Found	48	11.039999961853027	
0.1 Ω in series for measuring current on C2p/C2n (located externally to PMM2)	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Analog processing blocks	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Fast quad channel 24-bit ADC with SPI interface	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
FTDI chip for transferring data to the PC	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
Software trigger inputs	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
System voltage measurement	No Title Found	48	9.960000038146973	
●	No Title Found	48	11.039999961853027	
External analog input 5 V.	No Title Found	48	9.960000038146973	
Figure 64. PMM2 current measurement circuit PMM2	No Title Found	48	9.960000038146973	
The input to the circuit is the voltage across the sense resistors R74 and R75. The voltage across the	No Title Found	48	9.960000038146973	
sense resistors is sampled simultaneously by two differential amplifier stages and is converted by the	No Title Found	48	9.960000038146973	
ADC to a digital value. The low range has a conversion gain of 5053 V/A and covers from 1 μA up to	No Title Found	48	9.960000038146973	
about 790 μA. The high scale has a conversion gain of 6.114 V/A and covers up to about 600 mA	No Title Found	48	9.960000038146973	
UM-B-169	No Title Found	49	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	49	12.0	
Description	No Title Found	49	12.0	
User Manual	No Title Found	49	9.960000038146973	
Revision 1.0	No Title Found	49	9.960000038146973	
Feb 9, 2024	No Title Found	49	9.960000038146973	
CFR0012	No Title Found	49	7.559999942779541	
49 of 61	No Title Found	49	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	49	7.559999942779541	
depending on the VBAT voltage. Both channels are sampled simultaneously, and the host software	No Title Found	49	9.960000038146973	
selects the correct channel using a threshold of 750 μA. R9 provides a constant offset which helps	No Title Found	49	9.960000038146973	
avoid the nonlinear region of the low scale. A blue LED serves as a visual indicator of the range. It	No Title Found	49	9.960000038146973	
switches on close to 750 μA and allows you to have a quick indication of the state of the system (on	No Title Found	49	9.960000038146973	
when active, off when sleeping).	No Title Found	49	9.960000038146973	
Multiplexers U7 and U8 select among the available system voltages and feed channels 3 and 4 of the	No Title Found	49	9.960000038146973	
ADC. A divider formed by R59 and R60 can be selected on CH4 to allow for 5 V input signal range (the	No Title Found	49	9.960000038146973	
full-scale voltage of the ADC is 4 V). Two analog front ends around U11 and U12 are provided for	No Title Found	49	9.960000038146973	
measuring the C1 (VBATp/n) and C2 (VBUSp/n) currents. The switch in front of U11 prevents the	No Title Found	49	9.960000038146973	
leakage current of the differential amplifier stage from C1 (VBATp/n) to be measured as system current.	No Title Found	49	9.960000038146973	
A shift register and associated logic control the multiplexers and the rest of the functions of the module.	No Title Found	49	9.960000038146973	
An EEPROM memory is used to store production data and allows the host software to autodetect the	No Title Found	49	9.960000038146973	
module. Charge pump U6 generates a slightly negative voltage (-230 mV) to allow the output of the	No Title Found	49	9.960000038146973	
frontend OPAMPs to reach true zero.	No Title Found	49	9.960000038146973	
V2	No Title Found	49	10.539077758789062	
V3	No Title Found	49	10.550055503845215	
V4	No Title Found	49	10.550055503845215	
V5	No Title Found	49	10.550055503845215	
V6	No Title Found	49	10.550055503845215	
V7	No Title Found	49	10.539077758789062	
C1p	No Title Found	49	10.550055503845215	
C1n	No Title Found	49	10.550055503845215	
V1	No Title Found	49	10.550055503845215	
C2p	No Title Found	49	10.550055503845215	
C2n	No Title Found	49	10.539077758789062	
V9	No Title Found	49	10.550055503845215	
V8	No Title Found	49	10.550055503845215	
Figure 65. PMM2 on board peripherals (power supply, memory and so on)	No Title Found	49	9.960000038146973	
The circuit can be set in a low current measurement mode from the host (hibernation mode). This is	No Title Found	49	9.960000038146973	
useful to measure the current of the SoC in hibernation (shipping) mode, which is in the order of some	No Title Found	49	9.960000038146973	
hundreds of nA. The measurement range of the circuit in this mode is from 100 nA to 60 μA. This is	No Title Found	49	9.960000038146973	
achieved with a significantly larger sense resistor (R76) which is shorted by Q2 in normal operation. In	No Title Found	49	9.960000038146973	
hibernation mode, Q2 switches off and R76 is placed in series with R74 and R75 forming a 24.4 Ω	No Title Found	49	9.960000038146973	
sense resistor. The lower sampling point of the low range is moved to the terminals of this series	No Title Found	49	9.960000038146973	
combination with the help of analog switch U18. The high range connections remain unchanged, and	No Title Found	49	9.960000038146973	
it monitors the current through R74 and R75 only. To avoid excessive voltage drop due to the large	No Title Found	49	9.960000038146973	
sense resistor, in case the system wakes up and draws large currents, the LED indicator output also	No Title Found	49	9.960000038146973	
overrides the control of Q2 when the measured current exceeds ~600-700 μA. This ensures that the	No Title Found	49	9.960000038146973	
system can wake up normally and its operation is not affected by the hibernation mode. The hibernation	No Title Found	49	9.960000038146973	
mode is intended for measuring low level and mostly stable currents.	No Title Found	49	9.960000038146973	
The offset of the circuit can be calibrated in the Smart Snippets Toolbox software. The procedure	No Title Found	49	9.960000038146973	
necessitates disconnecting the daughterboard either physically or by sliding the daughterboard power	No Title Found	49	9.960000038146973	
selection switch to the right.	No Title Found	49	9.960000038146973	
UM-B-169	No Title Found	50	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	50	12.0	
Description	No Title Found	50	12.0	
User Manual	No Title Found	50	9.960000038146973	
Revision 1.0	No Title Found	50	9.960000038146973	
Feb 9, 2024	No Title Found	50	9.960000038146973	
CFR0012	No Title Found	50	7.559999942779541	
50 of 61	No Title Found	50	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	50	7.559999942779541	
Sampling rate is by default 128 kS/s but can be further reduced to 84 kS/s when using slower machines	No Title Found	50	9.960000038146973	
or lower speed USB ports. All analog and digital signals are sampled simultaneously.	No Title Found	50	9.960000038146973	
U1	No Title Found	50	10.193239212036133	
AFE	No Title Found	50	10.175387382507324	
OPAMP	No Title Found	50	8.568747520446777	
High Prec	No Title Found	50	8.568747520446777	
OPAMP	No Title Found	50	8.58659839630127	
MUX	No Title Found	50	8.568747520446777	
OPAMP	No Title Found	50	6.9621076583862305	
EEPROM	No Title Found	50	6.9621076583862305	
16.384	No Title Found	50	6.9621076583862305	
MHz	No Title Found	50	6.979958534240723	
RSense	No Title Found	50	8.58659839630127	
Negative	No Title Found	50	8.568747520446777	
voltage	No Title Found	50	8.568747520446777	
generator	No Title Found	50	8.58659839630127	
MUX	No Title Found	50	8.568747520446777	
OPAMP	No Title Found	50	6.9621076583862305	
Shift	No Title Found	50	8.58659839630127	
Register	No Title Found	50	8.568747520446777	
LED	No Title Found	50	8.58659839630127	
Figure 66. Current measurement unit PCBA (TOP)	No Title Found	50	9.960000038146973	
7.1	No Title Found	50	12.0	
Accuracy of current measurement for VBAT system (VLDO)	No Title Found	50	12.0	
The total measuring range of the current measurement circuit of the power measurement module two	No Title Found	50	9.960000038146973	
is 100 nA to 500 mA for VLDO= 3 V, implemented into two scales. The current measurement range is	No Title Found	50	9.960000038146973	
covered by two operating modes, the default (1 μA to 500 mA) and the hibernation mode (100 nA to	No Title Found	50	9.960000038146973	
60 μΑ). Switching from default to hibernation mode is done manually over Smart Snippets Toolbox (a	No Title Found	50	9.960000038146973	
version supporting this mode must be used).	No Title Found	50	9.960000038146973	
The circuit accuracy is measured by applying a constant current, monitoring the same current with an	No Title Found	50	9.960000038146973	
external instrument and the ADC of the PMM2 module, then comparing the two. In general, the	No Title Found	50	9.960000038146973	
inaccuracy presented in the current measurement circuit is less than 5% (practically less than 2%) in	No Title Found	50	9.960000038146973	
most of the current range, (	No Title Found	50	9.960000038146973	
Table 9	No Title Found	50	9.960000038146973	
). The values presented in	No Title Found	50	9.960000038146973	
Figure 67	No Title Found	50	9.960000038146973	
are averages of multiple points.	No Title Found	50	9.960000038146973	
UM-B-169	No Title Found	51	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	51	12.0	
Description	No Title Found	51	12.0	
User Manual	No Title Found	51	9.960000038146973	
Revision 1.0	No Title Found	51	9.960000038146973	
Feb 9, 2024	No Title Found	51	9.960000038146973	
CFR0012	No Title Found	51	7.559999942779541	
51 of 61	No Title Found	51	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	51	7.559999942779541	
Table 9. Accuracy of the current measurement circuit	No Title Found	51	9.960000038146973	
Channel	No Title Found	51	9.0	
Range	No Title Found	51	9.0	
Error (%)	No Title Found	51	9.0	
Comment	No Title Found	51	9.0	
CH1/CH2:	No Title Found	51	9.0	
VBAT current hibern.	No Title Found	51	9.0	
100 nA-60 μA	No Title Found	51	9.0	
±15% at 100 nA,	No Title Found	51	9.0	
<±2% at 1μA-60 μA	No Title Found	51	9.0	
Mode automatically overridden in	No Title Found	51	9.0	
hardware if current > ~750 μA	No Title Found	51	9.0	
CH1/CH2:	No Title Found	51	9.0	
VBAT current active	No Title Found	51	9.0	
1 μA-640 mA	No Title Found	51	9.0	
±10% at 1 μA,	No Title Found	51	9.0	
<±2% at 100 μA-640 mA	No Title Found	51	9.0	
2 ranges [1μA-750 μA/750 μA-	No Title Found	51	9.0	
640 mA]	No Title Found	51	9.0	
CH3, CH4 current:	No Title Found	51	9.0	
C1p/n, C2p/n	No Title Found	51	9.0	
1 mA-1 A	No Title Found	51	9.0	
±5%	No Title Found	51	9.0	
Range and accuracy depend on	No Title Found	51	9.0	
external R	No Title Found	51	9.0	
sense	No Title Found	51	6.0	
. Values shown with	No Title Found	51	9.0	
0.1 Ω/1% shunt	No Title Found	51	9.0	
CH3 voltage	No Title Found	51	9.0	
0-4 V	No Title Found	51	9.0	
±2%	No Title Found	51	9.0	
CH4 voltages	No Title Found	51	9.0	
0-4 V/0-5 V	No Title Found	51	9.0	
±2%	No Title Found	51	9.0	
0-5 V range available with 0.776x	No Title Found	51	9.0	
divider enabled	No Title Found	51	9.0	
Figure 67. Normal mode (1 μA to 640 mA at 3.3 V) data after offset calibration	No Title Found	51	9.960000038146973	
Figure 68. Hibernation mode (100 nA to 60 μA at 3.3 V) data after offset calibration	No Title Found	51	9.960000038146973	
UM-B-169	No Title Found	52	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	52	12.0	
Description	No Title Found	52	12.0	
User Manual	No Title Found	52	9.960000038146973	
Revision 1.0	No Title Found	52	9.960000038146973	
Feb 9, 2024	No Title Found	52	9.960000038146973	
CFR0012	No Title Found	52	7.559999942779541	
52 of 61	No Title Found	52	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	52	7.559999942779541	
Appendix A Schematics	No Title Found	52	14.039999961853027	
A.1	Appendix A Schematics	52	12.0	14.039999961853027
DA1453x Pro motherboard (610-01-B), schematic.	Appendix A Schematics	52	12.0	14.039999961853027
Figure 69. DA1453x Pro-MB (610-01-B), GPIO connectivity, voltage translators, and data flash	No Title Found	52	9.960000038146973	
UM-B-169	No Title Found	53	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	53	12.0	
Description	No Title Found	53	12.0	
User Manual	No Title Found	53	9.960000038146973	
Revision 1.0	No Title Found	53	9.960000038146973	
Feb 9, 2024	No Title Found	53	9.960000038146973	
CFR0012	No Title Found	53	7.559999942779541	
53 of 61	No Title Found	53	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	53	7.559999942779541	
Figure 70. DA1453x Pro-MB (610-01-B), PMM2 interface	No Title Found	53	9.960000038146973	
UM-B-169	No Title Found	54	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	54	12.0	
Description	No Title Found	54	12.0	
User Manual	No Title Found	54	9.960000038146973	
Revision 1.0	No Title Found	54	9.960000038146973	
Feb 9, 2024	No Title Found	54	9.960000038146973	
CFR0012	No Title Found	54	7.559999942779541	
54 of 61	No Title Found	54	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	54	7.559999942779541	
Figure 71. DA1453x Pro-MB (610-01-B), the MCU with segger implementation	No Title Found	54	9.960000038146973	
UM-B-169	No Title Found	55	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	55	12.0	
Description	No Title Found	55	12.0	
User Manual	No Title Found	55	9.960000038146973	
Revision 1.0	No Title Found	55	9.960000038146973	
Feb 9, 2024	No Title Found	55	9.960000038146973	
CFR0012	No Title Found	55	7.559999942779541	
55 of 61	No Title Found	55	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	55	7.559999942779541	
Figure 72. DA1453x Pro-MB (610-01-B), USB hub, UART and power section	No Title Found	55	9.960000038146973	
UM-B-169	No Title Found	56	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	56	12.0	
Description	No Title Found	56	12.0	
User Manual	No Title Found	56	9.960000038146973	
Revision 1.0	No Title Found	56	9.960000038146973	
Feb 9, 2024	No Title Found	56	9.960000038146973	
CFR0012	No Title Found	56	7.559999942779541	
56 of 61	No Title Found	56	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	56	7.559999942779541	
A.2	No Title Found	56	12.0	
DA14535 Pro daughterboard (610-02-A), schematic	No Title Found	56	12.0	
Figure 73. DA14535 Pro-DB (610-02-A)	No Title Found	56	9.960000038146973	
UM-B-169	No Title Found	57	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	57	12.0	
Description	No Title Found	57	12.0	
User Manual	No Title Found	57	9.960000038146973	
Revision 1.0	No Title Found	57	9.960000038146973	
Feb 9, 2024	No Title Found	57	9.960000038146973	
CFR0012	No Title Found	57	7.559999942779541	
57 of 61	No Title Found	57	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	57	7.559999942779541	
A.3	No Title Found	57	12.0	
DA14533 Pro daughterboard (610-05-A), schematic	No Title Found	57	12.0	
Figure 74. DA14533 Pro-DB (610-05-A)	No Title Found	57	9.960000038146973	
UM-B-169	No Title Found	58	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	58	12.0	
Description	No Title Found	58	12.0	
User Manual	No Title Found	58	9.960000038146973	
Revision 1.0	No Title Found	58	9.960000038146973	
Feb 9, 2024	No Title Found	58	9.960000038146973	
CFR0012	No Title Found	58	7.559999942779541	
58 of 61	No Title Found	58	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	58	7.559999942779541	
A.4	No Title Found	58	12.0	
PMM2, Power measurement module (500-29-E), schematic	No Title Found	58	12.0	
Figure 75. PMM2 (500-29-E), main circuit	No Title Found	58	9.960000038146973	
UM-B-169	No Title Found	59	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	59	12.0	
Description	No Title Found	59	12.0	
User Manual	No Title Found	59	9.960000038146973	
Revision 1.0	No Title Found	59	9.960000038146973	
Feb 9, 2024	No Title Found	59	9.960000038146973	
CFR0012	No Title Found	59	7.559999942779541	
59 of 61	No Title Found	59	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	59	7.559999942779541	
Figure 76. PMM2 (500-29-E), mating connector, power supply and EEPROM	No Title Found	59	9.960000038146973	
UM-B-169	No Title Found	60	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	60	12.0	
Description	No Title Found	60	12.0	
User Manual	No Title Found	60	9.960000038146973	
Revision 1.0	No Title Found	60	9.960000038146973	
Feb 9, 2024	No Title Found	60	9.960000038146973	
CFR0012	No Title Found	60	7.559999942779541	
60 of 61	No Title Found	60	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	60	7.559999942779541	
Revision History	No Title Found	60	14.039999961853027	
Revision	Revision History	60	9.0	14.039999961853027
Date	Revision History	60	9.0	14.039999961853027
Description	Revision History	60	9.0	14.039999961853027
1.0	Revision History	60	9.0	14.039999961853027
Feb 9, 2024	Revision History	60	9.0	14.039999961853027
Initial version.	Revision History	60	9.0	14.039999961853027
UM-B-169	No Title Found	61	14.039999961853027	
DA1453x Pro-Development Kit Hardware	No Title Found	61	12.0	
Description	No Title Found	61	12.0	
User Manual	No Title Found	61	9.960000038146973	
Revision 1.0	No Title Found	61	9.960000038146973	
Feb 9, 2024	No Title Found	61	9.960000038146973	
CFR0012	No Title Found	61	7.559999942779541	
61 of 61	No Title Found	61	7.559999942779541	
© 2024 Renesas Electronics	No Title Found	61	7.559999942779541	
Status Definitions	No Title Found	61	9.0	
Status	No Title Found	61	6.960000038146973	
Definition	No Title Found	61	6.960000038146973	
DRAFT	No Title Found	61	6.960000038146973	
The content of this document is under review and subject to formal approval, which may result in modifications or	No Title Found	61	6.960000038146973	
additions.	No Title Found	61	6.960000038146973	
APPROVED	No Title Found	61	6.960000038146973	
or unmarked	No Title Found	61	6.960000038146973	
The content of this document has been approved for publication.	No Title Found	61	6.960000038146973	
RoHS Compliance	No Title Found	61	9.0	
Renesas’ suppliers certify that its products are in compliance with the requirements of Directive 2011/65/EU of the European Parliament on the	No Title Found	61	6.960000038146973	
restriction of the use of certain hazardous substances in electrical and electronic equipment. RoHS certificates from our suppliers are available	No Title Found	61	6.960000038146973	
on request.	No Title Found	61	6.960000038146973	
Corporate Headquarters	No Title Found	62	10.5	
TOYOSU FORESIA, 3-2-24 Toyosu,	No Title Found	62	8.0	
Koto-ku, Tokyo 135-0061, Japan	No Title Found	62	8.0	
www.renesas.com	No Title Found	62	8.0	
Contact Information	No Title Found	62	10.5	
For further information on a product, technology, the most	No Title Found	62	8.0	
up-to-date version of a document, or your nearest sales	No Title Found	62	8.0	
office, please visit	No Title Found	62	8.0	
www.renesas.com/contact-us/	No Title Found	62	8.0	
.	No Title Found	62	8.0	
Trademarks	No Title Found	62	10.5	
Renesas and the Renesas logo are trademarks of Renesas	No Title Found	62	8.0	
Electronics Corporation. All trademarks and registered	No Title Found	62	8.0	
trademarks are the property  of their respective owners.	No Title Found	62	8.0	
IMPORTANT NOTICE AND DISCLAIMER	No Title Found	62	10.5	
RENESAS ELECTRONICS CORPORATION AND ITS SUBSIDIARIES (“RENESAS”) PROVIDES TECHNICAL	No Title Found	62	8.0	
SPECIFICATIONS AND RELIABILITY DATA (INCLUDING DATASHEETS), DESIGN RESOURCES (INCLUDING	No Title Found	62	8.0	
REFERENCE DESIGNS), APPLICATION OR OTHER DESIGN ADVICE, WEB TOOLS, SAFETY INFORMATION, AND	No Title Found	62	8.0	
OTHER RESOURCES “AS IS” AND WITH ALL FAULTS, AND DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED,	No Title Found	62	8.0	
INCLUDING, WITHOUT LIMITATION, ANY IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A	No Title Found	62	8.0	
PARTICULAR PURPOSE, OR NON-INFRINGEMENT OF THIRD-PARTY INTELLECTUAL PROPERTY RIGHTS.	No Title Found	62	8.0	
These resources are intended for developers who are designing with Renesas products. You are solely responsible for (1)	No Title Found	62	8.0	
selecting the appropriate products for your application, (2) designing, validating, and testing your application, and (3)	No Title Found	62	8.0	
ensuring your application meets applicable standards, and any other safety, security, or other requirements. These	No Title Found	62	8.0	
resources are subject to change without notice. Renesas grants you permission to use these resources only to develop an	No Title Found	62	8.0	
application that uses Renesas products. Other reproduction or use of these resources is strictly prohibited. No license is	No Title Found	62	8.0	
granted to any other Renesas intellectual property or to any third-party intellectual property. Renesas disclaims	No Title Found	62	8.0	
responsibility for, and you will fully indemnify Renesas and its representatives against, any claims, damages, costs, losses,	No Title Found	62	8.0	
or liabilities arising from your use of these resources. Renesas' products are provided only subject to Renesas' Terms and	No Title Found	62	8.0	
Conditions of Sale or other applicable terms agreed to in writing. No use of any Renesas resources expands or otherwise	No Title Found	62	8.0	
alters any applicable warranties or warranty disclaimers for these products.	No Title Found	62	8.0	
(Disclaimer Rev.1.01)	No Title Found	62	8.0	
© 2025 Renesas Electronics Corporation. All rights reserved.	No Title Found	62	8.0	

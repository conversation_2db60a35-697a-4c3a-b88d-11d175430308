:root {
	--primary: #00ff9d;
	--primary-glow: #00ff9d80;
	--secondary: #ff00ff;
	--secondary-glow: #ff00ff80;
	--bg: #0a0a0f;
	--sidebar-bg: #12121a;
	--card-bg: #12121a;
	--text: #ffffff;
	--text-light: #8b8b8b;
	--border: #2a2a3a;
}

body {
	background-color: var(--bg);
	color: var(--text);
	position: relative;
}

/* Grid Background */
body::before {
	content: '';
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: 
		linear-gradient(var(--bg) 2px, transparent 2px),
		linear-gradient(90deg, var(--bg) 2px, transparent 2px);
	background-size: 30px 30px;
	background-position: -1px -1px;
	background-color: rgba(0, 255, 157, 0.02);
	z-index: -1;
}

/* Sidebar Styles */
.sidebar {
	width: 240px;
	background: var(--sidebar-bg);
	padding: 1.5rem;
	box-shadow: 4px 0 15px rgba(0, 255, 157, 0.1);
	border-right: 1px solid var(--border);
	backdrop-filter: blur(10px);
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid var(--border);
}

.logo i {
	font-size: 24px;
	color: var(--primary);
	text-shadow: 0 0 10px var(--primary-glow);
}

.logo span {
	font-size: 20px;
	font-weight: 600;
	color: var(--text);
	text-transform: uppercase;
	letter-spacing: 2px;
}

.nav-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	color: var(--text-light);
	text-decoration: none;
	border-radius: 4px;
	margin-bottom: 8px;
	transition: all 0.3s ease;
	border: 1px solid transparent;
}

.nav-item:hover, .nav-item.active {
	background: rgba(0, 255, 157, 0.1);
	color: var(--primary);
	border: 1px solid var(--primary);
	box-shadow: 0 0 10px var(--primary-glow);
}

.nav-item i {
	font-size: 18px;
}

.sidebar-toggle {
	background: var(--primary);
	color: var(--bg);
	box-shadow: 0 0 10px var(--primary-glow);
}

/* Main Content Styles */
.main-content {
	margin-left: 240px;
	position: relative;
	padding: 2rem;
}

.main-content.expanded {
	margin-left: 80px;
}

/* Columns Layout */
.columns {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 2rem;
	margin-top: 1rem;
}

.column h2 {
	margin-bottom: 1.5rem;
	color: var(--primary);
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 2px;
	text-shadow: 0 0 10px var(--primary-glow);
}

/* Card Styles */
.card {
	background: var(--card-bg);
	border: 1px solid var(--border);
	border-radius: 4px;
	padding: 1.5rem;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.card:hover {
	border-color: var(--primary);
	box-shadow: 0 0 15px var(--primary-glow);
	transform: translateY(-5px);
}

.card h3 {
	color: var(--text);
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.card p {
	color: var(--secondary);
	font-family: monospace;
	font-size: 1.2rem;
	text-shadow: 0 0 10px var(--secondary-glow);
}

/* Decorative Elements */
.main-content::before {
	content: '';
	position: fixed;
	top: 20px;
	right: 20px;
	width: 200px;
	height: 200px;
	background: var(--primary);
	opacity: 0.1;
	filter: blur(40px);
	animation: pulse 4s ease-in-out infinite;
}

.main-content::after {
	content: '';
	position: fixed;
	bottom: 20px;
	left: 300px;
	width: 300px;
	height: 300px;
	background: var(--secondary);
	opacity: 0.1;
	filter: blur(60px);
	animation: pulse 6s ease-in-out infinite alternate;
}

/* Animations */
@keyframes pulse {
	0%, 100% { transform: scale(1); opacity: 0.1; }
	50% { transform: scale(1.2); opacity: 0.15; }
}

/* Responsive Design */
@media (max-width: 1024px) {
	.columns {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.columns {
		grid-template-columns: 1fr;
	}
}
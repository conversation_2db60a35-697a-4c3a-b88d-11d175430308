/* Base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: 'Inter', system-ui, sans-serif;
	line-height: 1.5;
}

.container {
	display: flex;
	min-height: 100vh;
}

/* Sidebar base styles */
.sidebar {
	position: fixed;
	height: 100vh;
	transition: all 0.3s ease;
	z-index: 100;
}

.sidebar.collapsed {
	width: 80px;
}

.sidebar-toggle {
	position: absolute;
	right: -12px;
	top: 20px;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	border: none;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: transform 0.3s ease;
}

.sidebar.collapsed .sidebar-toggle {
	transform: rotate(180deg);
}

/* Main content base styles */
.main-content {
	flex: 1;
	transition: margin-left 0.3s ease;
	padding: 2rem;
}

/* Card base styles */
.card {
	border-radius: 12px;
	padding: 1.5rem;
	margin-bottom: 1rem;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
	transform: translateY(-5px);
}

/* Animation keyframes */
@keyframes fadeIn {
	from { opacity: 0; transform: translateY(20px); }
	to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
	from { transform: translateX(-20px); opacity: 0; }
	to { transform: translateX(0); opacity: 1; }
}

/* Utility classes */
.animate-fade-in {
	animation: fadeIn 0.5s ease forwards;
}

.animate-slide-in {
	animation: slideIn 0.5s ease forwards;
}
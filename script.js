document.addEventListener('DOMContentLoaded', () => {
	// Add sidebar toggle functionality
	const sidebarToggle = document.querySelector('.sidebar-toggle');
	const sidebar = document.querySelector('.sidebar');
	const mainContent = document.querySelector('.main-content');
	
	sidebarToggle.addEventListener('click', () => {
		sidebar.classList.toggle('collapsed');
		mainContent.classList.toggle('expanded');
	});

	// Handle category switching
	document.querySelectorAll('.nav-item').forEach(item => {
		item.addEventListener('click', () => {
			// Remove active class from all items
			document.querySelectorAll('.nav-item').forEach(navItem => {
				navItem.classList.remove('active');
			});
			
			// Add active class to clicked item
			item.classList.add('active');
			
			// Get category name
			const category = item.querySelector('span').textContent.toLowerCase().replace(/\s+/g, '-');
			
			// Remove all show classes from subcategory grid
			const grid = document.querySelector('.subcategory-grid');
			grid.classList.remove('show-compare', 'show-pdf', 'show-generic', 'show-part-number');
			
			// Add show class for selected category
			grid.classList.add(`show-${category}`);
		});
	});

	// Card expansion functionality
	function expandCard(card) {
		if (card.classList.contains('expanded')) {
			card.classList.remove('expanded');
		} else {
			// Close other expanded cards
			document.querySelectorAll('.subcategory-card').forEach(c => {
				c.classList.remove('expanded');
			});
			card.classList.add('expanded');
		}
	}

	// Make expandCard function global
	window.expandCard = expandCard;

	// Prevent form submission from closing the card
	document.querySelectorAll('.upload-form').forEach(form => {
		form.addEventListener('click', (e) => {
			e.stopPropagation();
		});
	});

	// Handle file input changes
	document.querySelectorAll('.file-input').forEach(input => {
		input.addEventListener('change', (e) => {
			const fileName = e.target.files[0]?.name;
			if (fileName) {
				const button = e.target.nextElementSibling;
				button.textContent = 'Upload: ' + fileName;
			}
		});
	});

	// Handle file upload
	document.querySelectorAll('.upload-btn').forEach(button => {
		button.addEventListener('click', (e) => {
			e.preventDefault();
			const input = e.target.previousElementSibling;
			const file = input.files[0];
			if (file) {
				// Here you would handle the file upload
				console.log('Uploading file:', file.name);
				// Reset the form
				input.value = '';
				button.textContent = 'Upload File';
				// Close the expanded card
				setTimeout(() => {
					e.target.closest('.subcategory-card').classList.remove('expanded');
				}, 1000);
			}
		});
	});

	// Show Compare category by default
	document.querySelector('.nav-item').click();
});


#!/usr/bin/env python3
"""
Script to extract titles from files linked in an input file.
Processes each link, extracts titles where <PERSON><PERSON><PERSON> is N/A, and joins them with "|".
"""

import os
import sys
from typing import List, Set
import re

def extract_titles_from_file(file_path: str) -> List[str]:
    """
    Extract titles from a file where <PERSON>rror is N/A.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        List of unique titles where <PERSON>rror is N/A
    """
    titles = set()  # Use set to avoid duplicates
    
    try:
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        content = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print(f"Warning: Could not decode file {file_path}")
            return []
        
        # Process each line
        for line_num, line in enumerate(content.splitlines(), 1):
            line = line.strip()
            if not line:
                continue
            
            # Look for lines with the expected format
            # Line: X. [content] Changed_Feature: [value] Similarity_Percentage: [value] Title: [title] Error: [error]
            
            # Use regex to extract the components
            pattern = r'Line:\s*\d+\.\s*.*?\s+Changed_Feature:\s*(.*?)\s+Similarity_Percentage:\s*(.*?)\s+Title:\s*(.*?)\s+Error:\s*(.*?)$'
            match = re.search(pattern, line)
            
            if match:
                changed_feature = match.group(1).strip()
                similarity_percentage = match.group(2).strip()
                title = match.group(3).strip()
                error = match.group(4).strip()
                
                # Only include titles where Error is N/A
                if error.upper() == 'N/A':
                    if title and title.upper() != 'N/A':  # Make sure title is not empty or N/A
                        titles.add(title)
            else:
                # Try alternative parsing if regex doesn't match
                if 'Title:' in line and 'Error:' in line:
                    try:
                        # Extract title and error manually
                        title_start = line.find('Title:') + 6
                        error_start = line.find('Error:', title_start)
                        
                        if title_start > 5 and error_start > title_start:
                            title = line[title_start:error_start].strip()
                            error = line[error_start + 6:].strip()
                            
                            if error.upper() == 'N/A' and title and title.upper() != 'N/A':
                                titles.add(title)
                    except Exception as e:
                        print(f"Warning: Could not parse line {line_num} in {file_path}: {e}")
    
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return []
    
    return sorted(list(titles))  # Return sorted list

def process_links_file(input_file: str, output_file: str):
    """
    Process the input file containing links and generate output with joined titles.
    
    Args:
        input_file: Path to file containing links (one per line)
        output_file: Path to output file
    """
    
    print(f"Processing links from: {input_file}")
    print(f"Output will be saved to: {output_file}")
    
    try:
        # Read input file with links
        with open(input_file, 'r', encoding='utf-8') as f:
            links = [line.strip() for line in f if line.strip()]
        
        print(f"Found {len(links)} links to process")
        
        # Process each link
        results = []
        
        for i, link in enumerate(links, 1):
            print(f"Processing link {i}/{len(links)}: {os.path.basename(link)}")
            
            # Check if file exists
            if not os.path.exists(link):
                print(f"  Warning: File not found: {link}")
                results.append((link, "FILE_NOT_FOUND"))
                continue
            
            # Extract titles from the file
            titles = extract_titles_from_file(link)
            
            if titles:
                joined_titles = " | ".join(titles)
                results.append((link, joined_titles))
                print(f"  Found {len(titles)} titles: {joined_titles[:100]}...")
            else:
                results.append((link, "NO_TITLES_FOUND"))
                print(f"  No titles found with Error: N/A")
        
        # Write results to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Link\tTitles\n")  # Header
            
            for link, titles in results:
                f.write(f"{link}\t{titles}\n")
        
        print(f"\n✅ Processing complete!")
        print(f"📄 Processed {len(links)} links")
        print(f"💾 Results saved to: {output_file}")
        
        # Show summary
        successful = sum(1 for _, titles in results if titles not in ["FILE_NOT_FOUND", "NO_TITLES_FOUND"])
        not_found = sum(1 for _, titles in results if titles == "FILE_NOT_FOUND")
        no_titles = sum(1 for _, titles in results if titles == "NO_TITLES_FOUND")
        
        print(f"\n📊 Summary:")
        print(f"   • Successfully processed: {successful}")
        print(f"   • Files not found: {not_found}")
        print(f"   • No titles found: {no_titles}")
        
    except FileNotFoundError:
        print(f"Error: Input file not found: {input_file}")
    except Exception as e:
        print(f"Error processing input file: {e}")

def create_sample_input_file():
    """Create a sample input file for testing."""
    sample_content = """\\\\10.199.104.106\\SW_backup\\Said\\Pdf_Compare_Results\\mmg_1752065095897.txt
\\\\10.199.104.106\\SW_backup\\Said\\Pdf_Compare_Results\\another_file.txt
\\\\10.199.104.106\\SW_backup\\Said\\Pdf_Compare_Results\\third_file.txt"""
    
    with open('sample_links.txt', 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print("Created sample input file: sample_links.txt")

def test_single_file():
    """Test with a single file to demonstrate the functionality."""
    test_file_path = r"\\10.199.104.106\SW_backup\Said\Pdf_Compare_Results\mmg_1752065095897.txt"
    
    print("Testing with single file:")
    print(f"File: {test_file_path}")
    
    if os.path.exists(test_file_path):
        titles = extract_titles_from_file(test_file_path)
        if titles:
            joined = " | ".join(titles)
            print(f"Found titles: {joined}")
        else:
            print("No titles found with Error: N/A")
    else:
        print("Test file not found")

def main():
    """Main function."""
    print("Title Extractor from Links")
    print("=" * 50)
    
    # Check command line arguments
    if len(sys.argv) == 1:
        print("Usage:")
        print("  python extract_titles_from_links.py input_file.txt output_file.txt")
        print("  python extract_titles_from_links.py --test")
        print("  python extract_titles_from_links.py --sample")
        print()
        print("Options:")
        print("  --test    Test with single file")
        print("  --sample  Create sample input file")
        return
    
    if len(sys.argv) == 2:
        if sys.argv[1] == '--test':
            test_single_file()
            return
        elif sys.argv[1] == '--sample':
            create_sample_input_file()
            return
        else:
            print("Error: Please provide both input and output file names")
            return
    
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
        
        process_links_file(input_file, output_file)

if __name__ == "__main__":
    main()

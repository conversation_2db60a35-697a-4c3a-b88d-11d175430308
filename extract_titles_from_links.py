#!/usr/bin/env python3
"""
Script to extract titles from files linked in input.txt.
Processes each link, extracts titles where <PERSON><PERSON><PERSON> is N/A, and joins them with "|".
Output is saved to output.txt.
"""

import os
import re

def extract_titles_from_file(file_path):
    """Extract titles from a file where <PERSON>rror is N/A."""
    titles = set()  # Use set to avoid duplicates

    try:
        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print(f"Warning: Could not decode file {file_path}")
            return []

        # Process each line
        for line in content.splitlines():
            line = line.strip()
            if not line:
                continue

            # Look for lines with Title: and Error:
            if 'Title:' in line and 'Title_Failing_Reason:' in line:
                try:
                    # Extract title and error manually
                    title_start = line.find('Title:') + 6
                    error_start = line.find('Title_Failing_Reason:', title_start)

                    if title_start > 5 and error_start > title_start:
                        title = line[title_start:error_start].strip()
                        error = line[error_start + 6:].strip()

                        # Only include titles where Error is N/A
                        if error.upper() == 'N/A' and title and title.upper() != 'N/A':
                            titles.add(title)
                except:
                    continue

    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return []

    return sorted(list(titles))

def main():
    """Main function - processes input.txt and creates output.txt"""
    print("Title Extractor from Links")
    print("=" * 50)

    input_file = "input.txt"
    output_file = "output.txt"

    print(f"Processing links from: {input_file}")
    print(f"Output will be saved to: {output_file}")

    try:
        # Read input file with links
        with open(input_file, 'r', encoding='utf-8') as f:
            links = [line.strip() for line in f if line.strip()]

        print(f"Found {len(links)} links to process")

        # Process each link
        results = []

        for i, link in enumerate(links, 1):
            print(f"Processing link {i}/{len(links)}: {os.path.basename(link)}")

            # Check if file exists
            if not os.path.exists(link):
                print(f"  Warning: File not found: {link}")
                results.append((link, "FILE_NOT_FOUND"))
                continue

            # Extract titles from the file
            titles = extract_titles_from_file(link)

            if titles:
                joined_titles = " | ".join(titles)
                results.append((link, joined_titles))
                print(f"  Found {len(titles)} titles: {joined_titles[:100]}...")
            else:
                results.append((link, "NO_TITLES_FOUND"))
                print(f"  No titles found with Error: N/A")

        # Write results to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Link\tTitles\n")  # Header

            for link, titles in results:
                f.write(f"{link}\t{titles}\n")

        print(f"\n✅ Processing complete!")
        print(f"📄 Processed {len(links)} links")
        print(f"💾 Results saved to: {output_file}")

        # Show summary
        successful = sum(1 for _, titles in results if titles not in ["FILE_NOT_FOUND", "NO_TITLES_FOUND"])
        not_found = sum(1 for _, titles in results if titles == "FILE_NOT_FOUND")
        no_titles = sum(1 for _, titles in results if titles == "NO_TITLES_FOUND")

        print(f"\n📊 Summary:")
        print(f"   • Successfully processed: {successful}")
        print(f"   • Files not found: {not_found}")
        print(f"   • No titles found: {no_titles}")

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_file}")
        print("Please create input.txt with your file links (one per line)")
    except Exception as e:
        print(f"Error processing input file: {e}")

if __name__ == "__main__":
    main()

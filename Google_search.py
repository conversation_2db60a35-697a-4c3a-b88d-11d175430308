# from selenium import webdriver
# from selenium.webdriver.common.by import By
# from selenium.webdriver.common.keys import Keys
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.support import expected_conditions as EC
# from selenium.webdriver.firefox.options import Options  # Import options for headless mode
# import pandas as pd


# class ResultRow:
#     # Constructor (optional)
#     def __init__(self, link, pdf_status, zone_status, keyword_status):
#         # Initialize attributes
#         self.link = link
#         self.keyword_status = keyword_status
#         self.zone_status = zone_status
#         self.pdf_status = pdf_status




# def search_google(keyword, supplier_name):
#     # Set up Firefox options for headless mode
#     firefox_options = Options()
#     firefox_options.add_argument("--headless")  # Enable headless mode

#     # Initialize WebDriver with headless mode
#     browser = webdriver.Firefox(options=firefox_options)
#     results_list = []

#     try:
#         # Open Google
#         browser.get('http:\\www.google.com')
        
#         # Wait for the search bar to be available
#         search = WebDriverWait(browser, 10).until(
#             EC.presence_of_element_located((By.NAME, 'q'))
#         )
        
#         # Input search query and submit
#         search.send_keys(f"{keyword} {supplier_name}")
#         search.send_keys(Keys.RETURN)
        
#         # Wait for the search results to load
#         WebDriverWait(browser, 10).until(
#             EC.presence_of_element_located((By.ID, 'search'))  # Ensures results page loads
#         )
        
#         # Extract search result titles and links
#         results = browser.find_elements(By.XPATH, '\\div[@class="yuRUbf"]')  # Update the XPath if needed
#         elements = result.find_elements(By.XPATH, '\\*[@id="rso"]\\div\div\div\div[2]\div\span')
        
#         # Iterate through results and extract title and link
#         for result in results:
#             title = result.find_element(By.XPATH, '.\\h3').text  # Extract the title
#             link = result.find_element(By.XPATH, '.\\a').get_attribute('href')  # Extract the link
#             results_list.append([title, link])
#     finally:
#         # Quit the browser
#         browser.quit()
    
#     return results_list

# def main():
#     data_base_df = pd.read_excel("DataBase.xlsx")
#     keyword_supplier_df = pd.read_excel("input.xlsx")
#     all_result_rows = []
#     for _, row in keyword_supplier_df.iterrows():  # `_` is used since the index isn't needed
#         keyword = row[keyword_supplier_df.columns[0]]  # Replace 'keyword' with the correct column name from `keyword_supplier_df`
#         supplier = row[keyword_supplier_df.columns[1]]  # Replace 'supplier' with the correct column name from `keyword_supplier_df`

#         # Filter rows in data_base_df where 'SE_Name' contains the supplier
#         # matching_rows = data_base_df.loc[data_base_df['SE_Name'].str.contains(supplier, na=False, case=False)]
#         approved_zone_series = data_base_df.loc[data_base_df['SE_Name'].str.casefold() == supplier.casefold()]['Approved_Zone']
#         approved_zone = approved_zone_series.iloc[0] if not approved_zone_series.empty else None

#         results_list = search_google(keyword, supplier)

#         for result in results_list:
#             found_title = result[0].lower()
#             found_link = result[-1].lower()

#             if keyword.lower() in found_title:
#                 keyword_status = 'Found'
#             else:
#                 keyword_status = 'Not Found'

#             if approved_zone.lower() in found_link:
#                 zone_status = 'Found'
#             else:
#                 zone_status = 'Not Found'
            
#             if ".pdf" in found_link:
#                 pdf_status = 'Found'
#             else:
#                 pdf_status = 'Not Found'

#             all_result_rows.append(ResultRow(found_link, pdf_status, zone_status, keyword_status))

#             print(pdf_status, zone_status, keyword_status)
#             print(result)

#         print("-" * 50)

# # Call the main function
# main()

from multiprocessing import freeze_support
import concurrent.futures
import fitz
import  re
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown

class Block:
    def __init__(right, up, left, down, text, self):
        self.right = right
        self.up = up
        self.left = left
        self.down = down
        self.text = text


link = r"\\**************\pdfs2\2024\12\22\13\8\37\24492652\txn_\manual\tcan1043nq1.pdf"
link = r"\\**************\pdfs2\2024\9\12\20\33\12\301895\apitec_\manual\vuywzngp7nsmc11ixh1n.pdf"
link1 = r"\\**************\pdfs2\2024\10\18\14\56\32\531191\maxlin_\manual\document.ashxfilenamexr620a_ds.pdf"
link2 = r"\\**************\pdfs2\2025\1\9\17\20\54\945023960\maxlin_\manual\document.ashxfilenamexr620ads.pdf"




def main(links):
    try:
        blocks_list_1 = []
        blocks_list_2 = []
        blocks_list_1_1 = []
        blocks_list_2_2 = []
        link_1 = links.split("\t")[0]
        link_2 = links.split("\t")[-1].strip()
        doc_1 = fitz.open(link_1)
        doc_2 = fitz.open(link_2)
        for  page_number, page in enumerate(doc_1):
            for block in enumerate(page.get_text("blocks")):   
                try:
                    indicies = [round(x) for x in block[1][0:4]]
                    block_1= indicies
                    page.draw_rect(indicies,  color = (0, 1, 0), width = .25)
                except:
                    pass

        for  page_number, page in enumerate(doc_2):
            for block in enumerate(page.get_text("blocks")):   
                try:
                    indicies = [round(x) for x in block[1][0:4]]
                    block_2= indicies
                    page.draw_rect(indicies,  color = (0, 1, 0), width = .25)
                except:
                    pass

        for  page_number, page in enumerate(doc_1):
            for block in enumerate(page.get_text("blocks")):   
                try:  
                    # indicies = [round(x) for x in block[1][0:4]]
                    block_1_1 = [round(x) for x in block[1][0:4]]
                    block_1= [round(x) for x in block[1][0:4]]
                    block_1_1.append(page_number)
                    block_1.append(block[1][4])
                    block_1.append(page_number)
                    blocks_list_1.append(block_1)
                    blocks_list_1_1.append(block_1_1)
                except:
                    pass

        for  page_number, page in enumerate(doc_2):
            for block in enumerate(page.get_text("blocks")):   
                try:  
                    # indicies = [round(x) for x in block[1][0:4]]
                    block_2=  [round(x) for x in block[1][0:4]]
                    block_2_2 =  [round(x) for x in block[1][0:4]]
                    block_2_2.append(page_number)
                    block_2.append(block[1][4])
                    block_2.append(page_number)
                    blocks_list_2.append(block_2)
                    blocks_list_2_2.append(block_2_2)
                except:
                    pass

        set_blocks_1 = set(tuple(block) for block in blocks_list_1)
        set_blocks_2 = set(tuple(block) for block in blocks_list_2)
        diff_blocks_2 = set_blocks_2.difference(set_blocks_1)
        diff_blocks_1 = set_blocks_1.difference(set_blocks_2)

        for block in diff_blocks_1:   
                try:
                    indicies = block[0:4]
                    page_number = block[-1]
                    doc_1[page_number].draw_rect(indicies,  color = (0, 0, 1), width = .25)
                except:
                    pass

        doc_1.save('name_pdf_1.pdf')


        for block in diff_blocks_2:   
                try:
                    indicies = block[0:4]
                    page_number = block[-1]
                    doc_2[page_number].draw_rect(indicies,  color = (1, 0, 0), width = .25)
                except:
                    pass
        doc_2.save('name_pdf_2.pdf')


        if blocks_list_2_2 == blocks_list_1_1:
            structure = "same"
        else:
             structure = "not the same"

        return links.strip()+"\t"+structure
    
    except Exception as E:
        return links.strip()+"\t"+str(E)



with open("input.txt", "r") as input_file:
    links_list = input_file.readlines()

with open("output.txt", "w") as output_file:
    output_file.write("Document\tLatest\tstructure")
    output_file.write("\n")

for index, row in enumerate(map(main, links_list), 1):
    with open("output.txt", "a", encoding='utf8') as output_file:
        output_file.write(row)
        output_file.write("\n")

    print(f"Done_{index}".center(50, "-").upper())

# if __name__ == '__main__':

#     with open("input.txt", "r") as input_file:
#         links_list = input_file.readlines()

#     with open("output.txt", "w") as output_file:
#         output_file.write("Document\tLatest\tstructure")
#         output_file.write("\n")

#     freeze_support()
#     console = Console()
#     title = '''# BLOCK MATCHER'''
#     my_copyright = '''# © <EMAIL>'''
#     title = Markdown(title)
#     my_copyright = Markdown(my_copyright)
#     console.print(title)
#     console.print(my_copyright)
#     one_time_count = 500
#     total_rows = len(links_list)

#     with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
#             ncols=100) as progress_bar:

#         with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
#             for i in range(1, len(links_list), one_time_count):
#                 batch_links = links_list[i:i + one_time_count]
#                 results = executor1.map(main, batch_links)
#                 for result in results:
#                     try:
#                         with open("output.txt", 'a', encoding='utf8') as of:
#                             of.write(result)
#                             of.write('\n')
#                     except:
#                         pass
#                     progress_bar.update(1)
#         progress_bar.set_description(f"done".upper())
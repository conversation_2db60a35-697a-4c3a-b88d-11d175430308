<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>BlueI - Document Management System</title>
	<link rel="stylesheet" href="styles.css">
	<link href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
	<div class="container">
		<div class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-cube"></i>
				<span>BlueI</span>
			</div>
			<nav>
				<div class="nav-item active">
					<i class="fas fa-exchange-alt"></i>
					<span>Compare</span>
				</div>
				<div class="nav-item">
					<i class="fas fa-file-pdf"></i>
					<span>PDF</span>
				</div>
				<div class="nav-item">
					<i class="fas fa-cogs"></i>
					<span>Generic</span>
				</div>
				<div class="nav-item">
					<i class="fas fa-microchip"></i>
					<span>Part Number</span>
				</div>
			</nav>
		</div>

		<main class="main-content">
			<div class="header">
				<div class="user-profile">
					<img src="https://via.placeholder.com/32" alt="User Profile">
				</div>
			</div>

			<div class="content-grid">
				<!-- Column 1: Subcategory Cards -->
				<div class="column subcategories">
					<div class="subcategory-grid">
						<!-- Compare subcategories -->
						<div class="subcategory-card compare-subcategory" onclick="expandCard(this)">
							<i class="fas fa-book"></i>
							<h3>Catalog Compare</h3>
							<p>Compare and analyze PDFs with large page counts</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card compare-subcategory" onclick="expandCard(this)">
							<i class="fas fa-file-pdf"></i>
							<h3>Acrobat simulation</h3>
							<p>Simulate Adobe Acrobat functionality</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card compare-subcategory" onclick="expandCard(this)">
							<i class="fas fa-magic"></i>
							<h3>Magic</h3>
							<p>Advanced document processing</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card compare-subcategory" onclick="expandCard(this)">
							<i class="fas fa-equals"></i>
							<h3>Equal Manual</h3>
							<p>Manual document comparison</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card compare-subcategory" onclick="expandCard(this)">
							<i class="fas fa-cogs"></i>
							<h3>Supplier Configuration</h3>
							<p>Configure supplier settings</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>

						<!-- PDF subcategories -->
						<div class="subcategory-card pdf-subcategory" onclick="expandCard(this)">
							<i class="fas fa-calendar-plus"></i>
							<h3>Introduction Date</h3>
							<p>Manage PDF introduction dates</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>

						<!-- Generic subcategories -->
						<div class="subcategory-card generic-subcategory" onclick="expandCard(this)">
							<i class="fas fa-tags"></i>
							<h3>MetaData</h3>
							<p>Manage document metadata</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card generic-subcategory" onclick="expandCard(this)">
							<i class="fas fa-file-export"></i>
							<h3>Extraction</h3>
							<p>Extract document content</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
						<div class="subcategory-card generic-subcategory" onclick="expandCard(this)">
							<i class="fas fa-search"></i>
							<h3>Keyword Search</h3>
							<p>Search document keywords</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>

						<!-- Part Number subcategories -->
						<div class="subcategory-card part-number-subcategory" onclick="expandCard(this)">
							<i class="fas fa-list"></i>
							<h3>Part list Extraction</h3>
							<p>Extract part numbers from documents</p>
							<div class="upload-form">
								<input type="file" class="file-input">
								<button class="upload-btn">Upload File</button>
							</div>
						</div>
					</div>
				</div>

				<!-- Column 2: Stats Cards -->
				<div class="column stats">
					<div class="stat-card">
						<h3>Active Sessions</h3>
						<div class="stat-value">12</div>
					</div>
					<div class="stat-card">
						<h3>Active Users</h3>
						<div class="stat-value">45</div>
					</div>
					<div class="stat-card">
						<h3>History</h3>
						<div class="stat-list">
							<div class="stat-item">Last session: 2h ago</div>
							<div class="stat-item">Total sessions: 156</div>
						</div>
					</div>
				</div>

				<!-- Column 3: Details -->
				<div class="column details">
					<div class="detail-card">
						<h3>Selected Item Details</h3>
						<div class="detail-content">
							<p>Select a subcategory to view details</p>
						</div>
					</div>
				</div>
			</div>
		</main>
	</div>
	<script src="script.js"></script>
</body>
</html>




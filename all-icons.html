<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Font Awesome Icon Browser</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<style>
		:root {
			--bg-color: #1a1b1e;
			--card-bg: #25262b;
			--border-color: #2d2d2d;
			--accent: #6d28d9;
			--text: #ffffff;
			--text-secondary: #a1a1aa;
		}
		
		body {
			font-family: 'Inter', system-ui, sans-serif;
			background: var(--bg-color);
			color: var(--text);
			margin: 0;
			padding: 2rem;
			line-height: 1.5;
		}

		.container {
			max-width: 1400px;
			margin: 0 auto;
		}

		.header {
			margin-bottom: 2rem;
		}

		.search-container {
			position: sticky;
			top: 0;
			background: var(--bg-color);
			padding: 1rem 0;
			z-index: 100;
		}

		.search-box {
			width: 100%;
			padding: 1rem;
			background: var(--card-bg);
			border: 1px solid var(--border-color);
			border-radius: 8px;
			color: var(--text);
			font-size: 16px;
		}

		.icon-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
			gap: 1rem;
			margin-top: 2rem;
		}

		.icon-item {
			background: var(--card-bg);
			padding: 1rem;
			border-radius: 8px;
			text-align: center;
			cursor: pointer;
			transition: all 0.3s ease;
			border: 1px solid var(--border-color);
		}

		.icon-item:hover {
			transform: translateY(-2px);
			border-color: var(--accent);
		}

		.icon-item i {
			font-size: 24px;
			color: var(--accent);
			margin-bottom: 0.5rem;
			display: block;
		}

		.icon-name {
			font-size: 12px;
			color: var(--text-secondary);
			word-break: break-all;
		}

		.copied {
			background: var(--accent);
		}

		.copied .icon-name {
			color: var(--text);
		}

		#loading {
			text-align: center;
			padding: 2rem;
			color: var(--text-secondary);
		}

		.style-filter {
			display: flex;
			gap: 1rem;
			margin: 1rem 0;
		}

		.style-btn {
			background: var(--card-bg);
			border: 1px solid var(--border-color);
			color: var(--text);
			padding: 0.5rem 1rem;
			border-radius: 4px;
			cursor: pointer;
		}

		.style-btn.active {
			background: var(--accent);
			border-color: var(--accent);
		}
	</style>
</head>
<body>
	<div class="container">
		<div class="header">
			<h1>Font Awesome Icons</h1>
			<div class="search-container">
				<input type="text" class="search-box" placeholder="Search icons...">
				<div class="style-filter">
					<button class="style-btn active" data-style="fas">Solid</button>
					<button class="style-btn" data-style="far">Regular</button>
					<button class="style-btn" data-style="fab">Brands</button>
				</div>
			</div>
		</div>
		<div id="loading">Loading icons...</div>
		<div class="icon-grid" id="iconGrid"></div>
	</div>

	<script>
		const iconList = [
			// Solid icons
			{ name: 'address-book', styles: ['fas', 'far'] },
			{ name: 'address-card', styles: ['fas', 'far'] },
			{ name: 'adjust', styles: ['fas'] },
			{ name: 'air-freshener', styles: ['fas'] },
			// Add more icons here...
		];

		function loadIcons() {
			const grid = document.getElementById('iconGrid');
			const loading = document.getElementById('loading');
			const activeStyle = document.querySelector('.style-btn.active').dataset.style;

			grid.innerHTML = '';
			loading.style.display = 'none';

			iconList.forEach(icon => {
				if (icon.styles.includes(activeStyle)) {
					const div = document.createElement('div');
					div.className = 'icon-item';
					div.innerHTML = `
						<i class="${activeStyle} fa-${icon.name}"></i>
						<div class="icon-name">${icon.name}</div>
					`;
					div.onclick = () => copyToClipboard(`${activeStyle} fa-${icon.name}`);
					grid.appendChild(div);
				}
			});
		}

		function copyToClipboard(text) {
			navigator.clipboard.writeText(text);
			const item = event.currentTarget;
			item.classList.add('copied');
			setTimeout(() => item.classList.remove('copied'), 500);
		}

		document.querySelector('.search-box').addEventListener('input', (e) => {
			const searchTerm = e.target.value.toLowerCase();
			document.querySelectorAll('.icon-item').forEach(item => {
				const iconName = item.querySelector('.icon-name').textContent.toLowerCase();
				item.style.display = iconName.includes(searchTerm) ? 'block' : 'none';
			});
		});

		document.querySelectorAll('.style-btn').forEach(btn => {
			btn.addEventListener('click', () => {
				document.querySelectorAll('.style-btn').forEach(b => b.classList.remove('active'));
				btn.classList.add('active');
				loadIcons();
			});
		});

		// Initial load
		loadIcons();

		// Fetch complete icon list from Font Awesome API
		fetch('https://raw.githubusercontent.com/FortAwesome/Font-Awesome/master/metadata/icons.json')
			.then(response => response.json())
			.then(data => {
				iconList.length = 0;
				Object.entries(data).forEach(([name, info]) => {
					iconList.push({
						name,
						styles: info.styles.map(style => 
							style === 'solid' ? 'fas' : 
							style === 'regular' ? 'far' : 
							style === 'brands' ? 'fab' : style
						)
					});
				});
				loadIcons();
			})
			.catch(error => {
				console.error('Error loading icons:', error);
				document.getElementById('loading').textContent = 'Error loading icons. Using fallback list.';
			});
	</script>
</body>
</html>
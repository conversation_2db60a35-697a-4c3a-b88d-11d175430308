#!/usr/bin/env python3
"""
Simple movie scraper for hdtodayz.to
Extracts: movie name, year, length, genre, rating, and country
Saves results to output.txt
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import re

def setup_driver():
    """Setup Chrome driver with options."""
    chrome_options = Options()
    # Uncomment the next line to run headless (without browser window)
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        service = Service(executable_path='chromedriver.exe')
        driver = webdriver.Chrome(service=service, options=chrome_options)
    except:
        # Try without specifying path
        driver = webdriver.Chrome(options=chrome_options)
    
    return driver

def extract_movie_info(movie_element):
    """Extract movie information from a movie element."""
    movie_info = {
        'name': 'N/A',
        'year': 'N/A', 
        'length': 'N/A',
        'genre': 'N/A',
        'rating': 'N/A',
        'country': 'N/A'
    }
    
    try:
        # Movie name
        try:
            name_elem = movie_element.find_element(By.CSS_SELECTOR, ".film-name a")
            movie_info['name'] = name_elem.get_attribute('title') or name_elem.text.strip()
        except:
            try:
                name_elem = movie_element.find_element(By.CSS_SELECTOR, ".film-detail .film-name")
                movie_info['name'] = name_elem.text.strip()
            except:
                pass
        
        # Year, length, genre, country from film details
        try:
            detail_items = movie_element.find_elements(By.CSS_SELECTOR, ".fdi-item")
            for item in detail_items:
                text = item.text.strip()
                
                # Year (4 digits starting with 19 or 20)
                if re.search(r'\b(19|20)\d{2}\b', text) and movie_info['year'] == 'N/A':
                    year_match = re.search(r'\b(19|20)\d{2}\b', text)
                    movie_info['year'] = year_match.group()
                
                # Length (contains 'min' or 'h')
                if ('min' in text.lower() or 'h' in text.lower()) and movie_info['length'] == 'N/A':
                    movie_info['length'] = text
                
                # Genre (common movie genres)
                genres = ['Action', 'Comedy', 'Drama', 'Horror', 'Thriller', 'Romance', 
                         'Sci-Fi', 'Fantasy', 'Adventure', 'Crime', 'Mystery', 'Animation']
                for genre in genres:
                    if genre.lower() in text.lower() and movie_info['genre'] == 'N/A':
                        movie_info['genre'] = text
                        break
                
                # Country (common countries)
                countries = ['USA', 'UK', 'United States', 'United Kingdom', 'Canada', 
                           'France', 'Germany', 'Japan', 'Korea', 'China', 'India', 'Australia']
                for country in countries:
                    if country.lower() in text.lower() and movie_info['country'] == 'N/A':
                        movie_info['country'] = text
                        break
        except:
            pass
        
        # Rating
        try:
            rating_elem = movie_element.find_element(By.CSS_SELECTOR, ".film-rating .tick-rate")
            movie_info['rating'] = rating_elem.text.strip()
        except:
            try:
                rating_elem = movie_element.find_element(By.CSS_SELECTOR, ".imdb-rate")
                movie_info['rating'] = rating_elem.text.strip()
            except:
                pass
    
    except Exception as e:
        print(f"Error extracting movie info: {e}")
    
    return movie_info

def scrape_movies():
    """Main function to scrape movies."""
    print("🎬 Starting movie scraper for hdtodayz.to")
    print("=" * 50)
    
    # Setup driver
    driver = setup_driver()
    movies_data = []
    
    try:
        # Navigate to the movie page
        url = "https://hdtodayz.to/movie"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(5)
        
        # Find all movie elements
        try:
            wait = WebDriverWait(driver, 10)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".flw-item")))
            movie_elements = driver.find_elements(By.CSS_SELECTOR, ".flw-item")
            print(f"Found {len(movie_elements)} movies")
        except:
            print("Could not find movie elements. The website structure might have changed.")
            return
        
        # Extract information from each movie
        for i, movie_element in enumerate(movie_elements, 1):
            print(f"Processing movie {i}/{len(movie_elements)}")
            
            movie_info = extract_movie_info(movie_element)
            
            if movie_info['name'] != 'N/A':
                movies_data.append(movie_info)
                print(f"  ✓ {movie_info['name']} ({movie_info['year']})")
            else:
                print(f"  ✗ Could not extract movie name")
        
        # Save to output.txt
        if movies_data:
            with open('output.txt', 'w', encoding='utf-8') as f:
                # Write header
                f.write("name\tyear\tlength\tgenre\trating\tcountry\n")
                
                # Write movie data
                for movie in movies_data:
                    f.write(f"{movie['name']}\t{movie['year']}\t{movie['length']}\t")
                    f.write(f"{movie['genre']}\t{movie['rating']}\t{movie['country']}\n")
            
            print(f"\n✅ Successfully scraped {len(movies_data)} movies")
            print("📄 Data saved to output.txt")
            
            # Show first 5 movies as preview
            print(f"\n📋 Preview (first 5 movies):")
            print("-" * 80)
            for i, movie in enumerate(movies_data[:5], 1):
                print(f"{i}. {movie['name']}")
                print(f"   Year: {movie['year']} | Length: {movie['length']}")
                print(f"   Genre: {movie['genre']} | Rating: {movie['rating']}")
                print(f"   Country: {movie['country']}")
                print("-" * 80)
        else:
            print("❌ No movies were successfully scraped")
    
    except Exception as e:
        print(f"Error during scraping: {e}")
    
    finally:
        driver.quit()
        print("🔚 Browser closed")

if __name__ == "__main__":
    scrape_movies()

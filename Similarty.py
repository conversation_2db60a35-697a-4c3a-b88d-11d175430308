import pandas as pd
import re
from datetime import datetime
import nltk
from nltk.corpus import wordnet
from itertools import product
import os

try:
    wordnet.ensure_loaded()
except LookupError:
    nltk.download('wordnet')
    nltk.download('omw-1.4')

def clean_text(text):
    if not isinstance(text, str) or not text.strip():
        return ""
    
    # Remove special characters
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
    # Remove combinations of characters and numbers without spaces
    text = re.sub(r'[a-zA-Z]+[0-9]+[a-zA-Z0-9]*|[0-9]+[a-zA-Z]+[a-zA-Z0-9]*', ' ', text)
    # Remove pure numbers
    text = re.sub(r'\b\d+\b', ' ', text)
    text = re.sub(r'\s+','', text)
    # Normalize spaces
    text = ' '.join(text.split())
    return text.lower()

def get_synsets(word):
    words = word.split()
    all_syns = set()
    
    if len(words) > 1:
        compound_syns = wordnet.synsets(word.replace(" ", "_"))
        if compound_syns:
            all_syns.update(compound_syns)
        
        for w in words:
            all_syns.update(wordnet.synsets(w))
        
        for i in range(len(words)-1):
            compound = f"{words[i]}_{words[i+1]}"
            all_syns.update(wordnet.synsets(compound))
    else:
        all_syns.update(wordnet.synsets(word))
    
    return list(all_syns)

def calculate_similarity(text1, text2):
    if not text1 or not text2:
        return 0
    
    sims = []
    syns1 = get_synsets(text1)
    syns2 = get_synsets(text2)
    
    if not syns1 or not syns2:
        return 0
    
    for sense1, sense2 in product(syns1, syns2):
        sim = wordnet.wup_similarity(sense1, sense2)
        if sim is not None:
            sims.append(sim)
    
    return max(sims) if sims else 0

def check_patterns(line, cleaned_line):
    words_of_interest = [
        'no', 'candidate', 'reach', 'svhc', 'substance', 'article 59',
        'echa', 'rohs', 'obsolete', 'discontinued', 'last time buy',
        'not recommended for new design', 'nrnd', 'withdrawn', 'eol',
        'ltb', 'discontinu', 'not recommend', 'end of life',
        'not for new design', 'inactive', 'end-of-life', 'not active',
        'end of service', 'end-of-service'
    ]
    
    # Check for words of interest
    for word in words_of_interest:
        if word in cleaned_line:
            return "Word of interest found"
    
    # Check for part numbers
    if (re.search(r'\d{8,}', line) or 
        re.search(r'[a-zA-Z]+[0-9]+[a-zA-Z0-9]*|[0-9]+[a-zA-Z]+[a-zA-Z0-9]*', line) or
        'part no' in cleaned_line):
        return "Part number found"
    
    # Check for date or revision patterns
    if (re.search(r'20[0-9]{2}[/.-]+[0-9]{1,2}[/.-]+[0-9]{1,2}', line) or
        re.search(r'phone[\s:+]+', line)):
        return "Skip"
    
    return None

def map_line_to_feature(line, feature_db):
    if not isinstance(line, str):
        return [], ["Invalid input type"], "Error"
    
    if not line.strip():
        return [], ["Empty line"], "Done"
    
    notes = []
    # Check patterns before cleaning
    pattern_result = check_patterns(line, line.lower())
    if pattern_result == "Skip":
        notes.append("Skip - Date/Revision pattern found")
    elif pattern_result:
        notes.append(pattern_result)
    
    # Clean text after pattern checks
    cleaned_line = clean_text(line)
    if not cleaned_line:
        notes.append("Empty after cleaning")
    
    matched_features = []
    
    # Continue with mapping even if line is empty after cleaning
    if cleaned_line:
        # Direct match check
        direct_matches = feature_db[feature_db['Feature'].str.lower() == cleaned_line]
        if not direct_matches.empty:
            matched_features.extend(direct_matches['Feature'].tolist())
            notes.append("Direct match found")
        
        # Similarity check if no direct matches
        if not matched_features:
            similarities = []
            for feature in feature_db['Feature'].dropna():
                sim = calculate_similarity(cleaned_line, feature.lower())
                if sim >= 0.5:
                    similarities.append((sim, feature))
            
            if similarities:
                max_sim = max(similarities, key=lambda x: x[0])
                highest_matches = [f[1] for f in similarities if f[0] == max_sim[0]]
                matched_features.extend(highest_matches)
                notes.append(f"Similarity score: {max_sim[0]:.2f}")
        
        # Word-by-word check for short phrases if still no matches
        if not matched_features and len(cleaned_line.split()) <= 3:
            word_matches = []
            for word in cleaned_line.split():
                direct_word_matches = feature_db[feature_db['Feature'].str.lower().str.contains(r'\b' + word + r'\b', regex=True)]
                if not direct_word_matches.empty:
                    word_matches.extend(direct_word_matches['Feature'].tolist())
            if word_matches:
                matched_features.extend(list(set(word_matches)))
                notes.append("Word match found")
    
    status = "Done" if matched_features else "Error"
    if not matched_features:
        notes.append("No mapping found")
    
    return matched_features, "|".join(notes), status

def process_file(input_file, output_file, feature_db_file):
    try:
        feature_db = pd.read_excel(feature_db_file)
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        results = []
        # Process first line (headers)
        if lines:
            headers = lines[0].strip().split('\t')
            # Keep only first 3 columns and add aggregated columns
            headers = headers[:3]
            headers.extend(['Original_Texts', 'All_Mapped_Features', 'All_Notes', 'Avg_Similarity', 'Overall_Status'])
            results.append('\t'.join(headers))
        
        # Process remaining lines
        for line in lines[1:]:  # Start from second line
            line = line.strip()
            if not line:  # Skip empty lines
                continue
                
            # Split line into cells (tab-separated)
            cells = line.split('\t')
            if len(cells) >= 1:
                # Keep first 3 columns as is
                processed_cells = cells[:3]
                
                # Initialize collectors for all cells in the line
                all_original_texts = []
                all_features = set()  # Using set to avoid duplicates
                all_notes = set()     # Using set to avoid duplicates
                similarity_scores = []
                all_statuses = set()
                
                # Process cells from index 3 onwards
                for cell in cells[3:]:
                    if cell.strip():  # Only process non-empty cells
                        features, notes, status = map_line_to_feature(cell, feature_db)
                        
                        # Add original text
                        all_original_texts.append(cell)
                        
                        # Add features
                        if features:
                            all_features.update(features)
                        
                        # Process notes and extract similarity scores
                        if notes:
                            for note in notes.split('|'):
                                all_notes.add(note.strip())
                                if 'Similarity score:' in note:
                                    try:
                                        score = float(note.split(':')[1].strip())
                                        similarity_scores.append(score)
                                    except:
                                        pass
                        
                        all_statuses.add(status)
                
                # Calculate average similarity score
                avg_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
                
                # Add aggregated results
                processed_cells.extend([
                    ' | '.join(all_original_texts),
                    ' | '.join(sorted(all_features)),
                    ' | '.join(sorted(all_notes)),
                    f"{avg_similarity:.2f}",
                    ' | '.join(sorted(all_statuses))
                ])
                
                results.append('\t'.join(processed_cells))
        
        # Write results
        with open(output_file, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(result + '\n')
                
        return True
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return False

if __name__ == "__main__":
    input_file = "input.txt"
    output_file = "output.txt"
    feature_db_file = "feature_db.xlsx"
    
    if process_file(input_file, output_file, feature_db_file):
        print("Processing completed successfully.")
    else:
        print("Processing failed.")
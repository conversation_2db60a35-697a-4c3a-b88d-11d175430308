import os
import pandas as pd
import glob
from datetime import datetime

def combine_excel_files(input_folder, output_file=None):
    """
    Read all Excel files in the input folder, extract specified columns,
    and combine them into a single output file.
    
    Args:
        input_folder (str): Path to the folder containing Excel files
        output_file (str, optional): Path for the output Excel file. If None, creates a file on desktop.
        
    Returns:
        str: Path to the output file
    """
    # Create output file path if not provided
    if output_file is None:
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(desktop, f"Combined_Excel_{timestamp}.xlsx")
    
    # Columns to extract
    columns_to_extract = [
        'DOCUMENT', 'LATEST', 'COMPARE_STATUS', 'V_COMMENT', 
        'PART_STATUS', 'DATA_CHANGED', 'CHANGED_FET'
    ]
    
    # Find all Excel files in the input folder
    excel_files = glob.glob(os.path.join(input_folder, "*.xlsx"))
    
    if not excel_files:
        print(f"No Excel files found in {input_folder}")
        return None
    
    print(f"Found {len(excel_files)} Excel files in {input_folder}")
    
    # List to store dataframes from each file
    all_dfs = []
    
    # Process each Excel file
    for file_path in excel_files:
        file_name = os.path.basename(file_path)
        try:
            # Read the Excel file
            df = pd.read_excel(file_path)
            
            # Check if all required columns exist
            missing_columns = [col for col in columns_to_extract if col not in df.columns]
            
            if missing_columns:
                print(f"Warning: File {file_name} is missing columns: {', '.join(missing_columns)}")
                # Create missing columns with NaN values
                for col in missing_columns:
                    df[col] = pd.NA
            
            # Extract only the required columns
            df_extracted = df[columns_to_extract].copy()
            
            # Add source file information
            df_extracted['SOURCE_FILE'] = file_name
            
            # Append to the list of dataframes
            all_dfs.append(df_extracted)
            print(f"Processed: {file_name} - {len(df_extracted)} rows")
            
        except Exception as e:
            print(f"Error processing {file_name}: {str(e)}")
    
    if not all_dfs:
        print("No data could be extracted from the Excel files.")
        return None
    
    # Combine all dataframes
    combined_df = pd.concat(all_dfs, ignore_index=True)
    
    # Save the combined dataframe to an Excel file
    combined_df.to_excel(output_file, index=False)
    
    print(f"\nCombined data saved to: {output_file}")
    print(f"Total rows: {len(combined_df)}")
    
    return output_file

def main():
    """
    Main function to run the script.
    """
    # Get the folder path from the user
    print("Enter the path to the folder containing Excel files:")
    input_folder = input().strip()
    
    # Validate the folder path
    if not os.path.exists(input_folder):
        print(f"Error: Folder '{input_folder}' does not exist.")
        return
    
    # Combine the Excel files
    combine_excel_files(input_folder)

if __name__ == "__main__":
    main()

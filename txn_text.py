with open ("input.txt", "r", encoding='utf-8', errors='replace') as input_file:
    lines = input_file.readlines()

status = 'not found'

for line in lines:
  link = line.split('\t')[0]
  ext_line = line.split('\t')[5].strip()

  if "The marketing status values are defined as follows".strip().lower() in ext_line.lower().strip() \
  or "For more details on status, see our product life cycle".lower().strip() in ext_line.lower().strip() \
  or "PACKAGE MATERIALS INFORMATION".lower().strip() in ext_line.lower().strip():
     status = 'not found'

  if "PACKAGING INFORMATION".lower() in line.lower():
    status = 'found'

  if status == 'found':
    with open("output.txt", "a", encoding='utf8') as output_file:
      output_file.write(f"{link}\t{ext_line}\n")

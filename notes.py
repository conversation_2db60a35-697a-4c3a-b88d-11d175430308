import os
import pandas as pd

def combine_excel_files(folder_path):
  # Ensure the folder path exists
  if not os.path.exists(folder_path):
    print(f"Error: Folder {folder_path} does not exist.")
    return

  dfs = []
  for file in os.listdir(folder_path):
    if file.endswith('.xlsx'):
      try:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        # Add a column to track the source file
        df['Source_File'] = file
        dfs.append(df)
      except Exception as e:
        print(f"Error processing {file}: {e}")
  
  if dfs:
    combined_df = pd.concat(dfs, ignore_index=True)
    # Output to a new Excel file
    output_path = os.path.join(folder_path, 'combined_files.xlsx')
    combined_df.to_excel(output_path, index=False)
    print(f"Combined file saved to {output_path}")
    print(f"Total rows: {len(combined_df)}")
    print(f"Source files combined: {len(dfs)}")
  else:
    print("No Excel files found to combine")

# Use the correct folder path
folder_path = r'c:\D_Partition\Courses\Python_env\Map'
combine_excel_files(folder_path)

import openpyxl
import re

# Configuration - MODIFY THESE VALUES
INPUT_FILE = "input.xlsx"  # Path to your Excel file
OUTPUT_FILE = "output.xlsx"  # Path for the output file
COLUMN_TO_CLEAN = "A"  # Column letter to clean (e.g., "E" for column E)
START_ROW = 2  # First row to process (usually 2 to skip header)

def clean_text(text):
    """Apply regex cleaning patterns to text"""
    if not isinstance(text, str):
        return text
    
    # Apply the regex patterns
    x = re.sub(r'[^A-Za-z\s+]', "", text)
    x = re.sub(r'\s[a-z]\s', ' ', x)
    x = re.sub(r'\sX\s', ' ', x)
    x = re.sub(r'\s+', ' ', x).strip()
    
    return x

def process_excel_file():
    """Process Excel file, clean specified column, and write results"""
    try:
        print(f"Opening {INPUT_FILE}...")
        # Load the workbook and select the active worksheet
        wb = openpyxl.load_workbook(INPUT_FILE)
        ws = wb.active
        
        # Get the maximum row count
        max_row = ws.max_row
        print(f"Found {max_row} rows in the worksheet.")
        
        # Find the next available column to write results
        next_col_idx = openpyxl.utils.column_index_from_string(COLUMN_TO_CLEAN) + 1
        next_col_letter = openpyxl.utils.get_column_letter(next_col_idx)
        
        # Add header for the new column
        ws[f"{next_col_letter}1"] = f"Cleaned {COLUMN_TO_CLEAN}"
        
        # Process each row
        cleaned_count = 0
        for row in range(START_ROW, max_row + 1):
            cell_value = ws[f"{COLUMN_TO_CLEAN}{row}"].value
            
            if cell_value:
                cleaned_value = clean_text(cell_value)
                ws[f"{next_col_letter}{row}"] = cleaned_value
                cleaned_count += 1
        
        # Save the workbook to a new file
        wb.save(OUTPUT_FILE)
        print(f"Processing complete. Cleaned {cleaned_count} cells.")
        print(f"Results saved to {OUTPUT_FILE}")
        
    except Exception as e:
        print(f"Error processing Excel file: {e}")
        return False
    
    return True

if __name__ == "__main__":
    process_excel_file()

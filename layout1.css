:root {
	--primary: #6d28d9;
	--primary-light: #7c3aed;
	--bg: #f8f7ff;
	--sidebar-bg: #ffffff;
	--card-bg: #ffffff;
	--text: #1a1b1e;
	--text-light: #4b5563;
}

body {
	background-color: var(--bg);
	color: var(--text);
}

/* Sidebar Styles */
.sidebar {
	width: 240px;
	background: var(--sidebar-bg);
	padding: 1.5rem;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid #e5e7eb;
}

.logo i {
	font-size: 24px;
	color: var(--primary);
}

.logo span {
	font-size: 20px;
	font-weight: 600;
}

.nav-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	color: var(--text-light);
	text-decoration: none;
	border-radius: 8px;
	margin-bottom: 8px;
	transition: all 0.3s ease;
}

.nav-item:hover, .nav-item.active {
	background: var(--primary);
	color: white;
}

.nav-item i {
	font-size: 18px;
}

.sidebar-toggle {
	background: var(--primary);
	color: white;
}

/* Main Content Styles */
.main-content {
	margin-left: 240px;
	position: relative;
}

.main-content.expanded {
	margin-left: 80px;
}

/* Columns Layout */
.columns {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 2rem;
	margin-top: 1rem;
}

.column h2 {
	margin-bottom: 1.5rem;
	color: var(--primary);
}

/* Card Styles */
.card {
	background: var(--card-bg);
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
	border: 1px solid #e5e7eb;
}

.card h3 {
	color: var(--text);
	margin-bottom: 0.5rem;
}

.card p {
	color: var(--text-light);
}

/* Decorative Shapes */
.main-content::before {
	content: '';
	position: fixed;
	top: 20px;
	right: 20px;
	width: 200px;
	height: 200px;
	background: var(--primary);
	opacity: 0.1;
	border-radius: 50%;
	z-index: -1;
}

.main-content::after {
	content: '';
	position: fixed;
	bottom: 20px;
	left: 300px;
	width: 150px;
	height: 150px;
	background: var(--primary-light);
	opacity: 0.1;
	border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
	z-index: -1;
}

/* Responsive Design */
@media (max-width: 1024px) {
	.columns {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.columns {
		grid-template-columns: 1fr;
	}
}
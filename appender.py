import os

# Define the folder containing text files and the output file
input_folder = 'textfiles'
output_file = 'combined.txt'

# Open the output file in write mode
with open(output_file, 'w', encoding='utf-8') as outfile:
  # Iterate through all files in the folder
  for filename in os.listdir(input_folder):
    try:
      # Check if the file is a text file
      if filename.endswith('.txt'):
        file_path = os.path.join(input_folder, filename)
        with open(file_path, 'r', encoding='utf-8') as infile:
          # Write the content of the text file to the output file
          outfile.write(infile.read())
          outfile.write('\n')  # Add a newline between files
    except Exception as e:
      print(f"An error occurred while processing '{filename}': {e}")
print(f"All text files in '{input_folder}' have been appended into '{output_file}'.")
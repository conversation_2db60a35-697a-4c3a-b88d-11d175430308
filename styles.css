* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

:root {
	--bg-color: #1a1b1e;
	--sidebar-bg: #2c2e33;
	--card-bg: #25262b;
	--text-primary: #ffffff;
	--text-secondary: #a1a1aa;
	--accent-color: #6d28d9;
	--accent-hover: #7c3aed;
	--border-color: #2d2d2d;
	--card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	--hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}


body {
	margin: 0;
	padding: 16px;
	font-family: 'Inter', system-ui, sans-serif;
	background-color: var(--bg-color);
	color: var(--text-primary);
	line-height: 1.5;
}


.container {
	display: flex;
	gap: 16px;
	min-height: calc(100vh - 32px);
}

/* Sidebar styles */
.sidebar {
	position: fixed;
	top: 0px;
	left: 16px;
	width: 240px;
	height: calc(100vh - 104px);
	background: var(--sidebar-bg);
	color: white;
	border-radius: 16px;
	padding: 32px 24px;
	margin-top: 32px;
	z-index: 2000;
	overflow-y: auto;
	transition: transform 0.3s ease, width 0.3s ease;
}

.sidebar.collapsed {
	width: 80px;
	padding: 32px 12px;
}

.sidebar.collapsed .logo span,
.sidebar.collapsed .nav-item span {
	display: none;
}

.sidebar.collapsed .nav-item {
	padding: 12px 8px;
	justify-content: center;
}

.sidebar.collapsed .nav-item i {
	margin-right: 0;
}

.sidebar-toggle {
	position: absolute;
	top: 32px;
	right: -12px;
	width: 24px;
	height: 24px;
	background: var(--accent-color);
	border: none;
	border-radius: 50%;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2001;
	transition: transform 0.3s ease;
}

.sidebar-toggle:hover {
	background: var(--accent-hover);
}

.sidebar.collapsed .sidebar-toggle {
	transform: rotate(180deg);
}
/* Update scrollbar for dark theme */
::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-track {
	background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
	background: var(--accent-color);
	opacity: 0.5;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	opacity: 1;
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 40px;
	padding-bottom: 24px;
	border-bottom: 1px solid var(--border-color);
}

.logo i {
	font-size: 24px;
	color: var(--primary-color);
}

.logo span {
	font-size: 22px;
	font-weight: 700;
	letter-spacing: -0.5px;
}

/* Top bar styles */
.top-bar {
	position: fixed;
	top: 16px;
	left: 16px;
	right: 16px;
	z-index: 3000;
	background: rgba(255, 255, 255, 0.98);
	height: 56px;
	padding: 0 24px;
	border-radius: 12px;
	box-shadow: var(--shadow);
	backdrop-filter: blur(8px);
	-webkit-backdrop-filter: blur(8px);
	display: flex;
	justify-content: space-between;
	align-items: center;
}



/* Main content */
.main-content {
	padding: 32px;
	margin-top: 0;
	margin-left: 272px;
	width: calc(100% - 288px);
	transition: margin-left 0.3s ease, width 0.3s ease;
}

.main-content.expanded {
	margin-left: 112px;
	width: calc(100% - 128px);
}

/* Content grid */
.content-grid {
	display: flex;
	flex-direction: column;
	gap: 32px;
	height: auto;
	margin-top: 32px;
	padding: 0 16px;
}

/* Column styles */
.column {
	width: 100%;
}

/* Stats cards container */
.stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 24px;
	margin-bottom: 32px;
}

/* Header styles */
.header {
	position: relative;
	top: 0;
	right: 0;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 24px;
}


/* Stats cards */
.stat-card {
	padding: 32px;
	border-radius: 16px;
	margin-bottom: 0;
}

.stat-list {
	margin-top: 24px;
}

.stat-item {
	padding: 12px 0;
	border-bottom: 1px solid var(--border-color);
	color: var(--text-secondary);
	font-size: 14px;
}


.stat-card h3,
.detail-card h3 {
	font-size: 14px;
	font-weight: 500;
	color: var(--text-secondary);
	margin-bottom: 8px;
}

.stat-value {
	margin-top: 16px;
	font-size: 42px;
	font-weight: 700;
	color: var(--text-primary);
}

/* Detail card */
.detail-card {
	margin-top: 32px;
	padding: 32px;
	border-radius: 16px;
	height: auto;
	min-height: 300px;
}


.detail-card h3 {
	font-size: 20px;
	margin-bottom: 16px;
}


/* Navigation tabs */
.nav-tabs {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 16px;
	background: rgba(243, 244, 246, 0.98);
	padding: 6px;
	border-radius: 20px;
	backdrop-filter: blur(12px);
	-webkit-backdrop-filter: blur(12px);
	box-shadow: var(--card-shadow);
	z-index: 3100;
}

.nav-tabs a {
	color: var(--text-secondary);
	text-decoration: none;
	font-size: 14px;
	padding: 8px 20px;
	font-weight: 500;
	transition: all 0.2s ease;
	border-radius: 16px;
}

.nav-tabs a:hover, .nav-tabs a.active {
	color: var(--text-primary);
	background: white;
	box-shadow: var(--card-shadow);
}

/* Subcategory grid */
.subcategory-grid {
	display: flex;
	flex-direction: column;
	gap: 24px;
	padding: 0;
}

.subcategory-card, .stat-card, .detail-card {
	background: var(--card-bg);
	border-radius: 16px;
	padding: 32px;
	border: 1px solid var(--border-color);
	box-shadow: var(--card-shadow);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.subcategory-card {
	min-height: 180px;
	margin-bottom: 0;
	display: none;
	flex-direction: column;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
				box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.subcategory-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 12px 28px rgba(0, 0, 0, 0.25);
}





.subcategory-card i {
	font-size: 36px;
	color: var(--accent-color);
	margin-bottom: 20px;
}

.subcategory-card h3 {
	font-size: 22px;
	letter-spacing: -0.5px;
	margin-bottom: 16px;
	color: var(--text-primary);
	font-weight: 600;
}

.subcategory-card p {
	color: var(--text-secondary);
	font-size: 15px;
	line-height: 1.6;
	opacity: 0.9;
}

/* Upload form styling */
.upload-form {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: var(--card-bg);
	border-radius: 12px;
	border: 1px solid var(--border-color);
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	z-index: 4000;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 24px;
	opacity: 0;
	pointer-events: none;
	transition: all 0.3s ease;
}


.subcategory-card.expanded .upload-form {
	opacity: 1;
	pointer-events: all;
}

.file-input {
	background: var(--bg-color);
	border: 1px solid var(--border-color);
	padding: 12px;
	border-radius: 8px;
	color: var(--text-primary);
	width: 100%;
	margin-bottom: 16px;
}

.upload-btn {
	width: 100%;
	background: var(--accent-color);
	color: white;
	border: none;
	padding: 12px 28px;
	border-radius: 8px;
	font-weight: 600;
	letter-spacing: 0.5px;
	cursor: pointer;
	font-size: 16px;
	transition: all 0.2s ease;
}

.upload-btn:hover {
	background: #3182ce;
	transform: translateY(-1px);
}

/* Card hover and expansion effects */
.subcategory-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.subcategory-card.expanded {
	transform: scale(1.02) translateY(-4px);
	box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
	border-color: var(--accent-color);
}

/* Nav items */
.nav-item {
	display: flex;
	align-items: center;
	border-radius: 12px;
	padding: 14px 20px;
	margin: 8px 0;
	color: #a1a1aa;
	transition: all 0.2s ease;
	font-weight: 500;
	letter-spacing: 0.5px;
	opacity: 0.8;
	font-size: 15px;
}

.nav-item:hover, .nav-item.active {
	opacity: 1;
	background: var(--accent-color);
	color: white;
}


.nav-item i {
	width: 20px;
	margin-right: 12px;
	font-size: 20px;
	opacity: 0.9;
}

.nav-item:hover i {
	color: var(--text-primary);
}

/* User profile */
.user-profile img {
	width: 36px;
	height: 36px;
	border-radius: 50%;
	border: none;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Category headers */
.category-header {
	font-size: 18px;
	color: var(--text-primary);
	margin: 24px 0 16px;
	font-weight: 600;
	opacity: 0.9;
	border-left: none;
	padding-left: 0;
	grid-column: 1 / -1;
}


/* Show subcategories based on active category */
.compare-subcategory,
.pdf-subcategory,
.generic-subcategory,
.part-number-subcategory {
	display: none;
}

.subcategory-grid.show-compare .compare-subcategory,
.subcategory-grid.show-pdf .pdf-subcategory,
.subcategory-grid.show-generic .generic-subcategory,
.subcategory-grid.show-part-number .part-number-subcategory {
	display: flex;
}


/* Backdrop filters */
.top-bar, .sidebar, .subcategory-card {
	background: #f5f3f3;

	/* -webkit-backdrop-filter: blur(10px); */
}

.sidebar{
	background-color: #373A40;
}

.logo i, .nav-item i, .subcategory-card i {
	text-shadow: none;
}

/* Improve upload form */
.upload-form {
	background: white;
	border-radius: 8px;
}

.upload-btn {
	background: var(--accent-color);
	color: white;
	border-radius: 6px;
	padding: 10px 20px;
	border: none;
	transition: all 0.3s ease;
	font-weight: 600;
	letter-spacing: 0.01em;
	box-shadow: 0 2px 8px rgba(79, 70, 229, 0.2);
}

.upload-btn:hover {
	background: var(--accent-hover);
	box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
	transform: translateY(-2px);
}


/* Adjust grid layout */
.subcategory-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
	gap: 20px;
	padding: 16px;
}

/* Add hover effects */
.subcategory-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 24px rgba(79, 70, 229, 0.12);
	border: 1px solid var(--accent-color);
}

/* Fix nav tabs */
.nav-tabs a {
	color: var(--text-secondary);
	transition: all 0.3s ease;
}

.nav-tabs a.active {
	background: white;
	color: var(--accent-color);
	font-weight: 600;
	box-shadow: var(--card-shadow);
}

/* Fix scrollbar */
.subcategory-grid::-webkit-scrollbar,
.detail-card::-webkit-scrollbar {
	width: 4px;
}

.subcategory-grid::-webkit-scrollbar-thumb,
.detail-card::-webkit-scrollbar-thumb {
	background: #e5e7eb;
	border-radius: 4px;
}


/* Fix scrollable areas */
.subcategory-grid, .detail-card {
	overflow-y: auto;
	scrollbar-width: thin;
	scrollbar-color: #d1d5db transparent;
}






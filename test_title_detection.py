#!/usr/bin/env python3
"""
Test script for title detection functionality.
Demonstrates how to use the TitleDetector class with various criteria.
"""

from font_size import TitleDetector
import os

def test_title_criteria():
    """Test individual title validation criteria."""
    print("="*80)
    print("TESTING TITLE VALIDATION CRITERIA")
    print("="*80)
    
    # Create a dummy detector for testing criteria
    class TestDetector:
        def __init__(self):
            self.major_font_size = 12.0
        
        def clean_text_for_validation(self, text):
            return ''.join(char for char in text if char.isalpha())
        
        def has_sufficient_alphabetic_chars(self, text):
            cleaned_text = self.clean_text_for_validation(text)
            return len(cleaned_text) >= 6
        
        def has_valid_word_count(self, text):
            words = text.split()
            return len(words) <= 5
        
        def has_invalid_patterns(self, text):
            import re
            # Check for 6 consecutive numbers
            if re.search(r'\d{6}', text):
                return True
            
            # Check for alphanumeric mix in the same word
            words = text.split()
            for word in words:
                clean_word = re.sub(r'[^a-zA-Z0-9]', '', word)
                if clean_word and re.search(r'[a-zA-Z]', clean_word) and re.search(r'\d', clean_word):
                    return True
            return False
        
        def is_valid_title_font_size(self, font_size):
            min_title_size = self.major_font_size + 2
            max_title_size = self.major_font_size * 2
            return min_title_size <= font_size < max_title_size
    
    detector = TestDetector()
    
    # Test cases
    test_cases = [
        # (text, font_size, expected_result, reason)
        ("Introduction", 15.0, True, "Valid title"),
        ("Chapter One", 16.0, True, "Valid title with two words"),
        ("System Overview", 14.5, True, "Valid title"),
        ("ABC", 15.0, False, "Too few alphabetic characters (3 < 6)"),
        ("This is a very long title with many words", 15.0, False, "Too many words (> 5)"),
        ("Section 123456", 15.0, False, "Contains 6 consecutive numbers"),
        ("Config2024", 15.0, False, "Alphanumeric mix in same word"),
        ("User Guide", 12.0, False, "Font size too small (not > major + 2)"),
        ("Main Title", 25.0, False, "Font size too large (>= double major)"),
        ("Overview!", 15.0, True, "Valid with punctuation"),
        ("Test123 Guide", 15.0, False, "Alphanumeric mix in first word"),
        ("Section A", 15.0, True, "Valid short title"),
    ]
    
    print(f"Major font size: {detector.major_font_size}")
    print(f"Valid title font size range: {detector.major_font_size + 2} to {detector.major_font_size * 2}")
    print("\nTest Results:")
    print("-" * 80)
    
    for i, (text, font_size, expected, reason) in enumerate(test_cases, 1):
        # Test font size
        font_valid = detector.is_valid_title_font_size(font_size)
        
        # Test content criteria
        alpha_valid = detector.has_sufficient_alphabetic_chars(text)
        word_valid = detector.has_valid_word_count(text)
        pattern_valid = not detector.has_invalid_patterns(text)
        
        # Overall validity
        is_valid = font_valid and alpha_valid and word_valid and pattern_valid
        
        # Status
        status = "✓" if is_valid == expected else "✗"
        
        print(f"{i:2d}. [{status}] '{text}' (Font: {font_size})")
        print(f"    Expected: {expected} | Actual: {is_valid} | {reason}")
        print(f"    Font: {font_valid} | Alpha: {alpha_valid} | Words: {word_valid} | Pattern: {pattern_valid}")
        print("-" * 80)

def test_with_sample_pdf():
    """Test with the actual PDF if available."""
    pdf_path = r"\\10.199.104.160\pdfs2\2024\11\21\17\25\57\681854\gmts_\manual\gx7936ds.pdf"
    
    print("\n" + "="*80)
    print("TESTING WITH ACTUAL PDF")
    print("="*80)
    
    try:
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f"PDF file not found: {pdf_path}")
            print("Please update the path or ensure the file is accessible.")
            return
        
        # Initialize detector
        detector = TitleDetector(pdf_path)
        
        # Analyze document
        title_candidates = detector.analyze_document()
        
        # Show detailed analysis
        if title_candidates:
            print(f"\nDetailed Title Analysis:")
            print("-" * 80)
            
            for i, candidate in enumerate(title_candidates[:10], 1):  # Show first 10
                print(f"{i:2d}. '{candidate['text']}'")
                print(f"    Page: {candidate['page_num'] + 1}")
                print(f"    Font Size: {candidate['font_size']:.1f}")
                print(f"    Appears on {candidate['frequency']} page(s)")
                
                # Show validation details
                alpha_chars = len(''.join(c for c in candidate['text'] if c.isalpha()))
                word_count = len(candidate['text'].split())
                print(f"    Alphabetic chars: {alpha_chars} | Words: {word_count}")
                print("-" * 80)
        
        # Test finding closest title for some lines
        print(f"\nTesting closest title detection:")
        print("-" * 80)
        
        # Test with a few sample lines
        sample_indices = [5, 15, 25, 35] if len(detector.all_text_data) > 35 else [0, 1, 2, 3]
        
        for idx in sample_indices:
            if idx < len(detector.all_text_data):
                line_data = detector.all_text_data[idx]
                closest_title = detector.find_closest_title_to_line(line_data)
                
                print(f"Line {idx + 1}: '{line_data['text'][:50]}...'")
                if closest_title:
                    print(f"  → Closest title: '{closest_title['text']}'")
                    print(f"    (Page {closest_title['page_num'] + 1}, Font: {closest_title['font_size']:.1f})")
                else:
                    print(f"  → No suitable title found")
                print("-" * 40)
        
        # Close detector
        detector.close()
        
    except Exception as e:
        print(f"Error testing with PDF: {e}")
        print("This might be due to file access issues or PyMuPDF not being installed.")

def demonstrate_frequency_filtering():
    """Demonstrate how frequency filtering works."""
    print("\n" + "="*80)
    print("FREQUENCY FILTERING DEMONSTRATION")
    print("="*80)
    
    print("Frequency filtering rules:")
    print("- For documents with 5+ pages:")
    print("  • Titles appearing on ≥50% of pages are ignored")
    print("- For documents with <5 pages:")
    print("  • No frequency filtering applied")
    print()
    
    # Example scenarios
    scenarios = [
        (3, {"Header": 2, "Title": 1, "Footer": 3}, "3-page document"),
        (10, {"Header": 8, "Chapter": 2, "Footer": 10}, "10-page document"),
        (6, {"Introduction": 1, "Page": 4, "Section": 2}, "6-page document"),
    ]
    
    for page_count, title_frequencies, description in scenarios:
        print(f"{description} ({page_count} pages):")
        threshold = page_count * 0.5 if page_count >= 5 else float('inf')
        
        for title, frequency in title_frequencies.items():
            should_ignore = frequency >= threshold if page_count >= 5 else False
            status = "IGNORED" if should_ignore else "ACCEPTED"
            print(f"  '{title}': appears on {frequency} pages → {status}")
        print()

if __name__ == "__main__":
    print("Title Detection Testing Suite")
    print("="*80)
    
    # Run tests
    test_title_criteria()
    demonstrate_frequency_filtering()
    test_with_sample_pdf()
    
    print("\n" + "="*80)
    print("Testing completed!")
    print("="*80)
    
    print("\nTitle Detection Criteria Summary:")
    print("✓ Font size: major_font + 2 ≤ title_font < major_font × 2")
    print("✓ Alphabetic characters: ≥ 6 (after removing non-alphabetic)")
    print("✓ Word count: ≤ 5 words")
    print("✓ No 6 consecutive numbers")
    print("✓ No alphanumeric mix within same word")
    print("✓ Frequency: ignore if appears on ≥50% pages (5+ page docs)")
    print("✓ Position: closest title above the target line")

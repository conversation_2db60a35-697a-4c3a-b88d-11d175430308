import pandas as pd
import re
import pdfplumber
import re
import concurrent.futures
import re
import re
import concurrent.futures
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from multiprocessing import freeze_support

def clean_text(text):
  if text is None:
    return ""
  pattern = re.compile(r"-\n")
  text = re.sub(pattern, "", text)
  text = re.sub('\s+', " ", text)
  text =text.strip()
  return text


def refromat_table(table):
  new_table = []
  for row in table.values:
    new_row = [clean_text(cell) for cell in list(row)]
    new_table.append(new_row)
  new_table = pd.DataFrame(new_table)
  return new_table


def main(row):
  link = row.split("\t")[0].strip()
  keywords = row.split("\t")[-1].strip().split("|")
  try:
    doc = pdfplumber.open(link)
    part_list = []
    found_keywords = []
    keyword_part_dict = {}
    for page in doc.pages:
        for table in page.extract_tables():
          print(table)
          new_table = refromat_table(pd.DataFrame(table))
          for keyword in keywords:
            direction_coulmn_index = new_table.eq(keyword.lower()).stack().idxmax()[1] if keyword.lower() in [x.lower() for x in new_table.values.flatten().tolist()] else None
            if direction_coulmn_index is not None:
              if keyword not in keyword_part_dict:
                  keyword_part_dict[keyword] = ''
              part_list.extend(list(new_table[direction_coulmn_index]))
              keyword_part_dict[keyword]= "|".join(list(new_table[direction_coulmn_index]))
              found_keywords.append(keyword)

    keywords_pattern = f"({'|'.join([keyword for keyword in keywords])})"

    text_part_list = "|".join(list(set(part_list)))
    text_part_list = re.sub(re.compile(keywords_pattern, re.IGNORECASE), "", text_part_list)
    text_part_list = re.sub(r'\|+', '|', text_part_list)           # Collapse multiple pipes
    text_part_list = text_part_list.strip('|')                     # Remove leading/trailing pipes
  
    return "\t".join([link,"|".join(list(set(found_keywords))),text_part_list])

  except Exception as e:
         return "\t".join([link,str(e)])

   
if __name__ == '__main__':
    with open("input.txt", "r") as input_file:
        links_list = input_file.readlines()
    with open("output.txt", "w") as output_file:
        output_file.write("Document\tFound Keywords\tPart List")
        output_file.write("\n")

    freeze_support()
    console = Console()
    title = '''# BLOCK MATCHER'''
    my_copyright = '''# © <EMAIL>'''
    title = Markdown(title)
    my_copyright = Markdown(my_copyright)
    console.print(title)
    console.print(my_copyright)
    one_time_count = 500
    total_rows = len(links_list)

    with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
            ncols=100) as progress_bar:

        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    try:
                        with open("output.txt", 'a', encoding='utf8') as of:
                            of.write(result)
                            of.write('\n')
                    except:
                        pass
                    progress_bar.update(1)
        progress_bar.set_description(f"done".upper())

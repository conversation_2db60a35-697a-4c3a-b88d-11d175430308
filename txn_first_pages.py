import fitz
import re
import concurrent.futures
import re
from multiprocessing import freeze_support
import pandas as pd
from itertools import repeat
import fitz
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from datetime import date

class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page

    def __eq__(self, other):
        return (self.word_text == other.word_text and self.word_start == other.word_start
                and self.word_end == other.word_end and self.word_up == other.word_up)

    def __hash__(self):
        return hash((self.word_text, self.word_start, self.word_end, self.word_up))

    @property
    def word_coordinate(self):
        return self.word_up + self.word_down


class Line:
    def __init__(self, line_words):
        self.line_words = line_words

    @property
    def full_line(self):
        return " ".join([word.word_text for word in self.line_words])

    @property
    def line_up(self):
        return min([word.word_up for word in self.line_words])

    @property
    def line_down(self):
        return max([word.word_down for word in self.line_words])

    @property
    def line_start(self):
        return min([word.word_start for word in self.line_words])

    @property
    def line_end(self):
        return max([word.word_end for word in self.line_words])

    @property
    def line_page(self):
        return self.line_words[0].word_page
    

def fitz_extract_all_words(link):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4
    list_of_lines = []

    link = link.removesuffix('\n')
    doc_1 = fitz.open(link)

    for page_index, page in enumerate(doc_1):
        page_list_of_lines = []
        word_properties_list = [
            Word(w[text_index], w[start_index], w[end_index], w[up_index], w[down_index], page_index)
            for w in page.get_text("words")]

        line_list_of_words = []
        found_words_list = []

        for fixed_word in word_properties_list:
            if fixed_word in found_words_list:
                continue
            for looping_word in word_properties_list:
                if (looping_word.word_up - 4 <= fixed_word.word_up <= looping_word.word_up + 4
                        or looping_word.word_down - 4 <= fixed_word.word_down <= looping_word.word_down + 4):
                    line_list_of_words.append(looping_word)
                    found_words_list.append(looping_word)

            line_list_of_words = list(set(line_list_of_words))
            line_list_of_words.sort(key=lambda x: x.word_start)
            page_list_of_lines.append(
                Line(line_list_of_words))
            line_list_of_words = []
        page_list_of_lines.sort(key=lambda x: x.line_up)

        list_of_lines.extend(page_list_of_lines)

    return list_of_lines


link1 = r"\\**************\pdfs2\2022\8\7\1\50\14\326768\txn_\manual\getliterature.pdf"
link2 = r"\\**************\pdfs2\2025\5\15\19\34\20\453841110\txn_\manual\getliterature.pdf"

def main(links):
  try:
    link1 = links.split("\t")[0].strip()
    link2 = links.split("\t")[-1].strip()

    full_text1 = "\n".join(line.full_line for line in fitz_extract_all_words(link1))
    first_text1 = full_text1.split("PACKAGE OPTION ADDENDUM")[0]
    first_text1 = re.sub(r"(\n.*Copyright ©.*\n|\n.*www.ti.com.*\n|\n.*Product Folder Links.*\n)","\n", first_text1)

    full_text2 = "\n".join(line.full_line for line in fitz_extract_all_words(link2))
    first_text2 = full_text2.split("PACKAGE OPTION ADDENDUM")[0]
    first_text2 = re.sub(r"(\n.*Copyright ©.*\n|\n.*www.ti.com.*\n|\n.*Product Folder Links.*\n)","\n", first_text2)


    if first_text1.lower().strip() == first_text2.lower().strip():
      return links.strip() + "\t"+ "Equal"
    else:
        return links.strip() + "\t"+"Not Equal"
    
  except Exception as E:
      return links.strip() + "\t"+ str(E)
  


if __name__ == '__main__':

    with open("input.txt", "r") as input_file:
        links_list = input_file.readlines()

    with open("output.txt", "w") as output_file:
        output_file.write("Document\tDoc1_Path\tDoc2_Path\tCombined_Path\tPosition_Stats\tText_Stats")
        output_file.write("\n")

    freeze_support()
    console = Console()
    title = '''# BLOCK MATCHER'''
    my_copyright = '''# © <EMAIL>'''
    title = Markdown(title)
    my_copyright = Markdown(my_copyright)
    console.print(title)
    console.print(my_copyright)
    one_time_count = 500
    total_rows = len(links_list)

    with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
            ncols=100) as progress_bar:

        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    try:
                        with open("output.txt", 'a', encoding='utf8') as of:
                            of.write(result)
                            of.write('\n')
                    except:
                        pass
                    progress_bar.update(1)
        progress_bar.set_description(f"done".upper())
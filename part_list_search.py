import re
import fitz
import concurrent.futures
from itertools import repeat
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown


class input_part:
    # class variable
    count_of_parts = 0

    def __init__(self, original_part, searched_text):
        self.original_part = original_part
        self.searched_text = searched_text

    def __str__(self):
        return self.original_part

    @property
    def no_space_part(self):
        return self.original_part.replace(" ", "")

    @property
    def no_sc_to_space_part(self):
        return re.sub(re.compile(r"[^\w\s]"), " ", self.original_part)

    @property
    def no_sc_part(self):
        return re.sub(re.compile(r"[^\w\s]"), "", self.original_part)

    @property
    def no_space_no_sc_part(self):
        return re.sub(re.compile(r"[\W]"), "", self.original_part)

    def find_part(self):
        # find exact
        if self.original_part.lower() in "|".join(
                re.findall(re.compile(fr'\s*([^\s]*{re.escape(self.original_part)}[^\s]*)\s*', re.IGNORECASE),
                           self.searched_text)).lower().split("|"):
            return ['FOUND EXACT', self.original_part]

        elif self.no_space_part.lower() in "|".join(
                re.findall(re.compile(fr'\s*([^\s]*{re.escape(self.no_space_part)}[^\s]*)\s*', re.IGNORECASE),
                           self.searched_text)).lower().split("|"):
            return ['FOUND DIFFERENT FORMAT', f"{self.original_part} ~ {self.no_space_part}"]

        else:
            for element_part in re.findall(
                    re.compile(fr'\s*([^\s]*{re.escape(self.original_part)}[^\s]*)\s*', re.IGNORECASE),
                    self.searched_text):
                if self.original_part.lower() == re.sub(re.compile(r"[():,]"), "", element_part).lower():
                    return ['FOUND EXACT', self.original_part]

                elif self.original_part.lower().replace(" ", "") == re.sub(re.compile(r"[():,\s]"), "",
                                                                           element_part).lower():
                    return ['FOUND EXACT', self.original_part]

        func_list = [self.no_sc_part, self.no_sc_to_space_part, self.no_space_no_sc_part]

        all_found = re.findall(re.compile(fr'\s*([^\s]*{re.escape(self.original_part)}[^\s]*)\s*', re.IGNORECASE),
                               self.searched_text)
        all_found.sort(key=len)

        if self.original_part.lower() not in "|".join(all_found).lower().split("|") and all_found != []:
          return ['FOUND NON ALPHA', all_found[0] + f" [+] {re.sub(re.escape(self.original_part), '', all_found[0], flags=re.IGNORECASE)}"]

        for func in func_list:
            for element_func in re.findall(re.compile(fr'\s*([^\s]*{re.escape(func)}[^\s]*)\s*', re.IGNORECASE),
                                           self.searched_text):
                if func.lower() == re.sub(re.compile(r"[():,]"), "", element_func).lower():
                    return ['FOUND DIFFERENT FORMAT', f"{self.original_part} ~ {element_func}"]

        return ['NOT FOUND', self.original_part]


def extract_all_text(link):
    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))
    all_text = "\n".join([page.get_text() for page in doc_1])
    return all_text.replace("\n", " "), doc_1.page_count


def main(two_links):
    """Assign link values"""
    link1 = two_links.split('\t')[0].removesuffix('\n')
    part = two_links.split('\t')[1].removesuffix('\n')
    two_links = two_links.removesuffix('\n')
    result_row = []

    try:
        '''Extract all text from document'''
        parts_status = {'FOUND EXACT':[], 'FOUND DIFFERENT FORMAT':[],'FOUND NON ALPHA':[], 'NOT FOUND':[]}
        full_status = []
        doc_1_all_text_1, page_count_1 = extract_all_text(link1)

        for part in part.split("|"):
          my_part = input_part(part, doc_1_all_text_1)

          if len(doc_1_all_text_1) / page_count_1 < 200:
              comment_2_1 = "unsearchable"
          else:
              comment_2_1 = "searchable"

          [status, extracted_part]= my_part.find_part()

          parts_status[status].append(extracted_part)
        
        for key in parts_status.keys():
          full_status.append("|".join(parts_status[key]))

         
        result_row.append(two_links + '\t' + 'Done' + '\t' + comment_2_1)
        result_row.append(str(len(doc_1_all_text_1)))
        result_row.append(str(page_count_1))
        result_row.append("\t".join(full_status))


        return '\t'.join(result_row)

    except Exception as E:
        result_row = [two_links + '\t' + 'Error', str(E)]
        return '\t'.join(result_row)


if __name__ == '__main__':

    console = Console()

    title = '''# KEYWORD SEARCH
    > this tool is designed for the following purposes:
        1- specifying if the keywords given are found in the document or not.
        2- notify user if the document is suspected unsearchable.

    '''
    title = Markdown(title)
    console.print(title)

    print()

    # read links
    with open("input.txt", 'r') as f:
        links_list = f.readlines()

    with open('output_1.txt', 'w') as of:
        of.write('DOCUMENT\tPARTS\tSTATUS\tCOMMENT\tCHAR_LEN\tPAGE_COUNT\tFOUND EXACT\tFOUND DIFFERENT FORMAT\tFOUND NON ALPHA\tNOT FOUND')
        of.write('\n')

    one_time_count = 500

    with tqdm(total=len(links_list), desc=f"Processing".upper(), unit="row", ncols=100) as progress_bar:
        with concurrent.futures.ProcessPoolExecutor(max_workers=5) as executor:
            # pass links to function
            for i in range(0, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor.map(main, batch_links)
                for result in results:
                    with open('output_1.txt', 'a', encoding='utf8') as of:
                        of.write(result)
                        of.write('\n')
                    progress_bar.update(1)

        progress_bar.set_description(f"done".upper())

# Language Detection Script

A comprehensive Python script for detecting languages in text files line by line. This tool can analyze mixed-language content and provide detailed results showing the original text and detected language for each line.

## Features

- **Line-by-line analysis**: Processes each line individually for accurate detection
- **Multi-language support**: Supports 55+ languages including English, Spanish, French, German, Italian, Russian, Japanese, Korean, Chinese, Arabic, and many more
- **Confidence scoring**: Provides confidence levels for each detection
- **Flexible output**: Console display and file output options
- **Error handling**: Robust handling of empty lines, non-linguistic content, and encoding issues
- **Summary statistics**: Shows language distribution across the file
- **Customizable settings**: Adjustable confidence thresholds and display options

## Installation

1. **Install the required library**:
   ```bash
   pip install langdetect
   ```
   
   Or install from the requirements file:
   ```bash
   pip install -r requirements_language_detection.txt
   ```

## Usage

### Command Line Usage

Basic usage:
```bash
python language_detector.py input_file.txt
```

With options:
```bash
python language_detector.py input_file.txt -o results.txt -c 0.8 --max-length 60
```

### Command Line Options

- `input_file`: Path to the input text file (required)
- `-o, --output`: Path to save results to a file (optional)
- `-c, --confidence`: Minimum confidence threshold (default: 0.7)
- `--no-confidence`: Hide confidence scores in output
- `--max-length`: Maximum text length to display (default: 80)

### Programmatic Usage

```python
from language_detector import LanguageDetector

# Initialize detector
detector = LanguageDetector(confidence_threshold=0.7)

# Analyze a file
results = detector.analyze_file('your_text_file.txt')

# Display results
detector.print_results(results)

# Save results to file
detector.save_results_to_file(results, 'output.txt')

# Detect language of individual text
lang_code, lang_name, confidence = detector.detect_language("Hello, how are you?")
print(f"Language: {lang_name} (confidence: {confidence:.2f})")
```

## Examples

Run the example script to see the tool in action:
```bash
python example_usage.py
```

This will create a sample multilingual text file and demonstrate various usage patterns.

## Supported Languages

The script supports detection of 55+ languages including:

- **European**: English, Spanish, French, German, Italian, Portuguese, Russian, Polish, Dutch, Swedish, Danish, Norwegian, Finnish, Czech, Slovak, Hungarian, Romanian, Bulgarian, Croatian, Serbian, Slovenian, Estonian, Latvian, Lithuanian, Ukrainian, Belarusian, Macedonian, Albanian, Catalan, Basque, Galician, Welsh, Irish, Maltese, Icelandic
- **Asian**: Japanese, Korean, Chinese (Simplified/Traditional), Thai, Vietnamese, Hindi, Bengali, Tamil, Telugu, Malayalam, Kannada, Gujarati, Punjabi, Nepali, Sinhala, Myanmar, Khmer, Lao, Indonesian, Malay, Filipino
- **Middle Eastern/African**: Arabic, Persian, Urdu, Hebrew, Georgian, Amharic, Swahili, Zulu, Afrikaans

## Output Format

The script provides detailed output showing:
- Line number
- Confidence indicator (✓ for high confidence, ? for medium, ! for low)
- Detected language name
- Confidence score
- Original text (truncated if too long)
- Summary statistics with language distribution

## Error Handling

The script handles various edge cases:
- Empty lines
- Lines with only numbers or punctuation
- Very short text (less than 3 characters)
- File encoding issues (tries UTF-8 first, then Latin-1)
- Non-existent files
- Language detection failures

## Confidence Levels

- **High confidence** (≥ threshold): Marked with ✓
- **Medium confidence** (≥ 0.5): Marked with ?
- **Low confidence** (< 0.5): Marked with !

## Tips for Best Results

1. **Text length**: Longer text generally provides more accurate detection
2. **Mixed content**: Lines with mixed languages may be detected as the dominant language
3. **Confidence threshold**: Adjust based on your accuracy requirements
4. **Encoding**: Ensure your text files are properly encoded (UTF-8 recommended)

## Troubleshooting

### Installation Issues
If you encounter installation issues with langdetect:
```bash
pip install --upgrade pip
pip install langdetect
```

### Encoding Issues
If you get encoding errors, try:
1. Save your text file as UTF-8
2. The script automatically tries Latin-1 encoding as fallback

### Low Accuracy
For better accuracy:
1. Ensure text lines have sufficient content (more than a few words)
2. Adjust confidence threshold
3. Check for mixed-language content in individual lines

## License

This script is provided as-is for educational and practical use.

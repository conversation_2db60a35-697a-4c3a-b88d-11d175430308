#!/usr/bin/env python3
"""
Simple script to generate output.txt with lines and their corresponding titles.
Customize the PDF path below and run this script.
"""

from font_size import TitleDetector
import os
import sys

def main():
    """Main function to process PDF and generate output."""
    
    # ========================================
    # CUSTOMIZE THIS PATH TO YOUR PDF FILE
    # ========================================
    pdf_path = r"\\**************\pdfs2\2024\11\21\17\25\57\681854\gmts_\manual\gx7936ds.pdf"
    
    # You can also use a relative path like:
    # pdf_path = "your_document.pdf"
    
    # Or get path from command line argument:
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    print("PDF Title Detection and Output Generation")
    print("=" * 50)
    print(f"Processing: {pdf_path}")
    
    # Check if file exists
    if not os.path.exists(pdf_path):
        print(f"❌ Error: PDF file not found at: {pdf_path}")
        print("\nPlease:")
        print("1. Update the pdf_path variable in this script, or")
        print("2. Run with: python generate_title_output.py 'path/to/your/file.pdf'")
        return False
    
    try:
        # Initialize title detector
        print("\n📖 Initializing PDF title detector...")
        detector = TitleDetector(pdf_path)
        
        # Analyze the document
        print("🔍 Analyzing document structure...")
        title_candidates = detector.analyze_document()
        
        # Generate output file
        print("📝 Generating output file...")
        results = detector.generate_output_file("output.txt")
        
        if results:
            print(f"\n✅ SUCCESS!")
            print(f"   📄 Processed {len(results)} lines")
            print(f"   📋 Found {len(title_candidates)} title candidates")
            print(f"   💾 Output saved to 'output.txt'")
            
            # Show statistics
            lines_with_titles = sum(1 for r in results if r['title'] != "No Title Found")
            coverage = (lines_with_titles / len(results)) * 100 if results else 0
            
            print(f"\n📊 Statistics:")
            print(f"   • Lines with titles: {lines_with_titles}")
            print(f"   • Lines without titles: {len(results) - lines_with_titles}")
            print(f"   • Title coverage: {coverage:.1f}%")
            
            # Show sample results
            print(f"\n📋 Sample Results (first 3 lines):")
            print("-" * 60)
            for i, result in enumerate(results[:3]):
                line_preview = result['line_text'][:40] + "..." if len(result['line_text']) > 40 else result['line_text']
                print(f"Line {result['line_number']:3d}: {line_preview}")
                print(f"         Title: {result['title']}")
                print()
            
            if len(results) > 3:
                print(f"... and {len(results) - 3} more lines in output.txt")
        
        else:
            print("❌ Failed to generate output file")
            return False
        
        # Close the document
        detector.close()
        
        print(f"\n🎉 Complete! Check 'output.txt' for full results.")
        return True
        
    except ImportError:
        print("❌ Error: PyMuPDF library not found")
        print("Please install it with: pip install PyMuPDF")
        return False
    except Exception as e:
        print(f"❌ Error processing PDF: {e}")
        return False

def show_output_format():
    """Show what the output format looks like."""
    print("\n📋 Output Format Preview:")
    print("=" * 50)
    print("""
Line   1 | Page  1 | Font:  12.0
Title: Introduction
Text:  This document provides an overview of the system...
--------------------------------------------------------------------------------
Line   2 | Page  1 | Font:  12.0
Title: Introduction
Text:  The main components include the following modules...
--------------------------------------------------------------------------------
Line   3 | Page  1 | Font:  14.0
Title: No Title Found
Text:  System Requirements
--------------------------------------------------------------------------------
    """)

if __name__ == "__main__":
    print("🔧 PDF Title Detection Tool")
    print("=" * 50)
    
    # Show help if requested
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("Usage:")
        print("  python generate_title_output.py                    # Use default path")
        print("  python generate_title_output.py 'path/to/file.pdf' # Use custom path")
        print()
        show_output_format()
        sys.exit(0)
    
    # Run main function
    success = main()
    
    if success:
        print("\n✨ Done! Your output.txt file is ready.")
    else:
        print("\n💡 Tip: Make sure your PDF path is correct and PyMuPDF is installed.")
        print("   Install with: pip install PyMuPDF")

{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pdfplumber\n", "import pandas as pd\n", "doc = pdfplumber.open(r\"aaa8000c83 (1).pdf\")\n", "tables = []\n", "for page in doc.pages:\n", "    for table in page.extract_tables():\n", "      tables.append(pd.DataFrame(table))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "      <th>16</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Characteristics list</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Rated\\nvoltage\\n(V)</td>\n", "      <td>Rated\\ntemper-\\nature\\n(℃)</td>\n", "      <td>Cate-\\ngory\\nvoltage\\n(V)</td>\n", "      <td>Cate-\\ngory\\ntemper-\\nature\\n(℃)</td>\n", "      <td>Rated\\ncapaci-\\ntance\\n(μF)</td>\n", "      <td>Case size\\n（ｍｍ）</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Size\\ncode</td>\n", "      <td>Specifications</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Standard</td>\n", "      <td>None</td>\n", "      <td>Floor life\\nlevel</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>L</td>\n", "      <td>W</td>\n", "      <td>H</td>\n", "      <td>None</td>\n", "      <td>Ripple*1\\ncurrent\\n(mA rms)</td>\n", "      <td>ESR*2\\n(mΩ max.)</td>\n", "      <td>tan δ*3</td>\n", "      <td>LC*4\\n(μA)</td>\n", "      <td>Part number</td>\n", "      <td>Min.\\npackaging\\nq'ty\\n(pcs)</td>\n", "      <td>Reflow\\ntemp\\n≦260℃</td>\n", "      <td>Reflow\\ntemp\\n≦250℃</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>12.8</td>\n", "      <td>125</td>\n", "      <td>33</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>B2</td>\n", "      <td>1000</td>\n", "      <td>90</td>\n", "      <td>0.10</td>\n", "      <td>158.4</td>\n", "      <td>16TDC33MYFB</td>\n", "      <td>2000</td>\n", "      <td>-</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>12.8</td>\n", "      <td>125</td>\n", "      <td>None</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>None</td>\n", "      <td>1000</td>\n", "      <td>90</td>\n", "      <td>0.10</td>\n", "      <td>158.4</td>\n", "      <td>16TDC33MB2</td>\n", "      <td>2000</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>12.8</td>\n", "      <td>125</td>\n", "      <td>100</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>1.9</td>\n", "      <td>D2</td>\n", "      <td>1800</td>\n", "      <td>50</td>\n", "      <td>0.10</td>\n", "      <td>160.0</td>\n", "      <td>16TDC100MYF</td>\n", "      <td>3000</td>\n", "      <td>-</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>12.8</td>\n", "      <td>125</td>\n", "      <td>150</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>2.8</td>\n", "      <td>D3L</td>\n", "      <td>1800</td>\n", "      <td>50</td>\n", "      <td>0.10</td>\n", "      <td>240.0</td>\n", "      <td>16TDC150MYF</td>\n", "      <td>2500</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>12.8</td>\n", "      <td>125</td>\n", "      <td>220</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>2.8</td>\n", "      <td>None</td>\n", "      <td>1800</td>\n", "      <td>50</td>\n", "      <td>0.10</td>\n", "      <td>240.0</td>\n", "      <td>16TDC220MD3</td>\n", "      <td>2500</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>16</td>\n", "      <td>125</td>\n", "      <td>22</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>B2</td>\n", "      <td>1000</td>\n", "      <td>90</td>\n", "      <td>0.10</td>\n", "      <td>132.0</td>\n", "      <td>20TDC22MYFB</td>\n", "      <td>2000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>16</td>\n", "      <td>125</td>\n", "      <td>None</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>None</td>\n", "      <td>1000</td>\n", "      <td>90</td>\n", "      <td>0.10</td>\n", "      <td>132.0</td>\n", "      <td>20TDC22MB2</td>\n", "      <td>2000</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>16</td>\n", "      <td>125</td>\n", "      <td>100</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>2.8</td>\n", "      <td>D3L</td>\n", "      <td>1700</td>\n", "      <td>55</td>\n", "      <td>0.10</td>\n", "      <td>200.0</td>\n", "      <td>20TDC100MYF</td>\n", "      <td>2500</td>\n", "      <td>-</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>15</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>B2</td>\n", "      <td>900</td>\n", "      <td>100</td>\n", "      <td>0.10</td>\n", "      <td>112.5</td>\n", "      <td>25TDC15MYFB</td>\n", "      <td>2000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>None</td>\n", "      <td>3.5</td>\n", "      <td>2.8</td>\n", "      <td>1.9</td>\n", "      <td>None</td>\n", "      <td>900</td>\n", "      <td>100</td>\n", "      <td>0.10</td>\n", "      <td>112.5</td>\n", "      <td>25TDC15MB</td>\n", "      <td>2000</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>68</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>2.8</td>\n", "      <td>D3L</td>\n", "      <td>1400</td>\n", "      <td>70</td>\n", "      <td>0.10</td>\n", "      <td>170.0</td>\n", "      <td>25TDC68MYF</td>\n", "      <td>2500</td>\n", "      <td>-</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>None</td>\n", "      <td>105</td>\n", "      <td>20</td>\n", "      <td>125</td>\n", "      <td>100</td>\n", "      <td>7.3</td>\n", "      <td>4.3</td>\n", "      <td>2.8</td>\n", "      <td>None</td>\n", "      <td>1600</td>\n", "      <td>60</td>\n", "      <td>0.10</td>\n", "      <td>250.0</td>\n", "      <td>25TDC100MD3</td>\n", "      <td>2500</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      0                           1   \\\n", "0   Characteristics list                        None   \n", "1    Rated\\nvoltage\\n(V)  Rated\\ntemper-\\nature\\n(℃)   \n", "2                   None                        None   \n", "3                   None                         105   \n", "4                   None                         105   \n", "5                   None                         105   \n", "6                   None                         105   \n", "7                   None                         105   \n", "8                   None                         105   \n", "9                   None                         105   \n", "10                  None                         105   \n", "11                  None                         105   \n", "12                  None                         105   \n", "13                  None                         105   \n", "14                  None                         105   \n", "\n", "                           2                                 3   \\\n", "0                        None                              None   \n", "1   Cate-\\ngory\\nvoltage\\n(V)  Cate-\\ngory\\ntemper-\\nature\\n(℃)   \n", "2                        None                              None   \n", "3                        12.8                               125   \n", "4                        12.8                               125   \n", "5                        12.8                               125   \n", "6                        12.8                               125   \n", "7                        12.8                               125   \n", "8                          16                               125   \n", "9                          16                               125   \n", "10                         16                               125   \n", "11                         20                               125   \n", "12                         20                               125   \n", "13                         20                               125   \n", "14                         20                               125   \n", "\n", "                             4                5     6     7           8   \\\n", "0                          None             None  None  None        None   \n", "1   Rated\\ncapaci-\\ntance\\n(μF)  Case size\\n（ｍｍ）  None  None  Size\\ncode   \n", "2                          None                L     W     H        None   \n", "3                            33              3.5   2.8   1.9          B2   \n", "4                          None              3.5   2.8   1.9        None   \n", "5                           100              7.3   4.3   1.9          D2   \n", "6                           150              7.3   4.3   2.8         D3L   \n", "7                           220              7.3   4.3   2.8        None   \n", "8                            22              3.5   2.8   1.9          B2   \n", "9                          None              3.5   2.8   1.9        None   \n", "10                          100              7.3   4.3   2.8         D3L   \n", "11                           15              3.5   2.8   1.9          B2   \n", "12                         None              3.5   2.8   1.9        None   \n", "13                           68              7.3   4.3   2.8         D3L   \n", "14                          100              7.3   4.3   2.8        None   \n", "\n", "                             9                 10       11          12  \\\n", "0                          None              None     None        None   \n", "1                Specifications              None     None        None   \n", "2   Ripple*1\\ncurrent\\n(mA rms)  ESR*2\\n(mΩ max.)  tan δ*3  LC*4\\n(μA)   \n", "3                          1000                90     0.10       158.4   \n", "4                          1000                90     0.10       158.4   \n", "5                          1800                50     0.10       160.0   \n", "6                          1800                50     0.10       240.0   \n", "7                          1800                50     0.10       240.0   \n", "8                          1000                90     0.10       132.0   \n", "9                          1000                90     0.10       132.0   \n", "10                         1700                55     0.10       200.0   \n", "11                          900               100     0.10       112.5   \n", "12                          900               100     0.10       112.5   \n", "13                         1400                70     0.10       170.0   \n", "14                         1600                60     0.10       250.0   \n", "\n", "             13                            14                   15  \\\n", "0          None                          None                 None   \n", "1      Standard                          None    Floor life\\nlevel   \n", "2   Part number  Min.\\npackaging\\nq'ty\\n(pcs)  Reflow\\ntemp\\n≦260℃   \n", "3   16TDC33MYFB                          2000                    -   \n", "4    16TDC33MB2                          2000                    3   \n", "5   16TDC100MYF                          3000                    -   \n", "6   16TDC150MYF                          2500                 None   \n", "7   16TDC220MD3                          2500                 None   \n", "8   20TDC22MYFB                          2000                 None   \n", "9    20TDC22MB2                          2000                    3   \n", "10  20TDC100MYF                          2500                    -   \n", "11  25TDC15MYFB                          2000                 None   \n", "12    25TDC15MB                          2000                    3   \n", "13   25TDC68MYF                          2500                    -   \n", "14  25TDC100MD3                          2500                 None   \n", "\n", "                     16  \n", "0                  None  \n", "1                  None  \n", "2   Reflow\\ntemp\\n≦250℃  \n", "3                  None  \n", "4                  None  \n", "5                  None  \n", "6                  None  \n", "7                  None  \n", "8                  None  \n", "9                  None  \n", "10                 None  \n", "11                 None  \n", "12                 None  \n", "13                 None  \n", "14                 None  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["my_table = tables[10]\n", "my_table"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["import re\n", "def clean_text(text):\n", "  if text is None:\n", "    return \"\"\n", "  pattern = re.compile(r\"-\\n\")\n", "  text = re.sub(pattern, \"\", text)\n", "  text = re.sub('\\s+', \" \", text)\n", "  text =text.strip()\n", "  text =text.lower()\n", "  return text"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["new_table = []\n", "for row in my_table.values:\n", "  new_row = [clean_text(cell) for cell in list(row)]\n", "  new_table.append(new_row)\n", "\n", "new_table = pd.DataFrame(new_table)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["0                \n", "1        standard\n", "2     part number\n", "3     16tdc33myfb\n", "4      16tdc33mb2\n", "5     16tdc100myf\n", "6     16tdc150myf\n", "7     16tdc220md3\n", "8     20tdc22myfb\n", "9      20tdc22mb2\n", "10    20tdc100myf\n", "11    25tdc15myfb\n", "12      25tdc15mb\n", "13     25tdc68myf\n", "14    25tdc100md3\n", "Name: 13, dtype: object"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["direction_coulmn_index = new_table.eq('part number').stack().idxmax()[1] if 'part number' in new_table.values else None\n", "new_table[direction_coulmn_index]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}
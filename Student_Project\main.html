<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قاعدة بيانات الطلاب</title>
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: rgb(155, 207, 95);
            --border-color: #ccc;
            --bg-color: #f1f1f1;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-family: 'Cairo', Arial, sans-serif;
            font-family: 'IBM Plex Sans Arabic', Arial, sans-serif;
            /* font-family: 'Almarai', Arial, sans-serif; */
            margin: 0;
            padding: 20px;
            direction: rtl;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        #studentTable {
            width: 100%;
            background-color: white;
            border-collapse: collapse;
            margin-top: 20px;
        }

        #studentTable th {
            background-color: var(--primary-color);
            color: black;
            padding: 12px;
            text-align: right;
            font-weight: 600;
        }

        #studentTable td {
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        #studentTable tbody tr:hover {
            background-color: var(--bg-color);
        }

        .dataTables_wrapper .dataTables_filter {
            text-align: left;
            margin-bottom: 20px;
        }

        .dataTables_wrapper .dataTables_filter input {
            margin-right: 10px;
            padding: 5px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>قاعدة بيانات الطلاب</h1>
        <table id="studentTable">
            <thead>
                <tr>
                    <th>الرقم الجامعي</th>
                    <th>الاسم</th>
                    <th>التخصص</th>
                    <th>المستوى</th>
                    <th>مادة ١</th>
                    <th>مادة ٢</th>
                    <th>مادة ٣</th>
                    <th>مادة ٤</th>
                    <th>مادة ٥</th>
                    <th>المعدل</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>١٢٣٤٥٦</td>
                    <td>علي</td>
                    <td>علوم الحاسوب</td>
                    <td>المستوى الثالث</td>
                    <td>كيمياء</td>
                    <td>فيزياء</td>
                    <td>حاسب</td>
                    <td>رياضيات</td>
                    <td>لغة</td>
                    <td>٣.٥</td>
                </tr>
                <tr>
                    <td>١٢٣٤٥٧</td>
                    <td>محمد</td>
                    <td>هندسة البرمجيات</td>
                    <td>المستوى الثاني</td>
                    <td>كيمياء</td>
                    <td>فيزياء</td>
                    <td>حاسب</td>
                    <td>رياضيات</td>
                    <td>لغة</td>
                    <td>٣.٠</td>
                </tr>
                <tr>
                    <td>١٢٣٤٥٨</td>
                    <td>سارة</td>
                    <td>علوم الحاسوب</td>
                    <td>المستوى الرابع</td>
                    <td>كيمياء</td>
                    <td>فيزياء</td>
                    <td>حاسب</td>
                    <td>رياضيات</td>
                    <td>لغة</td>
                    <td>٣.٨</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#studentTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                order: [[1, 'asc']],
                pageLength: 10,
                responsive: true,
                columnDefs: [
                    {
                        targets: [0, 9],
                        render: function(data, type, row) {
                            if (type === 'sort') {
                                return data.replace(/[٠١٢٣٤٥٦٧٨٩]/g, function(d) {
                                    return d.charCodeAt(0) - 1632;
                                });
                            }
                            return data;
                        }
                    }
                ]
            });
        });
    </script>
</body>
</html>
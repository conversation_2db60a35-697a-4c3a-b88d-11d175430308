<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Green Forest - Layout 2</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<link rel="stylesheet" href="shared-styles.css">
	<link rel="stylesheet" href="layout2.css">
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-leaf"></i>
				<span>Green Forest</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-tree"></i>
					<span>Overview</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-seedling"></i>
					<span>Growth</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-cloud-sun"></i>
					<span>Weather</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Forest Health</h2>
					<div class="card animate-fade-in">
						<h3>Air Quality</h3>
						<p>Excellent conditions today</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Biodiversity</h3>
						<p>Species count increasing</p>
					</div>
				</section>

				<section class="column">
					<h2>Conservation</h2>
					<div class="card animate-fade-in">
						<h3>Protected Areas</h3>
						<p>New zone established</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Restoration</h3>
						<p>500 trees planted</p>
					</div>
				</section>

				<section class="column">
					<h2>Resources</h2>
					<div class="card animate-fade-in">
						<h3>Water Levels</h3>
						<p>Above average for season</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Soil Quality</h3>
						<p>Optimal conditions</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
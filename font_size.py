import fitz # PyMuPDF

doc = fitz.open(r"\\10.199.104.160\pdfs2\2024\11\21\17\25\57\681854\gmts_\manual\gx7936ds.pdf")
font_size_list = []
for page_num in range(doc.page_count):
    page = doc.load_page(page_num)
    # Extract text in a dictionary format to get detailed info
    text_blocks = page.get_text("dict")["blocks"]

    for block in text_blocks:
        if "lines" in block:
            for line in block["lines"]:
                # Each line contains spans, which hold font information
                for span in line["spans"]:
                    font_size = span["size"]
                    text = span["text"]
                    font_size_list.append(font_size)
                    print(f"Line: '{text}', Font Size: {font_size}")
print(max(font_size_list))  
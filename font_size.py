import fitz # PyMuPDF
from collections import Counter
import re
from typing import List, Dict, Tuple, Set

class TitleDetector:
    """Class to detect titles in PDF documents based on font size and content criteria."""

    def __init__(self, pdf_path: str):
        """Initialize the title detector with a PDF document."""
        self.doc = fitz.open(pdf_path)
        self.page_count = self.doc.page_count
        self.major_font_size = None
        self.all_text_data = []  # Store all text with metadata
        self.title_candidates = []

    def extract_all_text_data(self):
        """Extract all text with font size and position information."""
        font_size_list = []

        for page_num in range(self.page_count):
            page = self.doc.load_page(page_num)
            text_blocks = page.get_text("dict")["blocks"]

            for block_idx, block in enumerate(text_blocks):
                if "lines" in block:
                    for line_idx, line in enumerate(block["lines"]):
                        for span_idx, span in enumerate(line["spans"]):
                            font_size = span["size"]
                            text = span["text"].strip()

                            if text:  # Only process non-empty text
                                font_size_list.append(font_size)

                                # Store text data with metadata
                                text_data = {
                                    'text': text,
                                    'font_size': font_size,
                                    'page_num': page_num,
                                    'block_idx': block_idx,
                                    'line_idx': line_idx,
                                    'span_idx': span_idx,
                                    'bbox': span.get('bbox', None)  # Bounding box for position
                                }
                                self.all_text_data.append(text_data)

        # Determine major font size (most common)
        if font_size_list:
            self.major_font_size = Counter(font_size_list).most_common(1)[0][0]

        return self.all_text_data

    def is_valid_title_font_size(self, font_size: float) -> bool:
        """Check if font size meets title criteria."""
        if self.major_font_size is None:
            return False

        # Title font size should be greater than major font size by 2
        # and less than double the major font size
        min_title_size = self.major_font_size + 2
        max_title_size = self.major_font_size * 2

        return min_title_size <= font_size < max_title_size

    def clean_text_for_validation(self, text: str) -> str:
        """Remove non-alphabetic characters for validation."""
        return ''.join(char for char in text if char.isalpha())

    def has_sufficient_alphabetic_chars(self, text: str) -> bool:
        """Check if text has at least 6 alphabetic characters."""
        cleaned_text = self.clean_text_for_validation(text)
        return len(cleaned_text) >= 6

    def has_valid_word_count(self, text: str) -> bool:
        """Check if text has 5 words or fewer."""
        words = text.split()
        return len(words) <= 5

    def has_invalid_patterns(self, text: str) -> bool:
        """Check for invalid patterns: 6 consecutive numbers or alphanumeric mix in same word."""
        # Check for 6 consecutive numbers
        if re.search(r'\d{6}', text):
            return True

        # Check for alphanumeric mix in the same word
        words = text.split()
        for word in words:
            # Remove non-alphanumeric characters for this check
            clean_word = re.sub(r'[^a-zA-Z0-9]', '', word)
            if clean_word and re.search(r'[a-zA-Z]', clean_word) and re.search(r'\d', clean_word):
                return True

        return False

    def is_valid_title_content(self, text: str) -> bool:
        """Validate title content based on all criteria."""
        # Check alphabetic character count
        if not self.has_sufficient_alphabetic_chars(text):
            return False

        # Check word count
        if not self.has_valid_word_count(text):
            return False

        # Check for invalid patterns
        if self.has_invalid_patterns(text):
            return False

        return True

    def get_title_frequency_across_pages(self) -> Dict[str, int]:
        """Count how many pages each potential title appears on."""
        title_page_count = {}

        for text_data in self.all_text_data:
            text = text_data['text']
            font_size = text_data['font_size']
            page_num = text_data['page_num']

            # Only consider text with valid title font size
            if self.is_valid_title_font_size(font_size):
                if text not in title_page_count:
                    title_page_count[text] = set()
                title_page_count[text].add(page_num)

        # Convert sets to counts
        return {text: len(pages) for text, pages in title_page_count.items()}

    def should_ignore_frequent_title(self, text: str, frequency: int) -> bool:
        """Check if title should be ignored due to high frequency."""
        # Only apply frequency rule for documents with 5+ pages
        if self.page_count < 5:
            return False

        # Ignore titles that appear on 50% or more of pages
        frequency_threshold = self.page_count * 0.5
        return frequency >= frequency_threshold

    def find_title_candidates(self):
        """Find all potential title candidates."""
        if not self.all_text_data:
            self.extract_all_text_data()

        # Get title frequencies
        title_frequencies = self.get_title_frequency_across_pages()

        candidates = []

        for text_data in self.all_text_data:
            text = text_data['text']
            font_size = text_data['font_size']

            # Check font size criteria
            if not self.is_valid_title_font_size(font_size):
                continue

            # Check content criteria
            if not self.is_valid_title_content(text):
                continue

            # Check frequency criteria
            frequency = title_frequencies.get(text, 0)
            if self.should_ignore_frequent_title(text, frequency):
                continue

            # Add to candidates
            candidate = {
                'text': text,
                'font_size': font_size,
                'page_num': text_data['page_num'],
                'bbox': text_data['bbox'],
                'frequency': frequency
            }
            candidates.append(candidate)

        self.title_candidates = candidates
        return candidates

    def find_closest_title_to_line(self, target_line_data: Dict, max_distance: float = 100) -> Dict:
        """Find the closest title to a given line of text."""
        if not self.title_candidates:
            self.find_title_candidates()

        target_page = target_line_data['page_num']
        target_bbox = target_line_data.get('bbox')

        if not target_bbox:
            return None

        target_y = target_bbox[1]  # Top y-coordinate
        closest_title = None
        min_distance = float('inf')

        for candidate in self.title_candidates:
            # Only consider titles on the same page or nearby pages
            if abs(candidate['page_num'] - target_page) > 1:
                continue

            candidate_bbox = candidate.get('bbox')
            if not candidate_bbox:
                continue

            candidate_y = candidate_bbox[1]  # Top y-coordinate

            # Calculate distance (considering page difference)
            page_penalty = abs(candidate['page_num'] - target_page) * 1000  # Heavy penalty for different pages
            y_distance = abs(target_y - candidate_y)
            total_distance = y_distance + page_penalty

            # Title should be above the target line (smaller y-coordinate in PDF)
            if candidate['page_num'] == target_page and candidate_y > target_y:
                continue  # Skip titles below the target line on same page

            if total_distance < min_distance and total_distance <= max_distance:
                min_distance = total_distance
                closest_title = candidate

        return closest_title

    def analyze_document(self):
        """Perform complete document analysis."""
        print(f"Analyzing PDF document with {self.page_count} pages...")

        # Extract all text data
        self.extract_all_text_data()
        print(f"Major font size: {self.major_font_size}")

        # Find title candidates
        candidates = self.find_title_candidates()
        print(f"Found {len(candidates)} title candidates")

        # Display title candidates
        if candidates:
            print("\nTitle Candidates:")
            print("-" * 80)
            for i, candidate in enumerate(candidates, 1):
                print(f"{i:2d}. Page {candidate['page_num'] + 1}: '{candidate['text']}'")
                print(f"    Font Size: {candidate['font_size']:.1f} | Frequency: {candidate['frequency']} pages")
                print("-" * 80)

        return candidates

    def generate_output_file(self, output_filename: str = "output.txt"):
        """Generate output file with each line and its corresponding title."""
        print(f"\nGenerating output file: {output_filename}")

        # Ensure we have all data
        if not self.all_text_data:
            self.extract_all_text_data()

        if not self.title_candidates:
            self.find_title_candidates()

        # Process each line and find its corresponding title
        results = []

        for i, line_data in enumerate(self.all_text_data):
            line_text = line_data['text'].strip()

            # Skip empty lines
            if not line_text:
                continue

            # Find closest title for this line
            closest_title = self.find_closest_title_to_line(line_data)

            # Prepare result
            result = {
                'line_number': i + 1,
                'page_number': line_data['page_num'] + 1,
                'line_text': line_text,
                'title': closest_title['text'] if closest_title else "No Title Found",
                'title_font_size': closest_title['font_size'] if closest_title else None,
                'line_font_size': line_data['font_size']
            }

            results.append(result)

        # Write to output file
        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                # Write header as requested
                f.write("line\ttitle\tpage\tline font size\ttitle font size\n")

                # Write each line with its title in tab-separated format
                for result in results:
                    line_number = result['line_number']
                    title = result['title']
                    page_number = result['page_number']
                    line_font_size = result['line_font_size']
                    title_font_size = result['title_font_size'] if result['title_font_size'] is not None else ""

                    # Write tab-separated values
                    f.write(f"{line_number}\t{title}\t{page_number}\t{line_font_size}\t{title_font_size}\n")


            print(f"✓ Output file generated successfully: {output_filename}")
            print(f"  - Processed {len(results)} lines")
            print(f"  - Found titles for {sum(1 for r in results if r['title'] != 'No Title Found')} lines")

            return results

        except Exception as e:
            print(f"Error writing output file: {e}")
            return None

    def close(self):
        """Close the PDF document."""
        if self.doc:
            self.doc.close()


# Example usage
if __name__ == "__main__":
    pdf_path = r"\\10.199.104.160\pdfs2\2024\11\21\17\25\57\681854\gmts_\manual\gx7936ds.pdf"

    try:
        # Initialize title detector
        print("Initializing PDF title detector...")
        detector = TitleDetector(pdf_path)

        # Analyze the document
        title_candidates = detector.analyze_document()

        # Generate output file with all lines and their corresponding titles
        results = detector.generate_output_file("output.txt")

        if results:
            print(f"\n✓ Successfully processed {len(results)} lines")
            print("✓ Output saved to 'output.txt'")

            # Show a few examples
            print(f"\nFirst 5 examples:")
            print("-" * 60)
            for i, result in enumerate(results[:5]):
                print(f"Line {result['line_number']}: '{result['line_text'][:50]}...'")
                print(f"  → Title: '{result['title']}'")
                print()

        # Close the document
        detector.close()

    except FileNotFoundError:
        print(f"Error: PDF file not found at: {pdf_path}")
        print("Please update the path to point to your PDF file.")
    except Exception as e:
        print(f"Error processing PDF: {e}")
        print("Make sure PyMuPDF (fitz) is installed: pip install PyMuPDF")
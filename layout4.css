:root {
	--primary: #f97316;
	--primary-light: #fb923c;
	--bg: #18181b;
	--sidebar-bg: #27272a;
	--card-bg: #27272a;
	--text: #fafafa;
	--text-light: #a1a1aa;
}

body {
	background-color: var(--bg);
	color: var(--text);
}

/* Sidebar Styles */
.sidebar {
	width: 240px;
	background: var(--sidebar-bg);
	padding: 1.5rem;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
	border-right: 1px solid #3f3f46;
}

.logo {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid #3f3f46;
}

.logo i {
	font-size: 24px;
	color: var(--primary);
	animation: glow 2s ease-in-out infinite;
}

.logo span {
	font-size: 20px;
	font-weight: 600;
	color: var(--text);
}

.nav-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	color: var(--text-light);
	text-decoration: none;
	border-radius: 8px;
	margin-bottom: 8px;
	transition: all 0.3s ease;
}

.nav-item:hover, .nav-item.active {
	background: var(--primary);
	color: var(--text);
}

.nav-item i {
	font-size: 18px;
}

.sidebar-toggle {
	background: var(--primary);
	color: var(--text);
}

/* Main Content Styles */
.main-content {
	margin-left: 240px;
	position: relative;
	padding: 2rem;
}

.main-content.expanded {
	margin-left: 80px;
}

/* Columns Layout */
.columns {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 2rem;
	margin-top: 1rem;
}

.column h2 {
	margin-bottom: 1.5rem;
	color: var(--primary);
	font-weight: 600;
}

/* Card Styles */
.card {
	background: var(--card-bg);
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
	border: 1px solid #3f3f46;
	border-radius: 12px;
	padding: 1.5rem;
	position: relative;
	overflow: hidden;
}

.card::after {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 60px;
	height: 60px;
	background: var(--primary);
	opacity: 0.1;
	clip-path: polygon(100% 0, 0 0, 100% 100%);
	transition: all 0.3s ease;
}

.card:hover::after {
	width: 80px;
	height: 80px;
	opacity: 0.2;
}

.card h3 {
	color: var(--text);
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.card p {
	color: var(--text-light);
}

/* Decorative Shapes */
.main-content::before {
	content: '';
	position: fixed;
	top: 40px;
	right: 40px;
	width: 200px;
	height: 200px;
	background: var(--primary);
	opacity: 0.1;
	clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
	animation: rotate 20s linear infinite;
}

.main-content::after {
	content: '';
	position: fixed;
	bottom: 40px;
	left: 340px;
	width: 150px;
	height: 150px;
	background: var(--primary-light);
	opacity: 0.1;
	clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
	animation: float 10s ease-in-out infinite;
}

/* Animations */
@keyframes glow {
	0%, 100% { filter: brightness(1); }
	50% { filter: brightness(1.3); }
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes float {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-20px); }
}

/* Responsive Design */
@media (max-width: 1024px) {
	.columns {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.columns {
		grid-template-columns: 1fr;
	}
}
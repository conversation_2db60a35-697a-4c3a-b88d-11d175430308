#!/usr/bin/env python3
"""
Language Detection Script
Analyzes each line of a text file to detect and identify the language.
Supports multiple languages and provides clear output with original text and detected language.
"""

import os
import sys
import re
from typing import List, Tuple, Optional
import argparse
from pathlib import Path

try:
    from langdetect import detect, detect_langs, DetectorFactory
    from langdetect.lang_detect_exception import LangDetectException
except ImportError:
    print("Error: langdetect library is not installed.")
    print("Please install it using: pip install langdetect")
    sys.exit(1)

# Set seed for consistent results
DetectorFactory.seed = 0

class LanguageDetector:
    """Class to handle language detection for text files."""

    def __init__(self, confidence_threshold: float = 0.7):
        """
        Initialize the language detector.

        Args:
            confidence_threshold: Minimum confidence level for language detection
        """
        self.confidence_threshold = confidence_threshold

        # Only support 5 specific languages
        self.supported_languages = {
            'en': 'English',
            'zh-cn': 'Chinese',
            'zh': 'Chinese',  # Alternative Chinese code
            'de': 'German',
            'fr': 'French',
            'es': 'Spanish'
        }

        # All possible language names for reference (kept for completeness)
        self.all_language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh-cn': 'Chinese (Simplified)',
            'zh-tw': 'Chinese (Traditional)',
            'zh': 'Chinese',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'th': 'Thai',
            'vi': 'Vietnamese',
            'tr': 'Turkish',
            'pl': 'Polish',
            'nl': 'Dutch',
            'sv': 'Swedish',
            'da': 'Danish',
            'no': 'Norwegian',
            'fi': 'Finnish',
            'cs': 'Czech',
            'sk': 'Slovak',
            'hu': 'Hungarian',
            'ro': 'Romanian',
            'bg': 'Bulgarian',
            'hr': 'Croatian',
            'sr': 'Serbian',
            'sl': 'Slovenian',
            'et': 'Estonian',
            'lv': 'Latvian',
            'lt': 'Lithuanian',
            'uk': 'Ukrainian',
            'be': 'Belarusian',
            'mk': 'Macedonian',
            'sq': 'Albanian',
            'ca': 'Catalan',
            'eu': 'Basque',
            'gl': 'Galician',
            'cy': 'Welsh',
            'ga': 'Irish',
            'mt': 'Maltese',
            'is': 'Icelandic',
            'fa': 'Persian',
            'ur': 'Urdu',
            'bn': 'Bengali',
            'ta': 'Tamil',
            'te': 'Telugu',
            'ml': 'Malayalam',
            'kn': 'Kannada',
            'gu': 'Gujarati',
            'pa': 'Punjabi',
            'ne': 'Nepali',
            'si': 'Sinhala',
            'my': 'Myanmar',
            'km': 'Khmer',
            'lo': 'Lao',
            'ka': 'Georgian',
            'am': 'Amharic',
            'sw': 'Swahili',
            'zu': 'Zulu',
            'af': 'Afrikaans',
            'he': 'Hebrew',
            'id': 'Indonesian',
            'ms': 'Malay',
            'tl': 'Filipino'
        }
    
    def detect_language(self, text: str) -> Tuple[str, str, float]:
        """
        Detect the language of a given text.

        Args:
            text: The text to analyze

        Returns:
            Tuple of (language_code, language_name, confidence)
        """
        # Clean and prepare text
        original_text = text.strip()

        # Skip empty lines
        if not original_text:
            return 'empty', 'Empty Line', 1.0

        # Remove all non-alphabetic characters (keep only letters and spaces)
        # Use a more comprehensive approach to keep only alphabetic characters
        cleaned_text = ''.join(char if char.isalpha() or char.isspace() else ' ' for char in original_text)
        # Remove extra spaces and strip
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

        # Skip if no alphabetic content remains after cleaning
        if not cleaned_text or len(cleaned_text) < 3:
            return 'other', 'Non-linguistic content', 1.0

        try:
            # Get detailed language detection with probabilities using cleaned text
            lang_probs = detect_langs(cleaned_text)

            if lang_probs:
                # Look for supported languages in order of confidence
                for lang_prob in lang_probs:
                    lang_code = lang_prob.lang
                    confidence = lang_prob.prob

                    # Check if this is one of our supported languages
                    if lang_code in self.supported_languages:
                        lang_name = self.supported_languages[lang_code]
                        return lang_code, lang_name, confidence

                # If no supported language found, return as unsupported
                top_lang = lang_probs[0]
                detected_name = self.all_language_names.get(top_lang.lang, f'Unknown ({top_lang.lang})')
                return 'unsupported', f'Unsupported Language ({detected_name})', top_lang.prob
            else:
                return 'unknown', 'Unknown', 0.0

        except LangDetectException:
            return 'error', 'Detection Error', 0.0
    
    def analyze_file(self, file_path: str) -> List[Tuple[int, str, str, str, float]]:
        """
        Analyze a text file line by line for language detection.
        
        Args:
            file_path: Path to the text file
            
        Returns:
            List of tuples: (line_number, original_text, lang_code, lang_name, confidence)
        """
        results = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    original_text = line.rstrip('\n\r')
                    lang_code, lang_name, confidence = self.detect_language(original_text)
                    results.append((line_num, original_text, lang_code, lang_name, confidence))
                    print(f"Line {line_num}: {lang_name} (confidence: {confidence:.2f})")
        
        except FileNotFoundError:
            print(f"Error: File '{file_path}' not found.")
            return []
        except UnicodeDecodeError:
            print(f"Error: Unable to decode file '{file_path}'. Trying with different encoding...")
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    for line_num, line in enumerate(file, 1):
                        original_text = line.rstrip('\n\r')
                        lang_code, lang_name, confidence = self.detect_language(original_text)
                        results.append((line_num, original_text, lang_code, lang_name, confidence))
            except Exception as e:
                print(f"Error reading file: {e}")
                return []
        except Exception as e:
            print(f"Error processing file: {e}")
            return []
        
        return results
    
    def print_results(self, results: List[Tuple[int, str, str, str, float]], 
                     show_confidence: bool = True, max_text_length: int = 80):
        """
        Print the language detection results in a formatted way.
        
        Args:
            results: List of detection results
            show_confidence: Whether to show confidence scores
            max_text_length: Maximum length of text to display
        """
        if not results:
            print("No results to display.")
            return
        
        print("\n" + "="*100)
        print("LANGUAGE DETECTION RESULTS")
        print("="*100)
        
        # Group results by language for summary
        lang_counts = {}
        
        for line_num, text, lang_code, lang_name, confidence in results:
            # Truncate long text for display
            display_text = text if len(text) <= max_text_length else text[:max_text_length-3] + "..."
            
            # Format confidence
            conf_str = f" (confidence: {confidence:.2f})" if show_confidence else ""
            
            # Color coding for confidence levels (if terminal supports it)
            if confidence >= self.confidence_threshold:
                status = "✓"
            elif confidence >= 0.5:
                status = "?"
            else:
                status = "!"
            
            print(f"Line {line_num:3d}: [{status}] {lang_name}{conf_str}")
            print(f"         Text: {display_text}")
            print("-" * 100)
            
            # Count languages for summary
            lang_counts[lang_name] = lang_counts.get(lang_name, 0) + 1
        
        # Print summary
        print("\nSUMMARY:")
        print("-" * 50)
        total_lines = len(results)
        for lang_name, count in sorted(lang_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_lines) * 100
            print(f"{lang_name}: {count} lines ({percentage:.1f}%)")
        print(f"\nTotal lines analyzed: {total_lines}")
    
    def save_results_to_file(self, results: List[Tuple[int, str, str, str, float]], 
                           output_file: str):
        """
        Save the detection results to a file.
        
        Args:
            results: List of detection results
            output_file: Path to output file
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("Language Detection Results\n")
                f.write("="*50 + "\n\n")
                
                for line_num, text, lang_code, lang_name, confidence in results:
                    f.write(f"Line {line_num}: {lang_name} (confidence: {confidence:.2f})\n")
                    f.write(f"Text: {text}\n")
                    f.write("-" * 50 + "\n")
                
                # Summary
                lang_counts = {}
                for _, _, _, lang_name, _ in results:
                    lang_counts[lang_name] = lang_counts.get(lang_name, 0) + 1
                
                f.write("\nSummary:\n")
                total_lines = len(results)
                for lang_name, count in sorted(lang_counts.items(), key=lambda x: x[1], reverse=True):
                    percentage = (count / total_lines) * 100
                    f.write(f"{lang_name}: {count} lines ({percentage:.1f}%)\n")
                f.write(f"\nTotal lines analyzed: {total_lines}\n")
            
            print(f"\nResults saved to: {output_file}")
        
        except Exception as e:
            print(f"Error saving results to file: {e}")


def main():
    """Main function to run the language detection script."""
    parser = argparse.ArgumentParser(description="Detect languages in text files line by line")
    parser.add_argument("input_file", help="Path to the input text file")
    parser.add_argument("-o", "--output", help="Path to save results (optional)")
    parser.add_argument("-c", "--confidence", type=float, default=0.7,
                       help="Minimum confidence threshold (default: 0.7)")
    parser.add_argument("--no-confidence", action="store_true",
                       help="Hide confidence scores in output")
    parser.add_argument("--max-length", type=int, default=80,
                       help="Maximum text length to display (default: 80)")
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' does not exist.")
        sys.exit(1)
    
    # Initialize detector
    detector = LanguageDetector(confidence_threshold=args.confidence)
    
    print(f"Analyzing file: {args.input_file}")
    print(f"Confidence threshold: {args.confidence}")
    
    # Analyze the file
    results = detector.analyze_file(args.input_file)
    
    if not results:
        print("No results obtained. Please check the file and try again.")
        sys.exit(1)
    
    # Display results
    detector.print_results(results, 
                          show_confidence=not args.no_confidence,
                          max_text_length=args.max_length)
    
    # Save results if output file specified
    if args.output:
        detector.save_results_to_file(results, args.output)


if __name__ == "__main__":
    main()

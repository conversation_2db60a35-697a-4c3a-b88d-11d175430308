#!/usr/bin/env python3
"""
Test script for the 5-language detection feature.
Tests detection of English, Chinese, German, French, and Spanish only.
"""

from language_detector import LanguageDetector

def create_five_language_test_file():
    """Create a test file with content in the 5 supported languages plus some others."""
    test_content = """Hello, how are you today? This is an English sentence.
Bonjour, comment allez-vous? Ceci est une phrase française.
Ho<PERSON>, ¿cómo estás? Esta es una oración en español.
Guten Tag, wie geht es Ihnen? Das ist ein deutscher Satz.
你好，你好吗？这是一个中文句子。
こんにちは、元気ですか？これは日本語の文です。
Ciao, come stai? Questa è una frase italiana.
Привет, как дела? Это русское предложение.
안녕하세요, 어떻게 지내세요? 이것은 한국어 문장입니다.
مرحبا، كيف حالك؟ هذه جملة عربية.
Hello123World!@# This456is789English!!!
Bonjour, ça va bien! €50 coûte cher...
¡Hola! Tel: +34-123-456-789 ¿Cómo estás?
Guten Tag! Email: <EMAIL> Website: www.test.de
你好！价格：¥99.99 时间：14:30
English with numbers: I have 5 cats and 3 dogs.
French with symbols: J'ai 10€ dans ma poche!
Spanish with punctuation: ¡Tengo 25 años!
German with mixed content: Ich bin 30 Jahre alt.
Chinese with numbers: 我有3个苹果和5个橙子。"""
    
    with open('five_languages_test.txt', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("Created test file: five_languages_test.txt")
    return 'five_languages_test.txt'

def test_supported_languages():
    """Test individual sentences in the 5 supported languages."""
    detector = LanguageDetector()
    
    test_sentences = [
        # English
        ("Hello, how are you today? This is an English sentence.", "English"),
        ("I love programming in Python and JavaScript.", "English"),
        ("The weather is beautiful today, isn't it?", "English"),
        
        # French
        ("Bonjour, comment allez-vous? Ceci est une phrase française.", "French"),
        ("J'aime beaucoup la programmation et les langages.", "French"),
        ("Il fait très beau aujourd'hui, n'est-ce pas?", "French"),
        
        # Spanish
        ("Hola, ¿cómo estás? Esta es una oración en español.", "Spanish"),
        ("Me gusta mucho la programación y los idiomas.", "Spanish"),
        ("Hace muy buen tiempo hoy, ¿no te parece?", "Spanish"),
        
        # German
        ("Guten Tag, wie geht es Ihnen? Das ist ein deutscher Satz.", "German"),
        ("Ich liebe Programmierung und verschiedene Sprachen.", "German"),
        ("Das Wetter ist heute sehr schön, nicht wahr?", "German"),
        
        # Chinese
        ("你好，你好吗？这是一个中文句子。", "Chinese"),
        ("我喜欢编程和学习不同的语言。", "Chinese"),
        ("今天天气很好，不是吗？", "Chinese"),
    ]
    
    print("\n" + "="*80)
    print("TESTING SUPPORTED LANGUAGES (English, French, Spanish, German, Chinese)")
    print("="*80)
    
    for i, (sentence, expected) in enumerate(test_sentences, 1):
        lang_code, lang_name, confidence = detector.detect_language(sentence)
        
        # Check if detection matches expected
        status = "✓" if expected.lower() in lang_name.lower() else "✗"
        
        print(f"{i:2d}. [{status}] Expected: {expected} | Detected: {lang_name} (confidence: {confidence:.2f})")
        print(f"    Text: {sentence}")
        print("-" * 80)

def test_unsupported_languages():
    """Test sentences in languages that should be marked as unsupported."""
    detector = LanguageDetector()
    
    unsupported_sentences = [
        ("こんにちは、元気ですか？これは日本語の文です。", "Japanese"),
        ("Ciao, come stai? Questa è una frase italiana.", "Italian"),
        ("Привет, как дела? Это русское предложение.", "Russian"),
        ("안녕하세요, 어떻게 지내세요? 이것은 한국어 문장입니다.", "Korean"),
        ("مرحبا، كيف حالك؟ هذه جملة عربية.", "Arabic"),
        ("Olá, como você está? Esta é uma frase em português.", "Portuguese"),
        ("Hej, hur mår du? Det här är en svensk mening.", "Swedish"),
    ]
    
    print("\n" + "="*80)
    print("TESTING UNSUPPORTED LANGUAGES (Should be marked as 'Unsupported')")
    print("="*80)
    
    for i, (sentence, original_lang) in enumerate(unsupported_sentences, 1):
        lang_code, lang_name, confidence = detector.detect_language(sentence)
        
        # Check if marked as unsupported
        status = "✓" if "unsupported" in lang_name.lower() else "✗"
        
        print(f"{i:2d}. [{status}] Original: {original_lang} | Detected: {lang_name} (confidence: {confidence:.2f})")
        print(f"    Text: {sentence}")
        print("-" * 80)

def test_mixed_content_cleaning():
    """Test the non-alphabetic character removal with the 5 languages."""
    detector = LanguageDetector()
    
    mixed_content = [
        ("Hello123World!@# This456is789English!!!", "English"),
        ("Bonjour, ça va bien! €50 coûte cher...", "French"),
        ("¡Hola! Tel: +34-123-456-789 ¿Cómo estás?", "Spanish"),
        ("Guten Tag! Email: <EMAIL> Website: www.test.de", "German"),
        ("你好！价格：¥99.99 时间：14:30", "Chinese"),
        ("Email: <EMAIL> & Price: $99.99", "English"),
        ("Date: 2024-01-15, Bonjour le monde!", "French"),
    ]
    
    print("\n" + "="*80)
    print("TESTING MIXED CONTENT WITH NON-ALPHA REMOVAL")
    print("="*80)
    
    for i, (sentence, expected) in enumerate(mixed_content, 1):
        # Show cleaned version
        cleaned = ''.join(char if char.isalpha() or char.isspace() else ' ' for char in sentence)
        cleaned = ' '.join(cleaned.split())
        
        lang_code, lang_name, confidence = detector.detect_language(sentence)
        
        # Check if detection matches expected
        status = "✓" if expected.lower() in lang_name.lower() else "✗"
        
        print(f"{i:2d}. [{status}] Expected: {expected} | Detected: {lang_name} (confidence: {confidence:.2f})")
        print(f"    Original: {sentence}")
        print(f"    Cleaned:  {cleaned}")
        print("-" * 80)

def test_full_file_analysis():
    """Test the full file analysis with the 5-language detector."""
    print("\n" + "="*80)
    print("FULL FILE ANALYSIS - 5 LANGUAGES ONLY")
    print("="*80)
    
    # Create test file
    test_file = create_five_language_test_file()
    
    # Initialize detector
    detector = LanguageDetector()
    
    # Analyze the file
    results = detector.analyze_file(test_file)
    
    # Print results
    detector.print_results(results, max_text_length=60)
    
    # Show summary of supported vs unsupported
    supported_count = 0
    unsupported_count = 0
    
    for _, _, _, lang_name, _ in results:
        if "unsupported" in lang_name.lower():
            unsupported_count += 1
        elif lang_name in ["English", "French", "Spanish", "German", "Chinese"]:
            supported_count += 1
    
    print(f"\nDETECTION SUMMARY:")
    print(f"Supported languages detected: {supported_count} lines")
    print(f"Unsupported languages detected: {unsupported_count} lines")
    print(f"Other (empty, error, etc.): {len(results) - supported_count - unsupported_count} lines")

if __name__ == "__main__":
    print("5-Language Detection Testing")
    print("Supported: English, French, Spanish, German, Chinese")
    print("="*80)
    
    # Run tests
    test_supported_languages()
    test_unsupported_languages()
    test_mixed_content_cleaning()
    test_full_file_analysis()
    
    print("\n" + "="*80)
    print("Testing completed!")
    print("="*80)
    print("\nKey features:")
    print("✓ Only detects 5 specified languages")
    print("✓ Marks other languages as 'Unsupported'")
    print("✓ Removes non-alphabetic characters before detection")
    print("✓ Provides confidence scores for all detections")

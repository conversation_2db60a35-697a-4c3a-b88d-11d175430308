import os
from multiprocessing import freeze_support
import concurrent.futures
import fitz
import re
import uuid
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from difflib import SequenceMatcher

def clean_text(text):
    # Remove newlines and spaces for comparison
    return re.sub(r'[\n\s]+', '', text)

def text_similarity(text1, text2):

    # Calculate similarity ratio between two texts
    return SequenceMatcher(None, clean_text(text1), clean_text(text2)).ratio()

def find_all_similar_blocks(target_block, block_list, threshold=0.5):
    similar_blocks = []
    target_text = target_block[4]
    
    for block in block_list:
        similarity = text_similarity(target_text, block[4])
        if similarity > threshold:
            similar_blocks.append((block, similarity))
    
    return similar_blocks

def group_similar_blocks(blocks_list):
    groups = []
    used_blocks = set()
    
    for i, block1 in enumerate(blocks_list):
        if tuple(block1) in used_blocks:
            continue
            
        current_group = [block1]
        used_blocks.add(tuple(block1))
        
        for block2 in blocks_list[i+1:]:
            if tuple(block2) not in used_blocks and clean_text(block1[4]) == clean_text(block2[4]):
                current_group.append(block2)
                used_blocks.add(tuple(block2))
        
        if current_group:
            groups.append(current_group)
    
    return groups

def combine_pdfs(doc_1, doc_2):
    # Generate random filename
    random_name = f"combined_{str(uuid.uuid4())[:8]}.pdf"
    
    # Create new PDF for combined output
    combined_doc = fitz.open()
    
    # Get max pages from both docs
    max_pages = max(len(doc_1), len(doc_2))
    
    # Add pages alternately
    for i in range(max_pages):
        if i < len(doc_1):
            combined_doc.insert_pdf(doc_1, from_page=i, to_page=i)
        if i < len(doc_2):
            combined_doc.insert_pdf(doc_2, from_page=i, to_page=i)
    
    return combined_doc, random_name

def main(links):
    try:
        # Create combined_pdfs directory if it doesn't exist
        combined_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'combined_pdfs')
        os.makedirs(combined_dir, exist_ok=True)
        
        link_1 = links.split("\t")[0]
        link_2 = links.split("\t")[-1].strip()
        doc_1 = fitz.open(link_1)
        doc_2 = fitz.open(link_2)
        blocks_list_1 = []
        blocks_list_2 = []
        
        # Initialize counters for both position and text-based matching
        position_stats = {
            'matched': 0,
            'added': 0,    
            'deleted': 0,  
            'edited': 0    
        }
        
        text_stats = {
            'matched': 0,
            'added': 0,    
            'deleted': 0,  
            'edited': 0    
        }
    except Exception as e:
        return str(e)

    try:
        # Extract blocks from both documents
        for page_number, page in enumerate(doc_1):
            for block in enumerate(page.get_text("blocks")):   
                try:
                    indices = [round(x) for x in block[1][0:4]]
                    indices.append(block[1][4])  # Add text
                    indices.append(page_number)
                    blocks_list_1.append(indices)
                except:
                    pass

        for page_number, page in enumerate(doc_2):
            for block in enumerate(page.get_text("blocks")):   
                try:
                    indices = [round(x) for x in block[1][0:4]]
                    indices.append(block[1][4])  # Add text
                    indices.append(page_number)
                    blocks_list_2.append(indices)
                except:
                    pass

        # Group similar blocks in doc_1
        groups_doc1 = group_similar_blocks(blocks_list_1)
        matched_blocks_2 = set()
        matched_blocks_1 = set()
        
        # Process each group from doc_1
        for group in groups_doc1:
            found_match = False
            matching_blocks_doc2 = []
            
            # Try to find matches in doc_2 for the entire group
            for block2 in blocks_list_2:
                if tuple(block2) not in matched_blocks_2:
                    # Check for identical text (after cleaning)
                    if clean_text(group[0][4]) == clean_text(block2[4]):
                        matching_blocks_doc2.append(block2)
                        found_match = True
            
            if matching_blocks_doc2:
                # For matched blocks, just add to matched sets without drawing rectangles
                for block1 in group:
                    matched_blocks_1.add(tuple(block1))
                    if any(abs(block1[1] - block2[1]) < 50 for block2 in matching_blocks_doc2):
                        position_stats['matched'] += 1
                    text_stats['matched'] += 1

                for block2 in matching_blocks_doc2:
                    matched_blocks_2.add(tuple(block2))
            else:
                # Mark unmatched blocks in red in doc_1
                for block1 in group:
                    doc_1[block1[-1]].draw_rect(block1[0:4], color=(1, 0, 0), width=1)
                    position_stats['deleted'] += 1
                    text_stats['deleted'] += 1

        # Mark remaining unmatched blocks in doc_2 in blue
        for block2 in blocks_list_2:
            if tuple(block2) not in matched_blocks_2:
                doc_2[block2[-1]].draw_rect(block2[0:4], color=(0, 0, 1), width=1)
                if any(abs(block2[1] - block1[1]) < 50 for block1 in blocks_list_1):
                    position_stats['edited'] += 1
                else:
                    position_stats['added'] += 1
                text_stats['added'] += 1

        # Format statistics
        position_code = f"{position_stats['matched']}-{position_stats['added']}-{position_stats['deleted']}-{position_stats['edited']}"
        text_code = f"{text_stats['matched']}-{text_stats['added']}-{text_stats['deleted']}-{text_stats['edited']}"
        
        # Save documents and get paths
        pdf_name_1 = f"name_pdf_1_{str(uuid.uuid4())[:8]}.pdf"
        pdf_name_2 = f"name_pdf_2_{str(uuid.uuid4())[:8]}.pdf"
        doc1_path = os.path.join(combined_dir, pdf_name_1)
        doc2_path = os.path.join(combined_dir, pdf_name_2)
        doc_1.save(doc1_path)
        doc_2.save(doc2_path)
        
        # Combine PDFs and save
        combined_doc, random_name = combine_pdfs(doc_1, doc_2)
        combined_path = os.path.join(combined_dir, random_name)
        combined_doc.save(combined_path)
        combined_doc.close()
        
        # Return all paths and statistics
        return f"{links.strip()}\t{doc1_path}\t{doc2_path}\t{combined_path}\t{position_code}\t{text_code}"


    
    except Exception as E:
        return str(E)



if __name__ == '__main__':

    with open("input.txt", "r") as input_file:
        links_list = input_file.readlines()

    with open("output.txt", "w") as output_file:
        output_file.write("Document\tDoc1_Path\tDoc2_Path\tCombined_Path\tPosition_Stats\tText_Stats")
        output_file.write("\n")

    freeze_support()
    console = Console()
    title = '''# BLOCK MATCHER'''
    my_copyright = '''# © <EMAIL>'''
    title = Markdown(title)
    my_copyright = Markdown(my_copyright)
    console.print(title)
    console.print(my_copyright)
    one_time_count = 500
    total_rows = len(links_list)

    with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
            ncols=100) as progress_bar:

        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    try:
                        with open("output.txt", 'a', encoding='utf8') as of:
                            of.write(result)
                            of.write('\n')
                    except:
                        pass
                    progress_bar.update(1)
        progress_bar.set_description(f"done".upper())


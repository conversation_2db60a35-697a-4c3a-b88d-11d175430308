import pdfplumber
import re
import concurrent.futures
import re
import re
import concurrent.futures
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
from multiprocessing import freeze_support


def find_header_content(link):
  bold_lines_list = []
  doc = pdfplumber.open(link)
  for page in doc.pages:
    for line in page.extract_text_lines():
      sizes_list = []
      char_list = []
      end = 0
      for char in line['chars']:
        if 'bold' in char['fontname'].lower() and char['text'].isalpha():
          start = char['x0']
          if end == 0 or int(start)-int(end)<2:
            char_list.append(char['text'])
          else:
            char_list.append(" "+char['text'])
          end = char['x1']

          sizes_list.append(int(char['y1']) - int(char['y0']))

      if char_list:
        bold_lines_list.append(["".join(char_list), sum(sizes_list)/len(sizes_list)])

  header_list= [header[0].lower() for header in bold_lines_list if header[-1] == 14]
  all_text = "\n".join([page.extract_text_simple().lower() for page in  doc.pages])
  all_text = all_text.split("\n12.  abbreviations")[0]
  all_text = all_text.split("revision history")[0]

  pattern_1 = re.compile(r"\n.*legal disclaimers.*\n", re.IGNORECASE)
  pattern_2 = re.compile(r"\n.*Product data sheet.*\n", re.IGNORECASE)
  all_text = re.sub(pattern_1, "", all_text)
  all_text = re.sub(pattern_2, "", all_text)
  all_text = all_text.replace("\n", " ").strip()
  header_content_dict = {}

  for index, header in enumerate(header_list):
    if index == len(header_list)-1:
      header_content_dict[header] = re.findall(rf"{header_list[index]}.*", all_text)
    else:
      header_content_dict[header] = re.findall(rf"{header_list[index]}.*{header_list[index+1]}", all_text)
      if not header_content_dict[header]:
        header_content_dict[header] = re.findall(rf"{header_list[index]}.*", all_text)

  return header_content_dict


def main(links):
  try:
    link1 = links.split('\t')[0].strip()
    link2 = links.split('\t')[-1].strip()
    header_content_dict_1 = find_header_content(link1)
    header_content_dict_2 = find_header_content(link2)
    _ = []

    for key in header_content_dict_1:
      if header_content_dict_2[key] != header_content_dict_1[key]:
          _.append(key)
        
    for key in header_content_dict_2:
      if header_content_dict_2[key] != header_content_dict_1[key]:
        _.append(key)

    return link1+ "\t" + link2 + "\t"+"|".join(list(set(_)))
  except Exception as E:
     return link1+ "\t" + link2 + "\t"+str(E)
     

if __name__ == '__main__':

    with open("input.txt", "r") as input_file:
        links_list = input_file.readlines()

    with open("output.txt", "w") as output_file:
        output_file.write("Document\tLatest\tstructure")
        output_file.write("\n")

    freeze_support()
    console = Console()
    title = '''# BLOCK MATCHER'''
    my_copyright = '''# © <EMAIL>'''
    title = Markdown(title)
    my_copyright = Markdown(my_copyright)
    console.print(title)
    console.print(my_copyright)
    one_time_count = 500
    total_rows = len(links_list)

    with tqdm(total=total_rows - 1, desc=f"Processing".upper(), unit="row",
            ncols=100) as progress_bar:

        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    try:
                        with open("output.txt", 'a', encoding='utf8') as of:
                            of.write(result)
                            of.write('\n')
                    except:
                        pass
                    progress_bar.update(1)
        progress_bar.set_description(f"done".upper())



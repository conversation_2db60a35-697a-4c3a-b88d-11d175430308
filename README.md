# Outlook Attachment Extractor

This script allows you to extract all attachments from emails in a specific Outlook folder.

## Prerequisites

- Windows operating system
- Microsoft Outlook installed
- Python 3.6 or higher
- `pywin32` package

## Installation

1. Make sure you have Python installed. If not, download and install it from [python.org](https://www.python.org/downloads/).

2. Install the required `pywin32` package:
   ```
   pip install pywin32
   ```

## Usage

1. Open the `outlook_attachment_extractor.py` file in a text editor.

2. By default, the script is configured to extract attachments from a folder named "Shruthi". If you need to change this, modify the `target_folder_name` variable in the script.

3. Run the script:
   ```
   python outlook_attachment_extractor.py
   ```

4. The script will:
   - Connect to your Outlook
   - Search for the specified folder
   - Extract all attachments from emails in that folder
   - Save them to a new folder on your desktop (by default)

5. All attachments will be saved directly in the output folder. If there are duplicate filenames, a timestamp will be added to make them unique.

## Customizing the Save Location

If you want to save attachments to a specific location instead of the desktop, modify the `custom_save_path` variable in the script:

```python
custom_save_path = "C:\\Your\\Desired\\Path"  # Use double backslashes for Windows paths
```

## Troubleshooting

- **Folder not found**: Make sure the folder name is spelled correctly and exists in your Outlook.
- **Permission errors**: Ensure you have write permissions to the save location.
- **Outlook security prompts**: You may need to allow the script to access Outlook when prompted.

## How It Works

The script uses the `win32com` library to interact with Outlook through its COM interface. It:

1. Searches for the specified folder in your Outlook
2. Iterates through all emails in that folder
3. Extracts any attachments found
4. Saves them directly to the specified location

## License

This script is provided as-is for personal use.

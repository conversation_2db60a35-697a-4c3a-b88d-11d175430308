#!/usr/bin/env python3
"""
Example usage of the Language Detection Script
This demonstrates how to use the LanguageDetector class programmatically.
"""

from language_detector import LanguageDetector

def create_sample_text_file():
    """Create a sample text file with multiple languages for testing."""
    sample_text = """Hello, how are you today?
<PERSON><PERSON><PERSON>, comment allez-vous?
<PERSON><PERSON>, ¿cómo estás?
Guten Tag, wie geht es Ihnen?
Ciao, come stai?
Привет, как дела?
こんにちは、元気ですか？
안녕하세요, 어떻게 지내세요?
你好，你好吗？
مرحبا، كيف حالك؟
Olá, como você está?
Hej, hur mår du?
<PERSON><PERSON>, hoe gaat het?
<PERSON><PERSON><PERSON><PERSON>, jak się masz?
Aho<PERSON>, jak se máš?
123456789
This is another English sentence.
Esta es otra oración en español.
C'est une autre phrase en français.
Das ist ein weiterer deutscher Satz.
Questa è un'altra frase italiana.
"""
    
    with open('sample_multilingual.txt', 'w', encoding='utf-8') as f:
        f.write(sample_text)
    
    print("Created sample file: sample_multilingual.txt")

def example_basic_usage():
    """Example of basic usage of the LanguageDetector."""
    print("\n" + "="*60)
    print("EXAMPLE: Basic Usage")
    print("="*60)
    
    # Create sample file
    create_sample_text_file()
    
    # Initialize detector
    detector = LanguageDetector()
    
    # Analyze the file
    results = detector.analyze_file('sample_multilingual.txt')
    
    # Print results
    detector.print_results(results)

def example_programmatic_usage():
    """Example of using the detector programmatically."""
    print("\n" + "="*60)
    print("EXAMPLE: Programmatic Usage")
    print("="*60)
    
    detector = LanguageDetector()
    
    # Test individual sentences
    test_sentences = [
        "Hello, this is an English sentence.",
        "Bonjour, ceci est une phrase française.",
        "Hola, esta es una oración en español.",
        "Guten Tag, das ist ein deutscher Satz.",
        "Ciao, questa è una frase italiana.",
        "Привет, это русское предложение.",
        "こんにちは、これは日本語の文です。",
        "안녕하세요, 이것은 한국어 문장입니다.",
        "你好，这是一个中文句子。",
        "مرحبا، هذه جملة عربية."
    ]
    
    print("Individual sentence detection:")
    print("-" * 40)
    
    for i, sentence in enumerate(test_sentences, 1):
        lang_code, lang_name, confidence = detector.detect_language(sentence)
        print(f"{i:2d}. {lang_name} (confidence: {confidence:.2f})")
        print(f"    Text: {sentence}")
        print()

def example_with_custom_settings():
    """Example with custom confidence threshold and output file."""
    print("\n" + "="*60)
    print("EXAMPLE: Custom Settings")
    print("="*60)
    
    # Initialize detector with custom confidence threshold
    detector = LanguageDetector(confidence_threshold=0.8)
    
    # Analyze the sample file
    results = detector.analyze_file('sample_multilingual.txt')
    
    # Print results without confidence scores and with shorter text length
    print("Results with high confidence threshold (0.8) and no confidence display:")
    detector.print_results(results, show_confidence=False, max_text_length=50)
    
    # Save results to file
    detector.save_results_to_file(results, 'language_detection_results.txt')

def example_error_handling():
    """Example of error handling."""
    print("\n" + "="*60)
    print("EXAMPLE: Error Handling")
    print("="*60)
    
    detector = LanguageDetector()
    
    # Test with non-existent file
    print("Testing with non-existent file:")
    results = detector.analyze_file('non_existent_file.txt')
    print(f"Results: {len(results)} lines processed")
    
    # Test with problematic text
    print("\nTesting with problematic text:")
    problematic_texts = [
        "",  # Empty string
        "123",  # Only numbers
        "!!!",  # Only punctuation
        "a",  # Very short text
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit."  # Latin (might be detected as another language)
    ]
    
    for text in problematic_texts:
        lang_code, lang_name, confidence = detector.detect_language(text)
        print(f"Text: '{text}' -> {lang_name} (confidence: {confidence:.2f})")

if __name__ == "__main__":
    print("Language Detection Examples")
    print("="*60)
    
    # Run examples
    example_basic_usage()
    example_programmatic_usage()
    example_with_custom_settings()
    example_error_handling()
    
    print("\n" + "="*60)
    print("Examples completed!")
    print("="*60)

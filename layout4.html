<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Sunset View - Layout 4</title>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<link rel="stylesheet" href="shared-styles.css">
	<link rel="stylesheet" href="layout4.css">
</head>
<body>
	<div class="container">
		<aside class="sidebar">
			<button class="sidebar-toggle">
				<i class="fas fa-chevron-left"></i>
			</button>
			<div class="logo">
				<i class="fas fa-sun"></i>
				<span>Sunset View</span>
			</div>
			<nav>
				<a href="#" class="nav-item active">
					<i class="fas fa-fire"></i>
					<span>Dashboard</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-chart-line"></i>
					<span>Analytics</span>
				</a>
				<a href="#" class="nav-item">
					<i class="fas fa-clock"></i>
					<span>Timeline</span>
				</a>
			</nav>
		</aside>

		<main class="main-content">
			<div class="columns">
				<section class="column">
					<h2>Performance</h2>
					<div class="card animate-fade-in">
						<h3>CPU Usage</h3>
						<p>32% utilization</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Memory</h3>
						<p>4.2GB available</p>
					</div>
				</section>

				<section class="column">
					<h2>Activity</h2>
					<div class="card animate-fade-in">
						<h3>Users Online</h3>
						<p>1,234 active now</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Response Time</h3>
						<p>124ms average</p>
					</div>
				</section>

				<section class="column">
					<h2>System</h2>
					<div class="card animate-fade-in">
						<h3>Updates</h3>
						<p>All systems up to date</p>
					</div>
					<div class="card animate-fade-in">
						<h3>Security</h3>
						<p>No threats detected</p>
					</div>
				</section>
			</div>
		</main>
	</div>

	<script>
		document.querySelector('.sidebar-toggle').addEventListener('click', () => {
			document.querySelector('.sidebar').classList.toggle('collapsed');
			document.querySelector('.main-content').classList.toggle('expanded');
		});
	</script>
</body>
</html>
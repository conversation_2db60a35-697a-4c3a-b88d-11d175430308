import os
import win32com.client
import pandas as pd
import glob
from datetime import datetime

def get_outlook_folder(folder_name):
    """
    Get a specific Outlook folder by name.
    
    Args:
        folder_name (str): Name of the folder to find
        
    Returns:
        Folder object or None if not found
    """
    outlook = win32com.client.Dispatch("Outlook.Application").GetNamespace("MAPI")
    
    # First try to find the folder in the main folders
    for folder in outlook.Folders:
        if folder.Name.lower() == folder_name.lower():
            return folder
        
        # If not found at the top level, check in Inbox subfolders
        inbox = outlook.GetDefaultFolder(6)  # 6 is the index for Inbox
        for subfolder in inbox.Folders:
            if subfolder.Name.lower() == folder_name.lower():
                return subfolder
    
    # If still not found, search all folders recursively
    return search_folders_recursively(outlook.Folders, folder_name)

def search_folders_recursively(folders, target_folder_name):
    """
    Search for a folder recursively through all Outlook folders.
    
    Args:
        folders: Collection of Outlook folders to search
        target_folder_name (str): Name of the folder to find
        
    Returns:
        Folder object or None if not found
    """
    for folder in folders:
        # Check if current folder matches
        if folder.Name.lower() == target_folder_name.lower():
            return folder
        
        # Check subfolders if any
        if folder.Folders.Count > 0:
            subfolder_match = search_folders_recursively(folder.Folders, target_folder_name)
            if subfolder_match:
                return subfolder_match
    
    return None

def save_attachments(folder_name, save_path=None):
    """
    Save all attachments from emails in the specified Outlook folder.
    
    Args:
        folder_name (str): Name of the Outlook folder containing emails
        save_path (str): Path where attachments will be saved. If None, creates a folder on desktop.
        
    Returns:
        tuple: (save_path, attachment_count)
    """
    # Create save directory if it doesn't exist
    if save_path is None:
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(desktop, f"Outlook_Attachments_{timestamp}")
    
    if not os.path.exists(save_path):
        os.makedirs(save_path)
        print(f"Created directory: {save_path}")
    
    # Get the specified folder
    target_folder = get_outlook_folder(folder_name)
    
    if not target_folder:
        print(f"Folder '{folder_name}' not found in Outlook.")
        return save_path, 0
    
    print(f"Found folder: {target_folder.Name}")
    print(f"Number of items in folder: {target_folder.Items.Count}")
    
    # Process all emails in the folder
    attachment_count = 0
    email_count = 0
    
    for item in target_folder.Items:
        email_count += 1
        
        # Check if the item has attachments
        if item.Attachments.Count > 0:
            # Save each attachment directly to the outer folder
            for attachment in item.Attachments:
                # Handle duplicate filenames by adding a timestamp if needed
                base_filename = attachment.FileName
                file_path = os.path.join(save_path, base_filename)
                
                # If file already exists, add a timestamp to make it unique
                if os.path.exists(file_path):
                    file_name, file_ext = os.path.splitext(base_filename)
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    new_filename = f"{file_name}_{timestamp}{file_ext}"
                    file_path = os.path.join(save_path, new_filename)
                
                try:
                    attachment.SaveAsFile(file_path)
                    print(f"Saved: {file_path}")
                    attachment_count += 1
                except Exception as e:
                    print(f"Error saving {attachment.FileName}: {str(e)}")
    
    print(f"\nSummary:")
    print(f"Processed {email_count} emails")
    print(f"Saved {attachment_count} attachments to {save_path}")
    
    return save_path, attachment_count

def combine_excel_files(input_folder, output_file=None):
    """
    Read all Excel files in the input folder, extract specified columns,
    and combine them into a single output file.
    
    Args:
        input_folder (str): Path to the folder containing Excel files
        output_file (str, optional): Path for the output Excel file. If None, creates a file on desktop.
        
    Returns:
        str: Path to the output file
    """
    # Create output file path if not provided
    if output_file is None:
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(desktop, f"Combined_Excel_{timestamp}.xlsx")
    
    # Columns to extract
    columns_to_extract = [
        'DOCUMENT', 'LATEST', 'COMPARE_STATUS', 'V_COMMENT', 
        'PART_STATUS', 'DATA_CHANGED', 'CHANGED_FET'
    ]
    
    # Find all Excel files in the input folder
    excel_files = glob.glob(os.path.join(input_folder, "*.xlsx"))
    
    if not excel_files:
        print(f"No Excel files found in {input_folder}")
        return None
    
    print(f"Found {len(excel_files)} Excel files in {input_folder}")
    
    # List to store dataframes from each file
    all_dfs = []
    
    # Process each Excel file
    for file_path in excel_files:
        file_name = os.path.basename(file_path)
        try:
            # Read the Excel file
            df = pd.read_excel(file_path)
            
            # Check if all required columns exist
            missing_columns = [col for col in columns_to_extract if col not in df.columns]
            
            if missing_columns:
                print(f"Warning: File {file_name} is missing columns: {', '.join(missing_columns)}")
                # Create missing columns with NaN values
                for col in missing_columns:
                    df[col] = pd.NA
            
            # Extract only the required columns that exist in the dataframe
            available_columns = [col for col in columns_to_extract if col in df.columns]
            if available_columns:
                df_extracted = df[available_columns].copy()
                
                # Add missing columns as empty
                for col in missing_columns:
                    df_extracted[col] = pd.NA
                
                # Add source file information
                df_extracted['SOURCE_FILE'] = file_name
                
                # Append to the list of dataframes
                all_dfs.append(df_extracted)
                print(f"Processed: {file_name} - {len(df_extracted)} rows")
            else:
                print(f"Warning: File {file_name} doesn't contain any of the required columns")
            
        except Exception as e:
            print(f"Error processing {file_name}: {str(e)}")
    
    if not all_dfs:
        print("No data could be extracted from the Excel files.")
        return None
    
    # Combine all dataframes
    combined_df = pd.concat(all_dfs, ignore_index=True)
    
    # Save the combined dataframe to an Excel file
    combined_df.to_excel(output_file, index=False)
    
    print(f"\nCombined data saved to: {output_file}")
    print(f"Total rows: {len(combined_df)}")
    
    return output_file

def main():
    """
    Main function to run the script.
    """
    # Folder name to search for in Outlook
    target_folder_name = "shruthi"
    
    # Step 1: Extract attachments from Outlook
    print("=== STEP 1: Extracting attachments from Outlook ===")
    attachments_folder, attachment_count = save_attachments(target_folder_name)
    
    if attachment_count == 0:
        print("No attachments were found. Exiting.")
        return
    
    # Step 2: Process Excel files and combine data
    print("\n=== STEP 2: Processing Excel files and combining data ===")
    output_file = combine_excel_files(attachments_folder)
    
    if output_file:
        print("\nProcess completed successfully!")
        print(f"Combined data saved to: {output_file}")
    else:
        print("\nProcess completed, but no data was combined.")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Neo-Brutalism Style</title>
	<style>
		:root {
			--primary: #ff4d00;
			--secondary: #000000;
			--accent: #00ff00;
			--bg: #ffffff;
		}

		body {
			margin: 0;
			padding: 40px;
			background: var(--bg);
			font-family: Helvetica, Arial, sans-serif;
			color: var(--secondary);
		}

		.container {
			max-width: 1200px;
			margin: 0 auto;
		}

		.header {
			background: var(--primary);
			padding: 30px;
			margin-bottom: 40px;
			transform: rotate(-1deg);
			box-shadow: 8px 8px 0 var(--secondary);
			border: 3px solid var(--secondary);
		}

		.header h1 {
			font-size: 64px;
			margin: 0;
			transform: rotate(1deg);
			text-transform: uppercase;
		}

		.grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 40px;
		}

		.card {
			background: #f0f0f0;
			padding: 30px;
			border: 3px solid var(--secondary);
			box-shadow: 12px 12px 0 var(--secondary);
			transform: rotate(1deg);
			transition: transform 0.2s ease;
		}

		.card:hover {
			transform: rotate(-1deg) translateY(-5px);
		}

		.card h2 {
			font-size: 32px;
			margin-top: 0;
			color: var(--primary);
			text-transform: uppercase;
		}

		.button {
			background: var(--accent);
			border: 3px solid var(--secondary);
			padding: 15px 30px;
			font-size: 20px;
			font-weight: bold;
			cursor: pointer;
			box-shadow: 5px 5px 0 var(--secondary);
			transform: rotate(-1deg);
			transition: all 0.2s ease;
		}

		.button:hover {
			transform: rotate(1deg) translateY(-2px);
			box-shadow: 7px 7px 0 var(--secondary);
		}

		.button:active {
			transform: translateY(2px);
			box-shadow: 3px 3px 0 var(--secondary);
		}

		.image-block {
			width: 100%;
			height: 200px;
			background: #ddd;
			border: 3px solid var(--secondary);
			margin-bottom: 20px;
			position: relative;
		}

		.image-block::after {
			content: 'IMAGE';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-weight: bold;
			font-size: 24px;
		}

		.tag {
			display: inline-block;
			background: var(--primary);
			color: white;
			padding: 5px 15px;
			margin: 5px;
			transform: rotate(-2deg);
			border: 2px solid var(--secondary);
		}
	</style>
</head>
<body>
	<div class="container">
		<header class="header">
			<h1>Neo-Brutalism</h1>
		</header>

		<div class="grid">
			<div class="card">
				<div class="image-block"></div>
				<h2>Raw Design</h2>
				<p>Embracing imperfection and raw aesthetics in digital form.</p>
				<span class="tag">#brutal</span>
				<span class="tag">#raw</span>
				<button class="button">EXPLORE</button>
			</div>

			<div class="card">
				<div class="image-block"></div>
				<h2>Bold Choices</h2>
				<p>High contrast, hard shadows, and unapologetic design decisions.</p>
				<span class="tag">#bold</span>
				<span class="tag">#contrast</span>
				<button class="button">DISCOVER</button>
			</div>
		</div>
	</div>
</body>
</html>
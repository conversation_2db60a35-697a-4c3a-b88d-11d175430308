import pandas as pd
import os
import re

input_file = 'input.txt'
output_file = 'output.txt'

if not os.path.isfile(input_file):
    print(f"File '{input_file}' not found in the current directory.")
    exit(1)

# Load the tab-separated text file
df = pd.read_csv(input_file, sep='\t', dtype=str, on_bad_lines='skip', encoding='cp1252').fillna('')

# Function to check for non-alpha content
def has_nonalpha(cell):
    return any(not part.replace(' ', '').isalpha() for part in cell.split('|') if part.strip())

# Function to check if cell is short
def is_short(cell):
    return len(cell) < 4

# Function to clean each part: keep content before last comma
def clean_cell(cell):
    parts = cell.split('|')
    cleaned_parts = []
    for part in parts:
        # If the segment contains 'order no.' in any form, drop the whole part
        if 'order no.' in part.lower().strip():
            cleaned_parts.append('')  # empty between pipes
        else:
            cleaned_parts.append(part.strip())
    
    cleaned_parts.sort()
    
    x = '|'.join(list(set(cleaned_parts))).removeprefix("|")
    xc = re.sub(r"[^a-zA-Z|\s]", "", x).strip()

    return xc



# Flags before cleaning
df['Has_NonAlpha_Before'] = df['NOT_FOUND_LINES'].apply(has_nonalpha)
df['Is_Short_Before'] = df['NOT_FOUND_LINES'].apply(is_short)

# Apply cleaning
df['Cleaned_NOT_FOUND_LINES'] = df['NOT_FOUND_LINES'].apply(clean_cell)

# Flags after cleaning
df['Has_NonAlpha_After'] = df['Cleaned_NOT_FOUND_LINES'].apply(has_nonalpha)
df['Is_Short_After'] = df['Cleaned_NOT_FOUND_LINES'].apply(is_short)

# Save result
df.to_csv(output_file, sep='\t', index=False)
print(f"Processing complete. Output saved to '{output_file}'.")
